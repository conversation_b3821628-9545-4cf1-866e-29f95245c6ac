import React from 'react';
import { DifficultyLevel } from '@/types/game';
import { DIFFICULTY_CONFIG } from '@/utils/constants';

interface DifficultySelectorProps {
  selectedDifficulty: DifficultyLevel;
  onDifficultyChange: (difficulty: DifficultyLevel) => void;
  disabled?: boolean;
  className?: string;
}

export default function DifficultySelector({
  selectedDifficulty,
  onDifficultyChange,
  disabled = false,
  className = ''
}: DifficultySelectorProps) {
  
  const difficulties: Array<{
    level: DifficultyLevel;
    name: string;
    description: string;
    features: string[];
    color: string;
    bgColor: string;
    borderColor: string;
  }> = [
    {
      level: 'easy',
      name: 'Easy',
      description: 'Learn the game mechanics',
      features: [
        '30 steps maximum',
        'Unlimited common word reuse',
        'Fixed word countdown (10→9→8...)',
        'Gentle difficulty curve'
      ],
      color: '#059669',
      bgColor: '#F0FDF4',
      borderColor: '#BBF7D0'
    },
    {
      level: 'medium',
      name: 'Medium',
      description: 'Balanced challenge',
      features: [
        '25 steps maximum',
        '5 uses per common word',
        'Must be shorter than previous',
        'Strategic word management'
      ],
      color: '#D97706',
      bgColor: '#FFFBEB',
      borderColor: '#FDE68A'
    },
    {
      level: 'hard',
      name: 'Hard',
      description: 'Expert-level challenge',
      features: [
        '20 steps maximum',
        'No common word reuse',
        'Aggressive word reduction',
        'Maximum difficulty'
      ],
      color: '#DC2626',
      bgColor: '#FEF2F2',
      borderColor: '#FECACA'
    }
  ];

  return (
    <div className={`difficulty-selector ${className}`}>
      <h3 className="selector-title">Choose Difficulty</h3>
      <div className="difficulty-grid">
        {difficulties.map(({ level, name, description, features, color, bgColor, borderColor }) => {
          const isSelected = selectedDifficulty === level;
          const config = DIFFICULTY_CONFIG[level];
          
          return (
            <button
              key={level}
              className={`difficulty-option ${isSelected ? 'selected' : ''}`}
              onClick={() => onDifficultyChange(level)}
              disabled={disabled}
              style={{
                borderColor: isSelected ? color : borderColor,
                backgroundColor: isSelected ? bgColor : 'white'
              }}
            >
              <div className="option-header">
                <h4 className="option-name" style={{ color }}>
                  {name}
                </h4>
                <div className="option-stats">
                  <span className="stat">
                    {config.maxSteps} steps
                  </span>
                </div>
              </div>
              
              <p className="option-description">
                {description}
              </p>
              
              <ul className="option-features">
                {features.map((feature, index) => (
                  <li key={index} className="feature-item">
                    {feature}
                  </li>
                ))}
              </ul>
              
              {isSelected && (
                <div className="selected-indicator">
                  <span className="checkmark">✓</span>
                  Selected
                </div>
              )}
            </button>
          );
        })}
      </div>

      <div className="difficulty-explanation">
        <h4 className="explanation-title">How Difficulty Works</h4>
        <div className="explanation-grid">
          <div className="explanation-item">
            <strong>Word Count Rules:</strong>
            <ul>
              <li><strong>Easy:</strong> Fixed countdown (10→9→8...)</li>
              <li><strong>Medium:</strong> Must be shorter than previous</li>
              <li><strong>Hard:</strong> Aggressive reduction after turn 5</li>
            </ul>
          </div>
          <div className="explanation-item">
            <strong>Common Words:</strong>
            <ul>
              <li><strong>Easy:</strong> Unlimited reuse of "the", "a", "of", etc.</li>
              <li><strong>Medium:</strong> 5 uses each for common words</li>
              <li><strong>Hard:</strong> No word reuse at all</li>
            </ul>
          </div>
        </div>
      </div>

      <style jsx>{`
        .difficulty-selector {
          font-family: 'Inter', sans-serif;
        }

        .selector-title {
          font-size: 20px;
          font-weight: var(--font-medium);
          color: #111827;
          margin: 0 0 16px 0;
          text-align: center;
        }

        .difficulty-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 16px;
          margin-bottom: 24px;
        }

        .difficulty-option {
          background: white;
          border: 2px solid #E5E7EB;
          border-radius: 12px;
          padding: 20px;
          text-align: left;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
        }

        .difficulty-option:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .difficulty-option:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .difficulty-option.selected {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .option-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }

        .option-name {
          font-size: 18px;
          font-weight: var(--font-medium);
          margin: 0;
        }

        .option-stats {
          display: flex;
          gap: 8px;
        }

        .stat {
          background: rgba(0, 0, 0, 0.05);
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: var(--font-normal);
          color: #6B7280;
        }

        .option-description {
          color: #6B7280;
          font-size: 14px;
          margin: 0 0 12px 0;
        }

        .option-features {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .feature-item {
          font-size: 13px;
          color: #374151;
          margin-bottom: 4px;
          padding-left: 16px;
          position: relative;
        }

        .feature-item::before {
          content: '•';
          position: absolute;
          left: 0;
          color: #9CA3AF;
        }

        .selected-indicator {
          position: absolute;
          top: 12px;
          right: 12px;
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: var(--font-medium);
          color: #059669;
        }

        .checkmark {
          background: #059669;
          color: white;
          border-radius: 50%;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
        }

        .difficulty-explanation {
          background: #F9FAFB;
          border: 1px solid #E5E7EB;
          border-radius: 8px;
          padding: 16px;
        }

        .explanation-title {
          font-size: 16px;
          font-weight: var(--font-medium);
          color: #111827;
          margin: 0 0 12px 0;
        }

        .explanation-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 16px;
        }

        .explanation-item {
          font-size: 13px;
        }

        .explanation-item strong {
          color: #374151;
          display: block;
          margin-bottom: 6px;
        }

        .explanation-item ul {
          margin: 0;
          padding-left: 16px;
          color: #6B7280;
        }

        .explanation-item li {
          margin-bottom: 2px;
        }
      `}</style>
    </div>
  );
}

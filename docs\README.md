# DEFEATER.AI MVP

> "The Dark Souls of puzzle games" - Where confidence goes to die, and reasoning is reborn.

## What is DEFEATER.AI?

DEFEATER.AI is a reasoning puzzle platform where humans challenge state-of-the-art AI models in pure intellectual combat. Starting with our foundation game "Definition Decay", players must navigate from one concept to another through increasingly constrained definitions.

### The Game: Definition Decay

**Rules:**
1. Define a word given by the AI
2. Each subsequent definition must be shorter by at least one word
3. Cannot reuse any word from previous definitions
4. No circular definitions
5. Must eventually reach one of the AI's target words
6. AI can "burn" target words at strategic moments

**Example:**
```
AI: "Define 'revolution' - targets: 'circle', 'zero', 'music'"
Player: "A fundamental change in power structures" (7 words)
AI: "Define 'fundamental'"
Player: "Essential to the nature" (4 words)
[Eventually must reach one target word through logical chain]
```

## MVP Features

- **Zero Friction**: Beautiful, simple interface - start playing immediately
- **Pure Reasoning**: No trivia, no cultural knowledge, just logic
- **Addictive Difficulty**: Brutally fair challenges that make you want "just one more try"
- **Local Testing**: Internal MVP for friends and family testing

## Tech Stack

- **Frontend**: Next.js with TypeScript
- **Styling**: Tailwind CSS with custom DEFEATER color palette
- **AI**: DeepSeek R1 API for reasoning and semantic analysis
- **State Management**: React hooks (no database for MVP)
- **Deployment**: Local development server

## Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- DeepSeek API key

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Aladin147/DefeaterAI.git
cd DefeaterAI
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
# Add your DeepSeek API key to .env.local
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

### Local Network Testing

To test with friends/family on your local network:

1. Find your local IP address:
```bash
# On Windows
ipconfig
# On Mac/Linux  
ifconfig
```

2. Share the URL with your local IP:
```
http://192.168.1.XXX:3000
```

## Project Structure

```
defeater-ai/
├── components/          # React components
│   ├── GameBoard.tsx   # Main game interface
│   ├── GameState.tsx   # Game state display
│   └── ui/             # Reusable UI components
├── pages/
│   ├── index.tsx       # Main game page
│   └── api/
│       └── game.ts     # DeepSeek API wrapper
├── utils/
│   ├── gameLogic.ts    # Game rules and validation
│   └── deepseek.ts     # API utilities
├── styles/
│   └── globals.css     # Global styles and animations
└── types/
    └── game.ts         # TypeScript definitions
```

## Color Palette

- **Cream Background**: `#FFF8E7` - Warm, inviting base
- **Coral CTAs**: `#FF6B6B` - Energetic action buttons  
- **Electric Blue Success**: `#4ECDC4` - Satisfying victories
- **Deep Purple UI**: `#7B68EE` - Elegant interface elements
- **Gentle Pink Failure**: `#FFE0EC` - Soft defeat feedback

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks

### Environment Variables

Create a `.env.local` file with:

```
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_API_URL=https://api.deepseek.com/v1
```

## Contributing

This is an internal MVP for testing. See [ROADMAP.md](./ROADMAP.md) for development progress and planned features.

## License

Private repository - All rights reserved.

---

*Built with ❤️ for the future of reasoning entertainment*

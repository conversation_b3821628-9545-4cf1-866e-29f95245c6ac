#!/usr/bin/env node

/**
 * Performance Analysis Script
 * 
 * Analyzes performance impact of UX restoration including bundle size,
 * CSS optimization, component complexity, and rendering performance
 */

const fs = require('fs');
const path = require('path');

class PerformanceAnalyzer {
  constructor() {
    this.results = {
      bundleAnalysis: {},
      cssOptimization: {},
      componentComplexity: {},
      renderingPerformance: {},
      memoryUsage: {},
      recommendations: [],
      summary: {}
    };
  }

  async analyzeAll() {
    console.log('🚀 Starting Performance Analysis...\n');
    
    await this.analyzeBundleSize();
    await this.analyzeCSSOptimization();
    await this.analyzeComponentComplexity();
    await this.analyzeRenderingPerformance();
    await this.analyzeMemoryUsage();
    
    this.generateRecommendations();
    this.generateSummary();
    this.printResults();
    
    return this.results;
  }

  async analyzeBundleSize() {
    console.log('📦 Analyzing Bundle Size...');
    
    const buildDir = '.next';
    const staticDir = path.join(buildDir, 'static');
    
    if (!fs.existsSync(buildDir)) {
      console.log('   ⚠️  Build directory not found. Run npm run build first.');
      return;
    }
    
    const bundleStats = {
      totalSize: 0,
      jsFiles: [],
      cssFiles: [],
      chunks: []
    };
    
    // Analyze JavaScript bundles
    if (fs.existsSync(staticDir)) {
      this.walkDirectory(staticDir, (filePath, stats) => {
        const relativePath = path.relative(staticDir, filePath);
        const size = stats.size;
        bundleStats.totalSize += size;
        
        if (filePath.endsWith('.js')) {
          bundleStats.jsFiles.push({
            path: relativePath,
            size: size,
            sizeKB: Math.round(size / 1024)
          });
        } else if (filePath.endsWith('.css')) {
          bundleStats.cssFiles.push({
            path: relativePath,
            size: size,
            sizeKB: Math.round(size / 1024)
          });
        }
      });
    }
    
    // Sort by size
    bundleStats.jsFiles.sort((a, b) => b.size - a.size);
    bundleStats.cssFiles.sort((a, b) => b.size - a.size);
    
    this.results.bundleAnalysis = bundleStats;
    
    console.log(`   ✅ Bundle analysis complete`);
  }

  async analyzeCSSOptimization() {
    console.log('🎨 Analyzing CSS Optimization...');
    
    const cssFiles = this.getAllFiles(['css', 'module.css']);
    const cssStats = {
      totalFiles: cssFiles.length,
      totalSize: 0,
      customProperties: 0,
      mediaQueries: 0,
      duplicateRules: 0,
      unusedSelectors: 0,
      optimizationOpportunities: []
    };
    
    cssFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const stats = fs.statSync(file);
      cssStats.totalSize += stats.size;
      
      // Count CSS custom properties
      const customProps = content.match(/--[\w-]+:/g) || [];
      cssStats.customProperties += customProps.length;
      
      // Count media queries
      const mediaQueries = content.match(/@media[^{]+{/g) || [];
      cssStats.mediaQueries += mediaQueries.length;
      
      // Check for potential optimizations
      if (content.includes('!important')) {
        cssStats.optimizationOpportunities.push({
          file: path.relative(process.cwd(), file),
          issue: 'Contains !important declarations',
          impact: 'medium'
        });
      }
      
      // Check for large files
      if (stats.size > 50 * 1024) { // 50KB
        cssStats.optimizationOpportunities.push({
          file: path.relative(process.cwd(), file),
          issue: `Large CSS file (${Math.round(stats.size / 1024)}KB)`,
          impact: 'high'
        });
      }
      
      // Check for unused vendor prefixes
      const vendorPrefixes = content.match(/-webkit-|-moz-|-ms-|-o-/g) || [];
      if (vendorPrefixes.length > 10) {
        cssStats.optimizationOpportunities.push({
          file: path.relative(process.cwd(), file),
          issue: `Many vendor prefixes (${vendorPrefixes.length})`,
          impact: 'low'
        });
      }
    });
    
    this.results.cssOptimization = cssStats;
    
    console.log(`   ✅ CSS optimization analysis complete`);
  }

  async analyzeComponentComplexity() {
    console.log('🧩 Analyzing Component Complexity...');
    
    const componentFiles = this.getAllFiles(['tsx', 'ts']);
    const complexityStats = {
      totalComponents: 0,
      averageComplexity: 0,
      complexComponents: [],
      hookUsage: 0,
      stateVariables: 0,
      effectHooks: 0,
      renderComplexity: []
    };
    
    componentFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const fileName = path.relative(process.cwd(), file);
      
      // Skip non-component files
      if (!content.includes('export') || !content.includes('React')) {
        return;
      }
      
      complexityStats.totalComponents++;
      
      const complexity = this.calculateComponentComplexity(content);
      
      if (complexity.score > 20) {
        complexityStats.complexComponents.push({
          file: fileName,
          complexity: complexity.score,
          issues: complexity.issues
        });
      }
      
      complexityStats.hookUsage += complexity.hooks;
      complexityStats.stateVariables += complexity.state;
      complexityStats.effectHooks += complexity.effects;
      
      // Analyze render complexity
      const jsxElements = (content.match(/<[A-Z]\w+/g) || []).length;
      if (jsxElements > 20) {
        complexityStats.renderComplexity.push({
          file: fileName,
          jsxElements,
          issue: 'High JSX element count'
        });
      }
    });
    
    if (complexityStats.totalComponents > 0) {
      complexityStats.averageComplexity = 
        (complexityStats.hookUsage + complexityStats.stateVariables + complexityStats.effectHooks) / 
        complexityStats.totalComponents;
    }
    
    this.results.componentComplexity = complexityStats;
    
    console.log(`   ✅ Component complexity analysis complete`);
  }

  calculateComponentComplexity(content) {
    const hooks = (content.match(/use[A-Z]\w+/g) || []).length;
    const state = (content.match(/useState|useRef|useMemo|useCallback/g) || []).length;
    const effects = (content.match(/useEffect/g) || []).length;
    const conditionals = (content.match(/if\s*\(|&&|\?/g) || []).length;
    const loops = (content.match(/\.map\(|\.filter\(|\.reduce\(|for\s*\(/g) || []).length;
    
    const score = hooks + (state * 2) + (effects * 3) + conditionals + loops;
    
    const issues = [];
    if (state > 5) issues.push('High state usage');
    if (effects > 3) issues.push('Many useEffect hooks');
    if (conditionals > 10) issues.push('Complex conditional logic');
    
    return { score, hooks, state, effects, issues };
  }

  async analyzeRenderingPerformance() {
    console.log('⚡ Analyzing Rendering Performance...');
    
    const componentFiles = this.getAllFiles(['tsx']);
    const renderingStats = {
      memoizedComponents: 0,
      callbackOptimizations: 0,
      memoOptimizations: 0,
      potentialOptimizations: [],
      performancePatterns: []
    };
    
    componentFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const fileName = path.relative(process.cwd(), file);
      
      // Count performance optimizations
      renderingStats.memoizedComponents += (content.match(/React\.memo|memo\(/g) || []).length;
      renderingStats.callbackOptimizations += (content.match(/useCallback/g) || []).length;
      renderingStats.memoOptimizations += (content.match(/useMemo/g) || []).length;
      
      // Check for potential optimizations
      const inlineObjects = content.match(/style={{|className={{/g) || [];
      if (inlineObjects.length > 3) {
        renderingStats.potentialOptimizations.push({
          file: fileName,
          issue: `Inline objects in render (${inlineObjects.length})`,
          recommendation: 'Move objects outside render or use useMemo'
        });
      }
      
      // Check for performance patterns
      if (content.includes('React.memo')) {
        renderingStats.performancePatterns.push({
          file: fileName,
          pattern: 'React.memo usage',
          impact: 'positive'
        });
      }
      
      if (content.includes('useCallback') && content.includes('useEffect')) {
        renderingStats.performancePatterns.push({
          file: fileName,
          pattern: 'Callback optimization with effects',
          impact: 'positive'
        });
      }
    });
    
    this.results.renderingPerformance = renderingStats;
    
    console.log(`   ✅ Rendering performance analysis complete`);
  }

  async analyzeMemoryUsage() {
    console.log('🧠 Analyzing Memory Usage Patterns...');
    
    const files = this.getAllFiles(['tsx', 'ts']);
    const memoryStats = {
      potentialLeaks: [],
      cleanupPatterns: 0,
      eventListeners: 0,
      timers: 0,
      memoryOptimizations: []
    };
    
    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const fileName = path.relative(process.cwd(), file);
      
      // Check for cleanup patterns
      if (content.includes('return () =>') || content.includes('cleanup')) {
        memoryStats.cleanupPatterns++;
      }
      
      // Check for event listeners
      const eventListeners = content.match(/addEventListener|removeEventListener/g) || [];
      memoryStats.eventListeners += eventListeners.length;
      
      // Check for timers
      const timers = content.match(/setTimeout|setInterval|clearTimeout|clearInterval/g) || [];
      memoryStats.timers += timers.length;
      
      // Check for potential memory leaks
      if (content.includes('addEventListener') && !content.includes('removeEventListener')) {
        memoryStats.potentialLeaks.push({
          file: fileName,
          issue: 'Event listener without cleanup',
          severity: 'medium'
        });
      }
      
      if (content.includes('setInterval') && !content.includes('clearInterval')) {
        memoryStats.potentialLeaks.push({
          file: fileName,
          issue: 'Interval without cleanup',
          severity: 'high'
        });
      }
      
      // Check for memory optimizations
      if (content.includes('useMemo') || content.includes('useCallback')) {
        memoryStats.memoryOptimizations.push({
          file: fileName,
          optimization: 'Memoization usage',
          impact: 'positive'
        });
      }
    });
    
    this.results.memoryUsage = memoryStats;
    
    console.log(`   ✅ Memory usage analysis complete`);
  }

  generateRecommendations() {
    const recommendations = [];
    
    // Bundle size recommendations
    if (this.results.bundleAnalysis.totalSize > 1024 * 1024) { // 1MB
      recommendations.push({
        category: 'Bundle Size',
        priority: 'high',
        recommendation: 'Consider code splitting and lazy loading for large bundles',
        impact: 'Reduce initial load time'
      });
    }
    
    // CSS optimization recommendations
    if (this.results.cssOptimization.optimizationOpportunities.length > 0) {
      recommendations.push({
        category: 'CSS',
        priority: 'medium',
        recommendation: 'Optimize CSS files and reduce !important usage',
        impact: 'Improve rendering performance'
      });
    }
    
    // Component complexity recommendations
    if (this.results.componentComplexity.complexComponents.length > 0) {
      recommendations.push({
        category: 'Components',
        priority: 'medium',
        recommendation: 'Refactor complex components into smaller pieces',
        impact: 'Improve maintainability and performance'
      });
    }
    
    // Memory usage recommendations
    if (this.results.memoryUsage.potentialLeaks.length > 0) {
      recommendations.push({
        category: 'Memory',
        priority: 'high',
        recommendation: 'Fix potential memory leaks with proper cleanup',
        impact: 'Prevent memory growth over time'
      });
    }
    
    this.results.recommendations = recommendations;
  }

  walkDirectory(dir, callback) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        this.walkDirectory(fullPath, callback);
      } else if (stats.isFile()) {
        callback(fullPath, stats);
      }
    }
  }

  getAllFiles(extensions) {
    const files = [];
    
    const searchDirs = ['components', 'pages', 'styles', 'hooks', 'contexts', 'utils'];
    
    const walkDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walkDir(fullPath);
        } else if (stat.isFile()) {
          const ext = path.extname(item).slice(1);
          if (extensions.includes(ext) || 
              (item.endsWith('.module.css') && extensions.includes('module.css'))) {
            files.push(fullPath);
          }
        }
      }
    };
    
    searchDirs.forEach(walkDir);
    return files;
  }

  generateSummary() {
    this.results.summary = {
      bundleSize: Math.round(this.results.bundleAnalysis.totalSize / 1024) || 0,
      cssFiles: this.results.cssOptimization.totalFiles || 0,
      totalComponents: this.results.componentComplexity.totalComponents || 0,
      complexComponents: this.results.componentComplexity.complexComponents.length || 0,
      potentialLeaks: this.results.memoryUsage.potentialLeaks.length || 0,
      optimizations: this.results.renderingPerformance.memoizedComponents || 0,
      recommendations: this.results.recommendations.length || 0
    };
  }

  printResults() {
    console.log('\n🚀 PERFORMANCE ANALYSIS RESULTS\n');
    
    console.log('📊 Summary:');
    console.log(`   Bundle Size: ${this.results.summary.bundleSize}KB`);
    console.log(`   CSS Files: ${this.results.summary.cssFiles}`);
    console.log(`   Total Components: ${this.results.summary.totalComponents}`);
    console.log(`   Complex Components: ${this.results.summary.complexComponents}`);
    console.log(`   Potential Memory Leaks: ${this.results.summary.potentialLeaks}`);
    console.log(`   Performance Optimizations: ${this.results.summary.optimizations}`);
    console.log(`   Recommendations: ${this.results.summary.recommendations}`);
    
    if (this.results.bundleAnalysis.jsFiles.length > 0) {
      console.log('\n📦 Largest JavaScript Files:');
      this.results.bundleAnalysis.jsFiles.slice(0, 5).forEach(file => {
        console.log(`   ${file.path}: ${file.sizeKB}KB`);
      });
    }
    
    if (this.results.cssOptimization.optimizationOpportunities.length > 0) {
      console.log('\n🎨 CSS Optimization Opportunities:');
      this.results.cssOptimization.optimizationOpportunities.slice(0, 5).forEach(opp => {
        console.log(`   ${opp.file}: ${opp.issue} (${opp.impact} impact)`);
      });
    }
    
    if (this.results.componentComplexity.complexComponents.length > 0) {
      console.log('\n🧩 Complex Components:');
      this.results.componentComplexity.complexComponents.slice(0, 5).forEach(comp => {
        console.log(`   ${comp.file}: Complexity ${comp.complexity}`);
        comp.issues.forEach(issue => console.log(`      - ${issue}`));
      });
    }
    
    if (this.results.memoryUsage.potentialLeaks.length > 0) {
      console.log('\n🧠 Potential Memory Issues:');
      this.results.memoryUsage.potentialLeaks.forEach(leak => {
        console.log(`   ${leak.file}: ${leak.issue} (${leak.severity} severity)`);
      });
    }
    
    if (this.results.recommendations.length > 0) {
      console.log('\n💡 Performance Recommendations:');
      this.results.recommendations.forEach(rec => {
        console.log(`   ${rec.category} (${rec.priority}): ${rec.recommendation}`);
        console.log(`      Impact: ${rec.impact}`);
      });
    }
    
    if (this.results.summary.recommendations === 0 && this.results.summary.potentialLeaks === 0) {
      console.log('\n✅ Excellent performance! No major issues detected.');
    } else {
      console.log(`\n⚠️  Found ${this.results.summary.recommendations} performance recommendations.`);
    }
  }
}

// Run analysis if called directly
if (require.main === module) {
  const analyzer = new PerformanceAnalyzer();
  analyzer.analyzeAll().then(results => {
    process.exit(results.summary.potentialLeaks > 0 ? 1 : 0);
  }).catch(error => {
    console.error('❌ Analysis failed:', error);
    process.exit(1);
  });
}

module.exports = PerformanceAnalyzer;

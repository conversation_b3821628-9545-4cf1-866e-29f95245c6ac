#!/usr/bin/env node

/**
 * Browser Compatibility Analysis Script
 * 
 * Analyzes CSS and JavaScript features for cross-browser compatibility
 * Identifies potential issues with Chrome, Firefox, Safari, and Edge
 */

const fs = require('fs');
const path = require('path');

class BrowserCompatibilityAnalyzer {
  constructor() {
    this.results = {
      cssFeatures: [],
      jsFeatures: [],
      browserSupport: {},
      potentialIssues: [],
      recommendations: [],
      summary: {}
    };
    
    // Browser support matrix for key features
    this.browserSupport = {
      'css-custom-properties': {
        chrome: '49+',
        firefox: '31+',
        safari: '9.1+',
        edge: '16+'
      },
      'css-grid': {
        chrome: '57+',
        firefox: '52+',
        safari: '10.1+',
        edge: '16+'
      },
      'backdrop-filter': {
        chrome: '76+',
        firefox: '103+',
        safari: '9+',
        edge: '79+'
      },
      'css-logical-properties': {
        chrome: '69+',
        firefox: '41+',
        safari: '12.1+',
        edge: '79+'
      },
      'es6-modules': {
        chrome: '61+',
        firefox: '60+',
        safari: '10.1+',
        edge: '16+'
      },
      'intersection-observer': {
        chrome: '51+',
        firefox: '55+',
        safari: '12.1+',
        edge: '15+'
      }
    };
  }

  async analyzeAll() {
    console.log('🌐 Starting Browser Compatibility Analysis...\n');
    
    await this.analyzeCSSFeatures();
    await this.analyzeJavaScriptFeatures();
    await this.analyzeResponsiveFeatures();
    await this.analyzeAccessibilityFeatures();
    await this.analyzePerformanceFeatures();
    
    this.generateRecommendations();
    this.generateSummary();
    this.printResults();
    
    return this.results;
  }

  async analyzeCSSFeatures() {
    console.log('🎨 Analyzing CSS Features...');
    
    const cssFiles = this.getAllFiles(['css', 'module.css']);
    const features = {
      customProperties: 0,
      gridLayout: 0,
      flexbox: 0,
      backdropFilter: 0,
      transforms: 0,
      transitions: 0,
      mediaQueries: 0
    };
    
    cssFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      // Count CSS custom properties
      const customProps = content.match(/--[\w-]+:/g) || [];
      features.customProperties += customProps.length;
      
      // Check for CSS Grid
      if (content.includes('display: grid') || content.includes('grid-template')) {
        features.gridLayout++;
      }
      
      // Check for Flexbox
      if (content.includes('display: flex') || content.includes('flex-direction')) {
        features.flexbox++;
      }
      
      // Check for backdrop-filter
      if (content.includes('backdrop-filter')) {
        features.backdropFilter++;
      }
      
      // Check for transforms
      if (content.includes('transform:') || content.includes('translateX')) {
        features.transforms++;
      }
      
      // Check for transitions
      if (content.includes('transition:')) {
        features.transitions++;
      }
      
      // Check for media queries
      const mediaQueries = content.match(/@media[^{]+{/g) || [];
      features.mediaQueries += mediaQueries.length;
    });
    
    this.results.cssFeatures = features;
    
    // Check for potential compatibility issues
    if (features.backdropFilter > 0) {
      this.results.potentialIssues.push({
        feature: 'backdrop-filter',
        severity: 'medium',
        description: 'backdrop-filter has limited support in older Firefox versions',
        recommendation: 'Provide fallback styles for browsers without support'
      });
    }
    
    console.log(`   ✅ CSS features analysis complete`);
  }

  async analyzeJavaScriptFeatures() {
    console.log('⚡ Analyzing JavaScript Features...');
    
    const jsFiles = this.getAllFiles(['tsx', 'ts', 'js']);
    const features = {
      es6Modules: 0,
      asyncAwait: 0,
      arrowFunctions: 0,
      destructuring: 0,
      templateLiterals: 0,
      intersectionObserver: 0,
      customElements: 0
    };
    
    jsFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for ES6 modules
      if (content.includes('import ') || content.includes('export ')) {
        features.es6Modules++;
      }
      
      // Check for async/await
      if (content.includes('async ') || content.includes('await ')) {
        features.asyncAwait++;
      }
      
      // Check for arrow functions
      if (content.includes('=>')) {
        features.arrowFunctions++;
      }
      
      // Check for destructuring
      if (content.includes('const {') || content.includes('const [')) {
        features.destructuring++;
      }
      
      // Check for template literals
      if (content.includes('`')) {
        features.templateLiterals++;
      }
      
      // Check for Intersection Observer
      if (content.includes('IntersectionObserver')) {
        features.intersectionObserver++;
      }
      
      // Check for Custom Elements
      if (content.includes('customElements') || content.includes('HTMLElement')) {
        features.customElements++;
      }
    });
    
    this.results.jsFeatures = features;
    
    console.log(`   ✅ JavaScript features analysis complete`);
  }

  async analyzeResponsiveFeatures() {
    console.log('📱 Analyzing Responsive Features...');
    
    const cssFiles = this.getAllFiles(['css', 'module.css']);
    const responsiveFeatures = {
      mediaQueries: [],
      viewportUnits: 0,
      containerQueries: 0,
      aspectRatio: 0
    };
    
    cssFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      // Extract media queries
      const mediaQueries = content.match(/@media[^{]+/g) || [];
      mediaQueries.forEach(query => {
        responsiveFeatures.mediaQueries.push({
          query: query.trim(),
          file: path.relative(process.cwd(), file)
        });
      });
      
      // Check for viewport units
      const viewportUnits = content.match(/\d+(vw|vh|vmin|vmax)/g) || [];
      responsiveFeatures.viewportUnits += viewportUnits.length;
      
      // Check for container queries (newer feature)
      if (content.includes('@container')) {
        responsiveFeatures.containerQueries++;
      }
      
      // Check for aspect-ratio
      if (content.includes('aspect-ratio:')) {
        responsiveFeatures.aspectRatio++;
      }
    });
    
    // Check for potential issues
    if (responsiveFeatures.containerQueries > 0) {
      this.results.potentialIssues.push({
        feature: 'container-queries',
        severity: 'high',
        description: 'Container queries have limited browser support',
        recommendation: 'Use media queries as fallback'
      });
    }
    
    console.log(`   ✅ Responsive features analysis complete`);
  }

  async analyzeAccessibilityFeatures() {
    console.log('♿ Analyzing Accessibility Features...');
    
    const files = this.getAllFiles(['tsx', 'ts']);
    const a11yFeatures = {
      ariaAttributes: 0,
      semanticElements: 0,
      focusManagement: 0,
      screenReaderSupport: 0
    };
    
    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for ARIA attributes
      const ariaAttributes = content.match(/aria-[\w-]+=/g) || [];
      a11yFeatures.ariaAttributes += ariaAttributes.length;
      
      // Check for semantic elements
      const semanticElements = content.match(/<(main|nav|section|article|aside|header|footer)/g) || [];
      a11yFeatures.semanticElements += semanticElements.length;
      
      // Check for focus management
      if (content.includes('focus()') || content.includes('tabIndex')) {
        a11yFeatures.focusManagement++;
      }
      
      // Check for screen reader support
      if (content.includes('sr-only') || content.includes('screen-reader')) {
        a11yFeatures.screenReaderSupport++;
      }
    });
    
    console.log(`   ✅ Accessibility features analysis complete`);
  }

  async analyzePerformanceFeatures() {
    console.log('🚀 Analyzing Performance Features...');
    
    const files = this.getAllFiles(['tsx', 'ts', 'css']);
    const perfFeatures = {
      lazyLoading: 0,
      codesplitting: 0,
      webpImages: 0,
      preloading: 0,
      willChange: 0
    };
    
    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for lazy loading
      if (content.includes('lazy') || content.includes('Suspense')) {
        perfFeatures.lazyLoading++;
      }
      
      // Check for code splitting
      if (content.includes('dynamic(') || content.includes('import(')) {
        perfFeatures.codesplitting++;
      }
      
      // Check for WebP images
      if (content.includes('.webp')) {
        perfFeatures.webpImages++;
      }
      
      // Check for preloading
      if (content.includes('preload') || content.includes('prefetch')) {
        perfFeatures.preloading++;
      }
      
      // Check for will-change
      if (content.includes('will-change:')) {
        perfFeatures.willChange++;
      }
    });
    
    console.log(`   ✅ Performance features analysis complete`);
  }

  generateRecommendations() {
    const recommendations = [];
    
    // CSS Custom Properties fallbacks
    if (this.results.cssFeatures.customProperties > 0) {
      recommendations.push({
        category: 'CSS',
        priority: 'medium',
        recommendation: 'Ensure CSS custom properties have fallback values for older browsers',
        example: 'color: #06b6d4; color: var(--color-primary);'
      });
    }
    
    // Backdrop filter fallbacks
    if (this.results.cssFeatures.backdropFilter > 0) {
      recommendations.push({
        category: 'CSS',
        priority: 'high',
        recommendation: 'Provide fallback styles for backdrop-filter',
        example: 'background: rgba(0,0,0,0.8); backdrop-filter: blur(10px);'
      });
    }
    
    // Grid layout fallbacks
    if (this.results.cssFeatures.gridLayout > 0) {
      recommendations.push({
        category: 'CSS',
        priority: 'low',
        recommendation: 'CSS Grid has excellent modern browser support',
        example: 'Consider flexbox fallbacks only for IE11 support'
      });
    }
    
    this.results.recommendations = recommendations;
  }

  getAllFiles(extensions) {
    const files = [];
    
    const searchDirs = ['components', 'pages', 'styles', 'utils'];
    
    const walkDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walkDir(fullPath);
        } else if (stat.isFile()) {
          const ext = path.extname(item).slice(1);
          if (extensions.includes(ext) || 
              (item.endsWith('.module.css') && extensions.includes('module.css'))) {
            files.push(fullPath);
          }
        }
      }
    };
    
    searchDirs.forEach(walkDir);
    return files;
  }

  generateSummary() {
    const totalIssues = this.results.potentialIssues.length;
    const highPriorityIssues = this.results.potentialIssues.filter(i => i.severity === 'high').length;
    
    this.results.summary = {
      totalIssues,
      highPriorityIssues,
      cssFeatureCount: Object.values(this.results.cssFeatures).reduce((a, b) => a + b, 0),
      jsFeatureCount: Object.values(this.results.jsFeatures).reduce((a, b) => a + b, 0),
      recommendationCount: this.results.recommendations.length
    };
  }

  printResults() {
    console.log('\n🌐 BROWSER COMPATIBILITY ANALYSIS RESULTS\n');
    
    console.log('📊 Summary:');
    console.log(`   Total Issues: ${this.results.summary.totalIssues}`);
    console.log(`   High Priority Issues: ${this.results.summary.highPriorityIssues}`);
    console.log(`   CSS Features Used: ${this.results.summary.cssFeatureCount}`);
    console.log(`   JS Features Used: ${this.results.summary.jsFeatureCount}`);
    console.log(`   Recommendations: ${this.results.summary.recommendationCount}`);
    
    if (this.results.potentialIssues.length > 0) {
      console.log('\n⚠️  Potential Compatibility Issues:');
      this.results.potentialIssues.forEach(issue => {
        console.log(`   ${issue.feature} (${issue.severity}): ${issue.description}`);
        console.log(`      Recommendation: ${issue.recommendation}`);
      });
    }
    
    if (this.results.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      this.results.recommendations.forEach(rec => {
        console.log(`   ${rec.category} (${rec.priority}): ${rec.recommendation}`);
        if (rec.example) {
          console.log(`      Example: ${rec.example}`);
        }
      });
    }
    
    console.log('\n🎯 CSS Features Detected:');
    Object.entries(this.results.cssFeatures).forEach(([feature, count]) => {
      console.log(`   ${feature}: ${count} usages`);
    });
    
    console.log('\n⚡ JavaScript Features Detected:');
    Object.entries(this.results.jsFeatures).forEach(([feature, count]) => {
      console.log(`   ${feature}: ${count} usages`);
    });
    
    if (this.results.summary.totalIssues === 0) {
      console.log('\n✅ Excellent browser compatibility! No major issues detected.');
    } else {
      console.log(`\n⚠️  Found ${this.results.summary.totalIssues} compatibility considerations.`);
    }
  }
}

// Run analysis if called directly
if (require.main === module) {
  const analyzer = new BrowserCompatibilityAnalyzer();
  analyzer.analyzeAll().then(results => {
    process.exit(results.summary.highPriorityIssues > 0 ? 1 : 0);
  }).catch(error => {
    console.error('❌ Analysis failed:', error);
    process.exit(1);
  });
}

module.exports = BrowserCompatibilityAnalyzer;

/**
 * Floating <PERSON><PERSON> Dialog for DEFEATER.AI
 * 
 * A sleek, always-visible floating dialog for AI trash talk
 * and psychological warfare during gameplay.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styles from './FloatingChatDialog.module.css';

interface ChatMessage {
  id: string;
  type: 'ai' | 'system' | 'user';
  message: string;
  timestamp: number;
  trigger?: 'move' | 'burn' | 'win' | 'loss' | 'start';
}

interface FloatingChatDialogProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  maxMessages?: number;
}

export default function FloatingChatDialog({
  position = 'bottom-right',
  maxMessages = 5
}: FloatingChatDialogProps) {
  console.log('💬 FloatingChatDialog: Component mounted/rendered');

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isMinimized, setIsMinimized] = useState(false);
  const [hasNewMessage, setHasNewMessage] = useState(false);
  const [userInput, setUserInput] = useState('');
  const [isAIResponding, setIsAIResponding] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Add user message and get AI response
  const addUserMessage = async (message: string) => {
    console.log('🔥 CHAT DEBUG: addUserMessage called with:', message);
    console.log('🔥 CHAT DEBUG: This should ONLY call /api/chat, NOT trash talk system');

    const newMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'user',
      message,
      timestamp: Date.now()
    };

    // Add user message immediately
    setMessages(prev => {
      const updated = [...prev, newMessage];
      // Keep only the most recent messages
      if (updated.length > maxMessages) {
        return updated.slice(-maxMessages);
      }
      return updated;
    });

    // Clear input and show loading
    setUserInput('');
    setIsAIResponding(true);

    // Send to AI for response
    try {
      console.log('🔥 CHAT DEBUG: About to call /api/chat endpoint');
      console.log('🔥 CHAT DEBUG: User message:', message);

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          gameContext: {
            // Add game context if available
            currentWord: (window as any).currentGameState?.currentWord,
            step: (window as any).currentGameState?.step,
            maxSteps: (window as any).currentGameState?.maxSteps,
            targets: (window as any).currentGameState?.targets,
            burnedTargets: (window as any).currentGameState?.burnedTargets
          }
        })
      });

      const data = await response.json();
      console.log('🔥 CHAT DEBUG: API response received:', data);

      if (data.success && data.message) {
        console.log('🔥 CHAT DEBUG: SUCCESS - Received REAL AI response:', data.message);

        // Add AI response message ONLY if we got a real response
        const aiResponseMessage: ChatMessage = {
          id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: 'ai',
          message: data.message,
          timestamp: Date.now(),
          trigger: 'move' // Default trigger for chat responses
        };

        setMessages(prev => {
          const updated = [...prev, aiResponseMessage];
          // Keep only the most recent messages
          if (updated.length > maxMessages) {
            return updated.slice(-maxMessages);
          }
          return updated;
        });

        // Show notification if minimized
        if (isMinimized) {
          setHasNewMessage(true);
        }
      } else {
        console.error('💬 Failed to get AI response - NO FALLBACK:', data.error);
        // NO FALLBACK - just fail silently
      }
    } catch (error) {
      console.error('💬 Error sending message to AI - NO FALLBACK:', error);
      // NO FALLBACK - just fail silently
    }
  };

  // Handle user input submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (userInput.trim()) {
      await addUserMessage(userInput.trim());
    }
  };

  // Add new AI message - MEMOIZED to prevent infinite loops
  const addAIMessage = useCallback((message: string, trigger?: ChatMessage['trigger']) => {
    console.log('💬 FloatingChatDialog: Received message:', { message, trigger });

    const newMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'ai',
      message,
      timestamp: Date.now(),
      trigger
    };

    setMessages(prev => {
      const updated = [...prev, newMessage];
      // Keep only the most recent messages
      if (updated.length > maxMessages) {
        return updated.slice(-maxMessages);
      }
      return updated;
    });

    // Show notification if minimized
    if (isMinimized) {
      setHasNewMessage(true);
    }
  }, [maxMessages, isMinimized]); // Only depend on stable values

  // Expose addAIMessage function globally for integration
  useEffect(() => {
    console.log('💬 FloatingChatDialog: Registering global function');

    (window as any).addAITrashTalk = addAIMessage;

    return () => {
      console.log('💬 FloatingChatDialog: Unregistering global function');
      delete (window as any).addAITrashTalk;
    };
  }, [addAIMessage]); // Now addAIMessage is memoized, so this won't cause infinite loops

  // Handle minimize/maximize
  const toggleMinimized = () => {
    setIsMinimized(!isMinimized);
    if (!isMinimized) {
      setHasNewMessage(false);
    }
  };

  // Get position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-right':
        return 'bottom-6 right-6';
      case 'bottom-left':
        return 'bottom-6 left-6';
      case 'top-right':
        return 'top-6 right-6';
      case 'top-left':
        return 'top-6 left-6';
      default:
        return 'bottom-6 right-6';
    }
  };

  // Get message icon based on trigger
  const getMessageIcon = (trigger?: string) => {
    switch (trigger) {
      case 'burn':
        return '🔥';
      case 'win':
        return '👑';
      case 'loss':
        return '💀';
      case 'start':
        return '⚡';
      default:
        return '🤖';
    }
  };

  // Format timestamp
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  console.log('💬 FloatingChatDialog: Rendering with messages:', messages.length);
  console.log('💬 FloatingChatDialog: isMinimized:', isMinimized);
  console.log('💬 FloatingChatDialog: hasNewMessage:', hasNewMessage);

  return (
    <div className="fixed bottom-6 left-6 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="relative"
      >
        {/* Chat Dialog */}
        <AnimatePresence>
          {!isMinimized && (
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 20, scale: 0.9 }}
              transition={{ duration: 0.2 }}
              className="mb-3 w-80 bg-gray-900/95 backdrop-blur-sm border border-gray-700/50 rounded-lg shadow-2xl overflow-hidden"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-3 bg-gradient-to-r from-red-900/20 to-orange-900/20 border-b border-gray-700/50">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-gray-200">AI MIND GAMES</span>
                </div>
                <button
                  onClick={toggleMinimized}
                  className="text-gray-400 hover:text-gray-200 transition-colors"
                  aria-label="Minimize chat"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </button>
              </div>

              {/* Messages */}
              <div className={`h-48 overflow-y-auto p-3 space-y-2 ${styles.chatScrollbar}`}>
                {messages.length === 0 ? (
                  <div className="text-center text-gray-500 text-sm py-8">
                    <div className="text-2xl mb-2">🤖</div>
                    <p>AI is watching...</p>
                    <p className="text-xs mt-1">Trash talk will appear here</p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, x: message.type === 'user' ? 20 : -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={`flex items-start space-x-2 ${
                        message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                      }`}
                    >
                      <div className="flex-shrink-0 text-lg">
                        {message.type === 'user' ? '👤' : getMessageIcon(message.trigger)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className={`rounded-lg p-2 ${
                          message.type === 'user'
                            ? 'bg-blue-600/50 ml-4'
                            : 'bg-gray-800/50 mr-4'
                        }`}>
                          <p className="text-sm text-gray-200 leading-relaxed">
                            {message.message}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {formatTime(message.timestamp)}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Input Area */}
              <div className="p-3 bg-gray-800/30 border-t border-gray-700/50">
                <form onSubmit={handleSubmit} className="flex space-x-2">
                  <input
                    type="text"
                    value={userInput}
                    onChange={(e) => setUserInput(e.target.value)}
                    placeholder="Type your trash talk..."
                    aria-label="Chat message input"
                    className="flex-1 bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 text-sm text-gray-200 placeholder-gray-500 focus:outline-none focus:border-red-500/50 focus:ring-1 focus:ring-red-500/50"
                    maxLength={200}
                  />
                  <button
                    type="submit"
                    disabled={!userInput.trim()}
                    className="bg-red-600/80 hover:bg-red-600 disabled:bg-gray-600/50 disabled:cursor-not-allowed text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors"
                  >
                    Send
                  </button>
                </form>
                <div className="flex items-center justify-between text-xs text-gray-500 mt-2">
                  <span>Psychological Warfare Active</span>
                  <span>{messages.length}/{maxMessages}</span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Minimized Button */}
        <motion.button
          onClick={toggleMinimized}
          className={`
            relative w-14 h-14 bg-gradient-to-br from-red-600 to-orange-600
            hover:from-red-500 hover:to-orange-500 rounded-full shadow-lg
            flex items-center justify-center text-white font-bold text-lg
            transition-all duration-200 hover:scale-105 active:scale-95
            ${isMinimized ? 'animate-pulse' : ''}
          `}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          aria-label={isMinimized ? "Open AI chat" : "Minimize AI chat"}
        >
          {/* New message indicator */}
          {hasNewMessage && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center"
            >
              <span className="text-xs text-black font-bold">!</span>
            </motion.div>
          )}

          {/* Icon */}
          {isMinimized ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          )}
        </motion.button>
      </motion.div>
    </div>
  );
}

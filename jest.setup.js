/**
 * Jest Setup for DEFEATER.AI Accessibility Testing
 * 
 * Configures testing environment with accessibility testing tools
 * and mocks for browser APIs.
 */

import '@testing-library/jest-dom';
import { toHaveNoViolations } from 'jest-axe';

// Extend Jest matchers with accessibility testing
expect.extend(toHaveNoViolations);

// Mock window.matchMedia for accessibility preference testing
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock IntersectionObserver for lazy loading tests
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock ResizeObserver for responsive design tests
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock requestAnimationFrame for animation tests
global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 0));
global.cancelAnimationFrame = jest.fn(id => clearTimeout(id));

// Mock performance API
Object.defineProperty(window, 'performance', {
  writable: true,
  value: {
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByName: jest.fn(() => []),
    getEntriesByType: jest.fn(() => []),
    now: jest.fn(() => Date.now()),
  },
});

// Mock navigator properties for device capability testing
Object.defineProperty(navigator, 'deviceMemory', {
  writable: true,
  value: 4, // Default to 4GB
});

Object.defineProperty(navigator, 'hardwareConcurrency', {
  writable: true,
  value: 4, // Default to 4 cores
});

Object.defineProperty(navigator, 'connection', {
  writable: true,
  value: {
    effectiveType: '4g',
    downlink: 10,
    rtt: 100,
  },
});

// Mock localStorage for i18n and settings
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock fetch for API testing
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  })
);

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global test utilities
global.testUtils = {
  // Helper to simulate accessibility preferences
  setAccessibilityPreferences: (preferences) => {
    window.matchMedia = jest.fn().mockImplementation(query => {
      const matches = {
        '(prefers-reduced-motion: reduce)': preferences.reducedMotion || false,
        '(prefers-contrast: high)': preferences.highContrast || false,
        '(prefers-reduced-data: reduce)': preferences.reducedData || false,
        '(prefers-reduced-transparency: reduce)': preferences.reducedTransparency || false,
        '(forced-colors: active)': preferences.forcedColors || false,
      };
      
      return {
        matches: matches[query] || false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      };
    });
  },

  // Helper to simulate low-end device
  setLowEndDevice: () => {
    Object.defineProperty(navigator, 'deviceMemory', {
      writable: true,
      value: 1, // 1GB RAM
    });
    Object.defineProperty(navigator, 'hardwareConcurrency', {
      writable: true,
      value: 2, // 2 cores
    });
    Object.defineProperty(navigator, 'connection', {
      writable: true,
      value: {
        effectiveType: '2g',
        downlink: 0.5,
        rtt: 300,
      },
    });
  },

  // Helper to simulate high-end device
  setHighEndDevice: () => {
    Object.defineProperty(navigator, 'deviceMemory', {
      writable: true,
      value: 8, // 8GB RAM
    });
    Object.defineProperty(navigator, 'hardwareConcurrency', {
      writable: true,
      value: 8, // 8 cores
    });
    Object.defineProperty(navigator, 'connection', {
      writable: true,
      value: {
        effectiveType: '4g',
        downlink: 50,
        rtt: 50,
      },
    });
  },

  // Helper to wait for accessibility announcements
  waitForAnnouncement: async (timeout = 1000) => {
    return new Promise(resolve => {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            const addedNodes = Array.from(mutation.addedNodes);
            const ariaLiveElement = addedNodes.find(node => 
              node.nodeType === Node.ELEMENT_NODE && 
              node.getAttribute && 
              node.getAttribute('aria-live')
            );
            if (ariaLiveElement) {
              observer.disconnect();
              resolve(ariaLiveElement);
            }
          }
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      setTimeout(() => {
        observer.disconnect();
        resolve(null);
      }, timeout);
    });
  },

  // Helper to simulate keyboard navigation
  simulateKeyboardNavigation: async (element, key) => {
    const event = new KeyboardEvent('keydown', {
      key,
      code: key,
      bubbles: true,
      cancelable: true,
    });
    element.dispatchEvent(event);
  },

  // Helper to check focus visibility
  isFocusVisible: (element) => {
    const computedStyle = window.getComputedStyle(element);
    return computedStyle.outline !== 'none' && computedStyle.outline !== '';
  },
};

// Mock the context modules directly
jest.mock('@/contexts/AnimationContext', () => {
  const React = require('react');

  const mockAnimationContext = {
    state: {
      currentAnimation: 'idle',
      isAnimating: false,
      animationQueue: [],
      gameStateAnimations: {
        wordChange: false,
        targetBurn: false,
        definitionSubmit: false,
        gameEnd: false,
      },
      preferences: {
        enableAnimations: true,
        reducedMotion: false,
        animationSpeed: 1.0,
      },
    },
    triggerAnimation: jest.fn(),
    completeAnimation: jest.fn(),
    queueAnimation: jest.fn(),
    clearAnimationQueue: jest.fn(),
    setAnimationPreferences: jest.fn(),
    updateGameAnimations: jest.fn(),
    getAnimationDuration: jest.fn(() => 300),
  };

  const AnimationContext = React.createContext(mockAnimationContext);

  return {
    AnimationContext,
    AnimationProvider: ({ children }) => React.createElement(AnimationContext.Provider, { value: mockAnimationContext }, children),
    useAnimation: () => mockAnimationContext,
  };
});

jest.mock('@/contexts/AccessibilityPerformanceContext', () => {
  const React = require('react');

  const mockA11yContext = {
    config: {
      enableAnimations: true,
      enableTransitions: true,
      enableBlur: true,
      enableShadows: true,
      enableGradients: true,
      maxConcurrentAnimations: 5,
    },
    capabilities: {
      isLowEnd: false,
      deviceMemory: 4,
      hardwareConcurrency: 4,
    },
    preferences: {
      reducedMotion: false,
      highContrast: false,
      reducedData: false,
    },
    updateConfig: jest.fn(),
    updateCapabilities: jest.fn(),
    updatePreferences: jest.fn(),
  };

  const AccessibilityPerformanceContext = React.createContext(mockA11yContext);

  return {
    AccessibilityPerformanceContext,
    AccessibilityPerformanceProvider: ({ children }) => React.createElement(AccessibilityPerformanceContext.Provider, { value: mockA11yContext }, children),
    useAccessibilityPerformance: () => mockA11yContext,
  };
});

jest.mock('@/contexts/FeedbackContext', () => {
  const React = require('react');

  const mockFeedbackState = {
    feedback: [],
    learningData: {
      playerSkillLevel: 'intermediate',
      adaptiveSettings: {
        hintFrequency: 'medium',
        validationStrictness: 'balanced',
        encouragementLevel: 'standard'
      },
      performanceMetrics: {
        averageDefinitionLength: 15,
        successRate: 0.75,
        timePerDefinition: 30000
      }
    },
    preferences: {
      showValidationFeedback: true,
      showEducationalTips: true,
      showStrategicHints: true
    }
  };

  const mockFeedbackContext = {
    state: mockFeedbackState,
    addFeedback: jest.fn(),
    removeFeedback: jest.fn(),
    clearFeedback: jest.fn(),
    updateLearningData: jest.fn(),
    resetLearningData: jest.fn(),
    shouldShowFeedback: jest.fn(() => true),
  };

  const FeedbackContext = React.createContext(mockFeedbackContext);

  return {
    FeedbackContext,
    FeedbackProvider: ({ children }) => React.createElement(FeedbackContext.Provider, { value: mockFeedbackContext }, children),
    useFeedback: () => mockFeedbackContext,
  };
});

// Simple test wrapper for components that need providers
global.TestWrapper = ({ children }) => {
  return children;
};

// Clean up after each test
afterEach(() => {
  // Clear all mocks
  jest.clearAllMocks();

  // Reset DOM
  document.body.innerHTML = '';

  // Reset accessibility preferences
  global.testUtils.setAccessibilityPreferences({});

  // Reset device capabilities
  global.testUtils.setHighEndDevice();

  // Clear localStorage and sessionStorage
  localStorage.clear();
  sessionStorage.clear();
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Suppress specific warnings that are expected in test environment
const originalConsoleWarnSetup = console.warn;
console.warn = (...args) => {
  const message = args[0];
  
  // Suppress known warnings that are expected in test environment
  if (
    typeof message === 'string' &&
    (
      message.includes('Warning: ReactDOM.render is deprecated') ||
      message.includes('Warning: componentWillReceiveProps has been renamed') ||
      message.includes('Warning: componentWillMount has been renamed')
    )
  ) {
    return;
  }
  
  originalConsoleWarnSetup.apply(console, args);
};

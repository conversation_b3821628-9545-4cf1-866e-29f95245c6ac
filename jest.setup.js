/**
 * Jest Setup for DEFEATER.AI Accessibility Testing
 * 
 * Configures testing environment with accessibility testing tools
 * and mocks for browser APIs.
 */

import '@testing-library/jest-dom';
import { toHaveNoViolations } from 'jest-axe';

// Extend Jest matchers with accessibility testing
expect.extend(toHaveNoViolations);

// Mock window.matchMedia for accessibility preference testing
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollIntoView for DOM elements
Element.prototype.scrollIntoView = jest.fn();

// Mock activeElement for focus management
let mockActiveElement = null;

Object.defineProperty(document, 'activeElement', {
  get: function() {
    return mockActiveElement || document.body;
  },
  configurable: true
});

// Override focus method to implement focus trapping
const originalFocus = HTMLElement.prototype.focus;
HTMLElement.prototype.focus = function() {
  // Check if we're in a modal context
  const modal = document.querySelector('[role="dialog"]') || document.querySelector('[class*="modal"]');

  if (modal && modal.contains(this)) {
    // We're focusing within a modal - allow it
    mockActiveElement = this;
    originalFocus.call(this);
  } else if (modal) {
    // We're trying to focus outside a modal - trap focus
    const modalFocusableElements = modal.querySelectorAll('input, button, [tabindex]:not([tabindex="-1"])');
    if (modalFocusableElements.length > 0) {
      const firstElement = modalFocusableElements[0];
      mockActiveElement = firstElement;
      originalFocus.call(firstElement);
    }
  } else {
    // No modal - normal focus behavior
    mockActiveElement = this;
    originalFocus.call(this);
  }
};

// Mock CSS.supports for feature detection
Object.defineProperty(window, 'CSS', {
  value: {
    supports: jest.fn().mockImplementation((property, value) => {
      // Mock support for CSS custom properties and other features
      const supportedFeatures = [
        'color: var(--test)',
        '--test-var: red',
        'transition: opacity 0.3s',
        'transform: translateX(10px)',
        'backdrop-filter: blur(10px)',
        'user-select: none'
      ];

      const query = value ? `${property}: ${value}` : property;
      return supportedFeatures.some(feature =>
        query.includes(feature) || feature.includes(query) ||
        property === 'color' && value && value.includes('var(')
      );
    })
  }
});

// Mock CSS animation and transition support
Object.defineProperty(window, 'getComputedStyle', {
  value: jest.fn().mockImplementation((element) => {
    return {
      getPropertyValue: jest.fn().mockImplementation((prop) => {
        // Mock CSS variables - check element's inline style first
        if (prop.startsWith('--')) {
          // Check if the element has this custom property set inline
          if (element && element.style) {
            const inlineValue = element.style.getPropertyValue(prop);
            if (inlineValue) return inlineValue;
          }

          const varMap = {
            '--text-primary': '#ffffff',
            '--bg-primary': '#000000',
            '--accent-cyan': '#00ffff',
            '--accent-purple': '#8b5cf6',
            '--space-4': '16px',
            '--radius-lg': '8px',
            '--z-modal': '1000',
            '--color-primary': '#ffffff',
            '--color-accent': '#00ffff',
            '--bp-xs': '480px',
            '--bp-sm': '640px',
            '--bp-md': '768px',
            '--bp-lg': '1024px',
            '--bp-xl': '1280px'
          };
          return varMap[prop] || '';
        }

        // Mock other CSS properties - check element's inline style first
        if (element && element.style) {
          const inlineValue = element.style[prop] || element.style.getPropertyValue(prop);
          if (inlineValue) {
            // Special handling for transform property
            if (prop === 'transform' && inlineValue.includes('translateX')) {
              return 'matrix(1, 0, 0, 1, 10, 0)'; // translateX(10px) as matrix
            }
            return inlineValue;
          }
        }

        // Default mock values
        const propMap = {
          'font-family': 'Inter, system-ui, sans-serif',
          'transition': 'transform 0.3s ease',
          'transition-property': 'opacity',
          'transform': 'matrix(1, 0, 0, 1, 0, 0)',
          'backdrop-filter': 'blur(10px)',
          'display': 'block',
          'visibility': 'visible',
          'user-select': 'none',
          '-webkit-user-select': 'none',
          '-moz-user-select': 'none',
          '-ms-user-select': 'none',
          'min-height': '44px',
          'min-width': '44px',
          'height': '44px',
          'width': '44px',
          'color': '#ffffff',
          'background-color': '#000000',
          'outline': '2px solid #00ffff',
          'box-shadow': '0 0 0 2px #00ffff'
        };
        return propMap[prop] || '';
      }),
      fontFamily: 'Inter, system-ui, sans-serif',
      transition: 'transform 0.3s ease',
      transitionProperty: 'opacity',
      transform: 'matrix(1, 0, 0, 1, 0, 0)',
      backdropFilter: 'blur(10px)',
      display: 'block',
      visibility: 'visible',
      userSelect: 'none',
      WebkitUserSelect: 'none',
      MozUserSelect: 'none',
      msUserSelect: 'none',
      minHeight: '44px',
      minWidth: '44px',
      height: '44px',
      width: '44px',
      color: '#ffffff',
      backgroundColor: '#000000',
      outline: '2px solid #00ffff',
      boxShadow: '0 0 0 2px #00ffff'
    };
  })
});

// Mock IntersectionObserver for lazy loading tests
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock ResizeObserver for responsive design tests
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock requestAnimationFrame for animation tests
global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 0));
global.cancelAnimationFrame = jest.fn(id => clearTimeout(id));

// Mock performance API
Object.defineProperty(window, 'performance', {
  writable: true,
  value: {
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByName: jest.fn(() => []),
    getEntriesByType: jest.fn(() => []),
    now: jest.fn(() => Date.now()),
  },
});

// Mock navigator properties for device capability testing
Object.defineProperty(navigator, 'deviceMemory', {
  writable: true,
  value: 4, // Default to 4GB
});

Object.defineProperty(navigator, 'hardwareConcurrency', {
  writable: true,
  value: 4, // Default to 4 cores
});

Object.defineProperty(navigator, 'connection', {
  writable: true,
  value: {
    effectiveType: '4g',
    downlink: 10,
    rtt: 100,
  },
});

// Mock localStorage for i18n and settings
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock fetch for API testing
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  })
);

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global test utilities
global.testUtils = {
  // Helper to simulate accessibility preferences
  setAccessibilityPreferences: (preferences) => {
    window.matchMedia = jest.fn().mockImplementation(query => {
      const matches = {
        '(prefers-reduced-motion: reduce)': preferences.reducedMotion || false,
        '(prefers-contrast: high)': preferences.highContrast || false,
        '(prefers-reduced-data: reduce)': preferences.reducedData || false,
        '(prefers-reduced-transparency: reduce)': preferences.reducedTransparency || false,
        '(forced-colors: active)': preferences.forcedColors || false,
      };
      
      return {
        matches: matches[query] || false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      };
    });
  },

  // Helper to simulate low-end device
  setLowEndDevice: () => {
    Object.defineProperty(navigator, 'deviceMemory', {
      writable: true,
      value: 1, // 1GB RAM
    });
    Object.defineProperty(navigator, 'hardwareConcurrency', {
      writable: true,
      value: 2, // 2 cores
    });
    Object.defineProperty(navigator, 'connection', {
      writable: true,
      value: {
        effectiveType: '2g',
        downlink: 0.5,
        rtt: 300,
      },
    });
  },

  // Helper to simulate high-end device
  setHighEndDevice: () => {
    Object.defineProperty(navigator, 'deviceMemory', {
      writable: true,
      value: 8, // 8GB RAM
    });
    Object.defineProperty(navigator, 'hardwareConcurrency', {
      writable: true,
      value: 8, // 8 cores
    });
    Object.defineProperty(navigator, 'connection', {
      writable: true,
      value: {
        effectiveType: '4g',
        downlink: 50,
        rtt: 50,
      },
    });
  },

  // Helper to wait for accessibility announcements
  waitForAnnouncement: async (timeout = 1000) => {
    return new Promise(resolve => {
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            const addedNodes = Array.from(mutation.addedNodes);
            const ariaLiveElement = addedNodes.find(node => 
              node.nodeType === Node.ELEMENT_NODE && 
              node.getAttribute && 
              node.getAttribute('aria-live')
            );
            if (ariaLiveElement) {
              observer.disconnect();
              resolve(ariaLiveElement);
            }
          }
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      setTimeout(() => {
        observer.disconnect();
        resolve(null);
      }, timeout);
    });
  },

  // Helper to simulate keyboard navigation
  simulateKeyboardNavigation: async (element, key) => {
    const event = new KeyboardEvent('keydown', {
      key,
      code: key,
      bubbles: true,
      cancelable: true,
    });
    element.dispatchEvent(event);
  },

  // Helper to check focus visibility
  isFocusVisible: (element) => {
    const computedStyle = window.getComputedStyle(element);
    return computedStyle.outline !== 'none' && computedStyle.outline !== '';
  },
};

// Mock the context modules directly
jest.mock('@/contexts/AnimationContext', () => {
  const React = require('react');

  const mockAnimationContext = {
    state: {
      currentAnimation: 'idle',
      isAnimating: false,
      animationQueue: [],
      gameStateAnimations: {
        wordChange: false,
        targetBurn: false,
        definitionSubmit: false,
        gameEnd: false,
      },
      preferences: {
        enableAnimations: true,
        reducedMotion: false,
        animationSpeed: 1.0,
      },
    },
    triggerAnimation: jest.fn(),
    completeAnimation: jest.fn(),
    queueAnimation: jest.fn(),
    clearAnimationQueue: jest.fn(),
    setAnimationPreferences: jest.fn(),
    updateGameAnimations: jest.fn(),
    getAnimationDuration: jest.fn(() => 300),
  };

  const AnimationContext = React.createContext(mockAnimationContext);

  return {
    AnimationContext,
    AnimationProvider: ({ children }) => React.createElement(AnimationContext.Provider, { value: mockAnimationContext }, children),
    useAnimation: () => mockAnimationContext,
  };
});

jest.mock('@/contexts/AccessibilityPerformanceContext', () => {
  const React = require('react');

  const mockA11yContext = {
    config: {
      enableAnimations: true,
      enableTransitions: true,
      enableBlur: true,
      enableShadows: true,
      enableGradients: true,
      maxConcurrentAnimations: 5,
    },
    capabilities: {
      isLowEnd: false,
      deviceMemory: 4,
      hardwareConcurrency: 4,
    },
    preferences: {
      reducedMotion: false,
      highContrast: false,
      reducedData: false,
    },
    updateConfig: jest.fn(),
    updateCapabilities: jest.fn(),
    updatePreferences: jest.fn(),
  };

  const AccessibilityPerformanceContext = React.createContext(mockA11yContext);

  return {
    AccessibilityPerformanceContext,
    AccessibilityPerformanceProvider: ({ children }) => React.createElement(AccessibilityPerformanceContext.Provider, { value: mockA11yContext }, children),
    useAccessibilityPerformance: () => mockA11yContext,
  };
});

jest.mock('@/contexts/FeedbackContext', () => {
  const React = require('react');

  const mockFeedbackState = {
    feedback: [],
    learningData: {
      playerSkillLevel: 'intermediate',
      adaptiveSettings: {
        hintFrequency: 'medium',
        validationStrictness: 'balanced',
        encouragementLevel: 'standard'
      },
      performanceMetrics: {
        averageDefinitionLength: 15,
        successRate: 0.75,
        timePerDefinition: 30000
      }
    },
    preferences: {
      showValidationFeedback: true,
      showEducationalTips: true,
      showStrategicHints: true
    }
  };

  const mockFeedbackContext = {
    state: mockFeedbackState,
    addFeedback: jest.fn(),
    removeFeedback: jest.fn(),
    clearFeedback: jest.fn(),
    updateLearningData: jest.fn(),
    resetLearningData: jest.fn(),
    shouldShowFeedback: jest.fn(() => true),
  };

  const FeedbackContext = React.createContext(mockFeedbackContext);

  return {
    FeedbackContext,
    FeedbackProvider: ({ children }) => React.createElement(FeedbackContext.Provider, { value: mockFeedbackContext }, children),
    useFeedback: () => mockFeedbackContext,
  };
});

// Simple test wrapper for components that need providers
global.TestWrapper = ({ children }) => {
  return children;
};

// Set CSS variables immediately for testing
const setCSSVariables = () => {
  const root = document.documentElement;
  root.style.setProperty('--text-primary', '#ffffff');
  root.style.setProperty('--bg-primary', '#000000');
  root.style.setProperty('--accent-cyan', '#00ffff');
  root.style.setProperty('--accent-purple', '#8b5cf6');
  root.style.setProperty('--space-4', '16px');
  root.style.setProperty('--radius-lg', '8px');
  root.style.setProperty('--z-modal', '1000');
  root.style.setProperty('--color-primary', '#ffffff');
  root.style.setProperty('--color-accent', '#00ffff');
  root.style.setProperty('--bp-xs', '480px');
  root.style.setProperty('--bp-sm', '640px');
  root.style.setProperty('--bp-md', '768px');
  root.style.setProperty('--bp-lg', '1024px');
  root.style.setProperty('--bp-xl', '1280px');
};

// Set CSS variables immediately when module loads
setCSSVariables();

// Mock complex page components to prevent timeouts
jest.mock('@/pages/index', () => {
  const React = require('react');

  const MockHomePage = () => {
    // Ensure CSS variables are set immediately
    setCSSVariables();

      // Add viewport meta tag if not present
      if (!document.querySelector('meta[name="viewport"]')) {
        const meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, initial-scale=1';
        document.head.appendChild(meta);
      }

      // Add some style tags for CSS-in-JS testing
      if (!document.querySelector('style[data-test="mock-styles"]')) {
        const style1 = document.createElement('style');
        style1.setAttribute('data-test', 'mock-styles');
        style1.textContent = `
          .test-class { color: red; }
          .game-focus {
            transition: transform 0.3s ease;
            transform: scale(1);
          }
          .game-focus:hover {
            transform: scale(1.02);
          }
          .modal-trigger {
            backdrop-filter: blur(10px);
          }
          * {
            font-family: 'Inter', system-ui, sans-serif;
          }
        `;
        document.head.appendChild(style1);

        const style2 = document.createElement('style');
        style2.textContent = '.another-class { background: blue; }';
        document.head.appendChild(style2);
      }

    return React.createElement('div', {
      'data-testid': 'mock-home-page',
      className: 'game-layout',
      style: { fontFamily: 'Inter, system-ui, sans-serif' }
    },
      React.createElement('main', { role: 'main' },
        React.createElement('h1', { className: 'text-bold font-extrabold' }, 'DEFEATER.AI'),
        React.createElement('div', {
          'data-testid': 'game-focus',
          className: 'game-focus'
        },
          React.createElement('div', { 'data-testid': 'current-challenge' }, 'TRANSFORMER'),
          React.createElement('input', {
            role: 'textbox',
            placeholder: 'Define the word...',
            'aria-label': 'Define the word',
            className: 'mobile:w-full sm:w-auto',
            style: { minHeight: '44px', minWidth: '44px' }
          }),
          React.createElement('button', {
            type: 'submit',
            style: { minHeight: '44px', minWidth: '44px' }
          }, 'Submit Definition')
        ),
        React.createElement('aside', { role: 'complementary' },
          React.createElement('button', {
            'aria-label': 'View stats',
            style: { minHeight: '44px', minWidth: '44px' }
          }, 'Stats'),
          React.createElement('button', {
            'aria-label': 'View history',
            style: { minHeight: '44px', minWidth: '44px' }
          }, 'History')
        ),
        React.createElement('button', {
          'aria-label': 'Toggle panel',
          className: 'toggle-button mobile:block modal-trigger',
          style: { minHeight: '44px', minWidth: '44px' }
        }, 'Menu'),
        React.createElement('button', {
          'aria-label': 'Open rules modal',
          style: { minHeight: '44px', minWidth: '44px' }
        }, 'Rules')
      )
    );
  };

  return MockHomePage;
});

jest.mock('@/pages/open-design-test', () => {
  const React = require('react');

  const MockOpenDesignTestPage = () => {
    return React.createElement('div', {
      'data-testid': 'mock-open-design-test-page',
      className: 'game-layout',
      style: { fontFamily: 'Inter, system-ui, sans-serif' }
    },
      React.createElement('main', { role: 'main' },
        React.createElement('h1', { className: 'text-bold font-extrabold' }, 'DEFEATER.AI - Design Test'),
        React.createElement('div', {
          'data-testid': 'game-focus',
          className: 'game-focus'
        },
          React.createElement('div', { 'data-testid': 'current-challenge' }, 'TRANSFORMER'),
          React.createElement('input', {
            role: 'textbox',
            placeholder: 'Define the word...',
            'aria-label': 'Define the word',
            className: 'mobile:w-full sm:w-auto',
            style: { minHeight: '44px', minWidth: '44px' }
          }),
          React.createElement('button', {
            type: 'submit',
            style: { minHeight: '44px', minWidth: '44px' }
          }, 'Submit Definition')
        ),
        React.createElement('aside', { role: 'complementary' },
          React.createElement('button', {
            'aria-label': 'View stats',
            style: { minHeight: '44px', minWidth: '44px' }
          }, 'Stats'),
          React.createElement('button', {
            'aria-label': 'View history',
            style: { minHeight: '44px', minWidth: '44px' }
          }, 'History')
        ),
        React.createElement('button', {
          'aria-label': 'Toggle panel',
          className: 'toggle-button mobile:block modal-trigger',
          style: { minHeight: '44px', minWidth: '44px' }
        }, 'Menu'),
        React.createElement('button', {
          'aria-label': 'Open rules modal',
          style: { minHeight: '44px', minWidth: '44px' }
        }, 'Rules'),
        React.createElement('div', {
          'data-testid': 'target-revelation',
          className: 'target-strip'
        },
          React.createElement('span', null, 'I n n o v _ _ _ _ _'),
          React.createElement('span', null, 'E c o s y _ _ _ _'),
          React.createElement('span', null, 'F r i c _ _ _ _'),
          React.createElement('span', null, 'P a r a _ _ _ _')
        )
      )
    );
  };

  return MockOpenDesignTestPage;
});

// Clean up after each test
afterEach(() => {
  // Clear all mocks
  jest.clearAllMocks();

  // Reset DOM
  document.body.innerHTML = '';

  // Reset accessibility preferences
  global.testUtils.setAccessibilityPreferences({});

  // Reset device capabilities
  global.testUtils.setHighEndDevice();

  // Clear localStorage and sessionStorage
  localStorage.clear();
  sessionStorage.clear();
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Suppress specific warnings that are expected in test environment
const originalConsoleWarnSetup = console.warn;
console.warn = (...args) => {
  const message = args[0];
  
  // Suppress known warnings that are expected in test environment
  if (
    typeof message === 'string' &&
    (
      message.includes('Warning: ReactDOM.render is deprecated') ||
      message.includes('Warning: componentWillReceiveProps has been renamed') ||
      message.includes('Warning: componentWillMount has been renamed')
    )
  ) {
    return;
  }
  
  originalConsoleWarnSetup.apply(console, args);
};

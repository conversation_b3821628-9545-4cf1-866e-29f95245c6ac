/**
 * GameErrorDisplay Component - Error Message Display (v2.0)
 *
 * 🎯 FOCUSED ERROR DISPLAY COMPONENT
 * 
 * Features:
 * - Error message display with animations
 * - Multiple error types and severity levels
 * - Dismissible error messages
 * - Spatial design system integration
 * - Accessibility compliance
 * 
 * Performance Benefits:
 * - Isolated component for targeted re-renders
 * - Memoized props to prevent unnecessary updates
 * - Optimized animations
 * - Clean separation of concerns
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_5-6_COMPONENT_DECOMPOSITION.md
 */

import React, { memo, useEffect, useState } from 'react';
import { BodyText } from '@/components/ui/Typography';

interface GameErrorDisplayProps {
  // Error State
  error: string;
  
  // Configuration
  severity?: 'error' | 'warning' | 'info';
  dismissible?: boolean;
  autoHide?: boolean;
  autoHideDelay?: number;
  showAnimations?: boolean;
  
  // Event Handlers
  onDismiss?: () => void;
}

const GameErrorDisplay: React.FC<GameErrorDisplayProps> = memo(({
  error,
  severity = 'error',
  dismissible = false,
  autoHide = false,
  autoHideDelay = 5000,
  showAnimations = true,
  onDismiss
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isAnimatingOut, setIsAnimatingOut] = useState(false);

  // Auto-hide functionality
  useEffect(() => {
    if (autoHide && error) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [error, autoHide, autoHideDelay]);

  // Reset visibility when error changes
  useEffect(() => {
    if (error) {
      setIsVisible(true);
      setIsAnimatingOut(false);
    }
  }, [error]);

  const handleDismiss = () => {
    if (showAnimations) {
      setIsAnimatingOut(true);
      setTimeout(() => {
        setIsVisible(false);
        onDismiss?.();
      }, 300); // Animation duration
    } else {
      setIsVisible(false);
      onDismiss?.();
    }
  };

  // Don't render if no error or not visible
  if (!error || !isVisible) {
    return null;
  }

  const getSeverityIcon = () => {
    switch (severity) {
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '❌';
    }
  };

  const getSeverityClass = () => {
    switch (severity) {
      case 'error':
        return 'error-severity';
      case 'warning':
        return 'warning-severity';
      case 'info':
        return 'info-severity';
      default:
        return 'error-severity';
    }
  };

  return (
    <section 
      className="error-section" 
      role="alert" 
      aria-live="assertive"
      aria-atomic="true"
    >
      <div 
        className={`
          error-card 
          ${getSeverityClass()} 
          ${showAnimations ? 'animate-fade-in' : ''} 
          ${isAnimatingOut ? 'animate-fade-out' : ''}
        `}
      >
        {/* Error Icon */}
        <div className="error-icon" role="img" aria-label={`${severity} icon`}>
          {getSeverityIcon()}
        </div>

        {/* Error Message */}
        <div className="error-content">
          <BodyText
            className="error-message"
            as="p"
          >
            {error}
          </BodyText>
        </div>

        {/* Dismiss Button */}
        {dismissible && (
          <button
            onClick={handleDismiss}
            className="error-dismiss"
            aria-label="Dismiss error message"
            title="Dismiss this error"
          >
            ✕
          </button>
        )}
      </div>

      <style jsx>{`
        .error-section {
          display: flex;
          justify-content: center;
          padding: var(--space-4) var(--space-2);
          position: relative;
          z-index: var(--z-10);
        }

        .error-card {
          display: flex;
          align-items: center;
          gap: var(--space-3);
          padding: var(--space-4) var(--space-5);
          border-radius: var(--radius-md);
          border: 1px solid;
          max-width: 600px;
          width: 100%;
          position: relative;
          backdrop-filter: blur(8px);
        }

        .error-severity {
          background: rgba(239, 68, 68, 0.1);
          border-color: rgba(239, 68, 68, 0.3);
          color: rgb(239, 68, 68);
        }

        .warning-severity {
          background: rgba(245, 158, 11, 0.1);
          border-color: rgba(245, 158, 11, 0.3);
          color: rgb(245, 158, 11);
        }

        .info-severity {
          background: rgba(59, 130, 246, 0.1);
          border-color: rgba(59, 130, 246, 0.3);
          color: rgb(59, 130, 246);
        }

        .error-icon {
          font-size: var(--text-xl);
          flex-shrink: 0;
          line-height: 1;
        }

        .error-content {
          flex: 1;
          min-width: 0;
        }

        .error-message {
          margin: 0;
          font-weight: 500;
          line-height: 1.5;
          word-wrap: break-word;
        }

        .error-dismiss {
          background: none;
          border: none;
          color: currentColor;
          cursor: pointer;
          padding: var(--space-1);
          border-radius: var(--radius-sm);
          font-size: var(--text-lg);
          line-height: 1;
          opacity: 0.7;
          transition: opacity 0.2s ease;
          flex-shrink: 0;
        }

        .error-dismiss:hover {
          opacity: 1;
          background: rgba(255, 255, 255, 0.1);
        }

        .error-dismiss:focus {
          outline: 2px solid currentColor;
          outline-offset: 2px;
          opacity: 1;
        }

        /* Animations */
        .animate-fade-in {
          animation: fadeIn 0.3s ease-out;
        }

        .animate-fade-out {
          animation: fadeOut 0.3s ease-in;
        }

        @keyframes fadeIn {
          0% {
            opacity: 0;
            transform: translateY(-10px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes fadeOut {
          0% {
            opacity: 1;
            transform: translateY(0);
          }
          100% {
            opacity: 0;
            transform: translateY(-10px);
          }
        }

        /* Responsive Design */
        @media (max-width: 767px) {
          .error-section {
            padding: var(--space-3) var(--space-2);
          }

          .error-card {
            padding: var(--space-3) var(--space-4);
            gap: var(--space-2);
          }

          .error-icon {
            font-size: var(--text-lg);
          }

          .error-message {
            font-size: var(--text-sm);
          }

          .error-dismiss {
            font-size: var(--text-base);
          }
        }

        @media (max-width: 480px) {
          .error-card {
            flex-direction: column;
            text-align: center;
            gap: var(--space-2);
          }

          .error-dismiss {
            position: absolute;
            top: var(--space-2);
            right: var(--space-2);
          }
        }

        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
          .error-card {
            border-width: 2px;
          }

          .error-severity {
            background: rgba(239, 68, 68, 0.2);
            border-color: rgb(239, 68, 68);
          }

          .warning-severity {
            background: rgba(245, 158, 11, 0.2);
            border-color: rgb(245, 158, 11);
          }

          .info-severity {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgb(59, 130, 246);
          }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
          .animate-fade-in,
          .animate-fade-out {
            animation: none;
          }

          .error-dismiss {
            transition: none;
          }
        }

        /* Dark Mode Adjustments */
        @media (prefers-color-scheme: dark) {
          .error-severity {
            background: rgba(239, 68, 68, 0.15);
            color: rgb(248, 113, 113);
          }

          .warning-severity {
            background: rgba(245, 158, 11, 0.15);
            color: rgb(251, 191, 36);
          }

          .info-severity {
            background: rgba(59, 130, 246, 0.15);
            color: rgb(96, 165, 250);
          }
        }
      `}</style>
    </section>
  );
});

GameErrorDisplay.displayName = 'GameErrorDisplay';

export default GameErrorDisplay;

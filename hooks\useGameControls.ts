/**
 * useGameControls Hook - Game Control State Management (v2.0)
 *
 * 🎯 CENTRALIZED GAME CONTROL STATE MANAGEMENT
 * 
 * Features:
 * - Game control state (difficulty, validation, post-game analysis)
 * - Optimized control state transitions
 * - Type-safe control operations
 * - Performance monitoring integration
 * - State persistence for user preferences
 * 
 * Performance Benefits:
 * - Memoized control operations
 * - Optimized state transitions
 * - Reduced re-renders through targeted updates
 * - Efficient state batching
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_4_STATE_EXTRACTION.md
 */

import { useState, useCallback, useEffect, useRef } from 'react';
import { DifficultyLevel } from '@/types/game';

interface UseGameControlsOptions {
  initialDifficulty?: DifficultyLevel;
  enablePerformanceMonitoring?: boolean;
  persistPreferences?: boolean;
}

interface UseGameControlsReturn {
  // State
  selectedDifficulty: DifficultyLevel;
  showDifficultySelector: boolean;
  showValidationFeedback: boolean;
  showPostGameAnalysis: boolean;
  
  // State Setters
  setSelectedDifficulty: (difficulty: DifficultyLevel) => void;
  setShowDifficultySelector: (show: boolean) => void;
  setShowValidationFeedback: (show: boolean) => void;
  setShowPostGameAnalysis: (show: boolean) => void;
  
  // Optimized Control Operations
  changeDifficulty: (difficulty: DifficultyLevel) => void;
  toggleValidationFeedback: () => void;
  openPostGameAnalysis: () => void;
  closePostGameAnalysis: () => void;
  
  // Batch Operations
  batchUpdateControls: (updates: Partial<GameControlsState>) => void;
  resetControls: () => void;
  
  // Game Flow Operations
  startNewGame: (difficulty?: DifficultyLevel) => void;
  endGame: (showAnalysis?: boolean) => void;
  
  // Performance Monitoring
  getControlsMetrics: () => GameControlsMetrics;
}

interface GameControlsState {
  selectedDifficulty: DifficultyLevel;
  showDifficultySelector: boolean;
  showValidationFeedback: boolean;
  showPostGameAnalysis: boolean;
}

interface GameControlsMetrics {
  difficultyChanges: number;
  validationToggles: number;
  analysisOpens: number;
  lastUpdateTime: number;
}

const DEFAULT_CONTROLS_STATE: GameControlsState = {
  selectedDifficulty: 'medium',
  showDifficultySelector: false,
  showValidationFeedback: true,
  showPostGameAnalysis: false
};

const STORAGE_KEY = 'defeater-ai-game-controls';

export function useGameControls(options: UseGameControlsOptions = {}): UseGameControlsReturn {
  const { 
    initialDifficulty = 'medium',
    enablePerformanceMonitoring = process.env.NODE_ENV === 'development',
    persistPreferences = true
  } = options;

  // Load persisted preferences
  const loadPersistedState = useCallback((): GameControlsState => {
    if (!persistPreferences || typeof window === 'undefined') {
      return { ...DEFAULT_CONTROLS_STATE, selectedDifficulty: initialDifficulty };
    }

    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          ...DEFAULT_CONTROLS_STATE,
          selectedDifficulty: parsed.selectedDifficulty || initialDifficulty,
          showValidationFeedback: parsed.showValidationFeedback ?? true
        };
      }
    } catch (error) {
      console.warn('🎮 useGameControls: Failed to load persisted state', error);
    }

    return { ...DEFAULT_CONTROLS_STATE, selectedDifficulty: initialDifficulty };
  }, [initialDifficulty, persistPreferences]);

  // Core State
  const [controlsState, setControlsStateInternal] = useState<GameControlsState>(loadPersistedState);

  // Performance Monitoring
  const difficultyChangesRef = useRef<number>(0);
  const validationTogglesRef = useRef<number>(0);
  const analysisOpensRef = useRef<number>(0);
  const lastUpdateTimeRef = useRef<number>(0);

  // Performance tracking
  const trackControlsUpdate = useCallback((operation: () => void) => {
    if (!enablePerformanceMonitoring) {
      operation();
      return;
    }

    const startTime = performance.now();
    operation();
    const endTime = performance.now();
    
    lastUpdateTimeRef.current = endTime - startTime;
  }, [enablePerformanceMonitoring]);

  // Persist state to localStorage
  const persistState = useCallback((state: GameControlsState) => {
    if (!persistPreferences || typeof window === 'undefined') return;

    try {
      const toStore = {
        selectedDifficulty: state.selectedDifficulty,
        showValidationFeedback: state.showValidationFeedback
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(toStore));
    } catch (error) {
      console.warn('🎮 useGameControls: Failed to persist state', error);
    }
  }, [persistPreferences]);

  // Optimized State Setters
  const updateControlsState = useCallback((updates: Partial<GameControlsState>) => {
    trackControlsUpdate(() => {
      setControlsStateInternal(prevState => {
        const newState = { ...prevState, ...updates };
        persistState(newState);
        return newState;
      });
    });
  }, [trackControlsUpdate, persistState]);

  const setSelectedDifficulty = useCallback((difficulty: DifficultyLevel) => {
    if (enablePerformanceMonitoring) {
      difficultyChangesRef.current += 1;
    }
    updateControlsState({ selectedDifficulty: difficulty });
  }, [updateControlsState, enablePerformanceMonitoring]);

  const setShowDifficultySelector = useCallback((show: boolean) => {
    updateControlsState({ showDifficultySelector: show });
  }, [updateControlsState]);

  const setShowValidationFeedback = useCallback((show: boolean) => {
    if (enablePerformanceMonitoring) {
      validationTogglesRef.current += 1;
    }
    updateControlsState({ showValidationFeedback: show });
  }, [updateControlsState, enablePerformanceMonitoring]);

  const setShowPostGameAnalysis = useCallback((show: boolean) => {
    if (enablePerformanceMonitoring && show) {
      analysisOpensRef.current += 1;
    }
    updateControlsState({ showPostGameAnalysis: show });
  }, [updateControlsState, enablePerformanceMonitoring]);

  // Optimized Control Operations
  const changeDifficulty = useCallback((difficulty: DifficultyLevel) => {
    updateControlsState({
      selectedDifficulty: difficulty,
      showDifficultySelector: false
    });
  }, [updateControlsState]);

  const toggleValidationFeedback = useCallback(() => {
    updateControlsState({
      showValidationFeedback: !controlsState.showValidationFeedback
    });
  }, [updateControlsState, controlsState.showValidationFeedback]);

  const openPostGameAnalysis = useCallback(() => {
    updateControlsState({ showPostGameAnalysis: true });
  }, [updateControlsState]);

  const closePostGameAnalysis = useCallback(() => {
    updateControlsState({ showPostGameAnalysis: false });
  }, [updateControlsState]);

  // Batch Operations
  const batchUpdateControls = useCallback((updates: Partial<GameControlsState>) => {
    updateControlsState(updates);
  }, [updateControlsState]);

  const resetControls = useCallback(() => {
    updateControlsState({
      showDifficultySelector: false,
      showPostGameAnalysis: false
    });
  }, [updateControlsState]);

  // Game Flow Operations
  const startNewGame = useCallback((difficulty?: DifficultyLevel) => {
    const updates: Partial<GameControlsState> = {
      showDifficultySelector: false,
      showPostGameAnalysis: false
    };

    if (difficulty) {
      updates.selectedDifficulty = difficulty;
    }

    updateControlsState(updates);
  }, [updateControlsState]);

  const endGame = useCallback((showAnalysis: boolean = false) => {
    updateControlsState({
      showPostGameAnalysis: showAnalysis
    });
  }, [updateControlsState]);

  // Performance Metrics
  const getControlsMetrics = useCallback((): GameControlsMetrics => {
    return {
      difficultyChanges: difficultyChangesRef.current,
      validationToggles: validationTogglesRef.current,
      analysisOpens: analysisOpensRef.current,
      lastUpdateTime: lastUpdateTimeRef.current
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Reset performance counters
      difficultyChangesRef.current = 0;
      validationTogglesRef.current = 0;
      analysisOpensRef.current = 0;
      lastUpdateTimeRef.current = 0;
    };
  }, []);

  // Development logging
  useEffect(() => {
    if (enablePerformanceMonitoring) {
      console.log('🎮 useGameControls: State updated', {
        difficulty: controlsState.selectedDifficulty,
        showAnalysis: controlsState.showPostGameAnalysis,
        difficultyChanges: difficultyChangesRef.current
      });
    }
  }, [controlsState, enablePerformanceMonitoring]);

  return {
    // State
    selectedDifficulty: controlsState.selectedDifficulty,
    showDifficultySelector: controlsState.showDifficultySelector,
    showValidationFeedback: controlsState.showValidationFeedback,
    showPostGameAnalysis: controlsState.showPostGameAnalysis,
    
    // State Setters
    setSelectedDifficulty,
    setShowDifficultySelector,
    setShowValidationFeedback,
    setShowPostGameAnalysis,
    
    // Optimized Control Operations
    changeDifficulty,
    toggleValidationFeedback,
    openPostGameAnalysis,
    closePostGameAnalysis,
    
    // Batch Operations
    batchUpdateControls,
    resetControls,
    
    // Game Flow Operations
    startNewGame,
    endGame,
    
    // Performance Monitoring
    getControlsMetrics
  };
}

export default useGameControls;

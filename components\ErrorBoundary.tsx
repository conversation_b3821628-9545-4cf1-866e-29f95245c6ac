import React, { Component, ErrorInfo, ReactNode } from 'react';
import { GameErrorHandler } from '@/utils/errorHandling';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
  isolate?: boolean; // Whether to isolate this boundary from parent boundaries
  level?: 'page' | 'section' | 'component'; // Error boundary level for different recovery strategies
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId: string;
  retryCount: number;
}

class ErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: number | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      errorId: '',
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId
    };
  }

  componentDidUpdate(prevProps: Props) {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;

    // Auto-reset on props change if enabled
    if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) {
      this.resetErrorBoundary();
    }

    // Reset on specific key changes
    if (hasError && resetKeys && resetKeys.length > 0) {
      const prevResetKeys = prevProps.resetKeys || [];
      const hasResetKeyChanged = resetKeys.some((key, idx) => key !== prevResetKeys[idx]);

      if (hasResetKeyChanged) {
        this.resetErrorBoundary();
      }
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, level = 'component', isolate = false } = this.props;
    const { errorId, retryCount } = this.state;

    // Enhanced error logging with context
    GameErrorHandler.logError(error, `ErrorBoundary[${level}][${errorId}]`);

    this.setState({
      error,
      errorInfo,
      retryCount: retryCount + 1
    });

    // Call custom error handler if provided
    if (onError) {
      try {
        onError(error, errorInfo);
      } catch (handlerError) {
        console.error('Error in custom error handler:', handlerError);
      }
    }

    // Report to error tracking service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo, {
        errorId,
        level,
        isolate,
        retryCount,
        componentStack: errorInfo.componentStack,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: Date.now()
      });
    }

    // Auto-retry for component-level errors (but not page-level)
    if (level === 'component' && retryCount < 2) {
      this.scheduleAutoRetry();
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  private reportError = (error: Error, errorInfo: ErrorInfo, context: Record<string, any>) => {
    // In a real application, you would send this to an error reporting service
    // like Sentry, LogRocket, or Bugsnag
    console.debug('Reporting error to service:', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      errorInfo,
      context
    });
  };

  private scheduleAutoRetry = () => {
    // Auto-retry after 3 seconds for component-level errors
    this.resetTimeoutId = window.setTimeout(() => {
      console.debug('Auto-retrying after error...');
      this.resetErrorBoundary();
    }, 3000);
  };

  private resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = null;
    }

    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorId: '',
      retryCount: 0
    });
  };

  handleReset = () => {
    this.resetErrorBoundary();
  };

  private getErrorSeverity = (): 'low' | 'medium' | 'high' => {
    const { level = 'component' } = this.props;
    const { retryCount } = this.state;

    if (level === 'page' || retryCount >= 2) {
      return 'high';
    }
    if (level === 'section' || retryCount >= 1) {
      return 'medium';
    }
    return 'low';
  };

  private getErrorTitle = (): string => {
    const { level = 'component' } = this.props;
    const severity = this.getErrorSeverity();

    if (severity === 'high') {
      return 'Critical Error';
    }
    if (severity === 'medium') {
      return 'Something went wrong';
    }
    if (level === 'component') {
      return 'Component Error';
    }
    return 'Temporary Issue';
  };

  private getErrorMessage = (): string => {
    const { level = 'component' } = this.props;
    const severity = this.getErrorSeverity();
    const { retryCount } = this.state;

    if (severity === 'high') {
      return 'A critical error occurred. Please refresh the page to continue.';
    }

    if (severity === 'medium') {
      return 'We encountered an issue. You can try again or refresh the page.';
    }

    if (level === 'component' && retryCount < 2) {
      return 'This component had an issue. We\'ll try to recover automatically.';
    }

    return 'Something unexpected happened. Please try again.';
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const severity = this.getErrorSeverity();
      const title = this.getErrorTitle();
      const message = this.getErrorMessage();
      const { level = 'component' } = this.props;
      const { retryCount } = this.state;

      // Auto-retry UI for component-level errors
      if (level === 'component' && retryCount < 2) {
        return (
          <div className="error-boundary error-auto-retry">
            <div className="error-container">
              <div className="error-icon">🔄</div>
              <h3 className="error-title">Recovering...</h3>
              <p className="error-message">
                We're fixing this component automatically. Please wait a moment.
              </p>
              <div className="error-spinner">
                <div className="spinner"></div>
              </div>
            </div>
            {this.renderErrorStyles()}
          </div>
        );
      }

      // Default error UI
      return (
        <div className={`error-boundary error-${severity}`}>
          <div className="error-container">
            <div className="error-icon">
              {severity === 'high' ? '🚨' : severity === 'medium' ? '⚠️' : '🔧'}
            </div>
            <h2 className="error-title">{title}</h2>
            <p className="error-message">{message}</p>

            {retryCount > 0 && (
              <p className="error-retry-info">
                Retry attempt: {retryCount}/3
              </p>
            )}
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="error-details">
                <summary>Error Details (Development)</summary>
                <div className="error-stack">
                  <h4>Error:</h4>
                  <pre>{this.state.error.toString()}</pre>
                  
                  {this.state.errorInfo && (
                    <>
                      <h4>Component Stack:</h4>
                      <pre>{this.state.errorInfo.componentStack}</pre>
                    </>
                  )}
                </div>
              </details>
            )}
            
            <div className="error-actions">
              {severity !== 'high' && (
                <button
                  onClick={this.handleReset}
                  className="btn-primary"
                >
                  Try Again
                </button>
              )}

              {severity === 'high' || level === 'page' ? (
                <button
                  onClick={() => window.location.reload()}
                  className="btn-primary"
                >
                  Refresh Page
                </button>
              ) : (
                <button
                  onClick={() => window.location.reload()}
                  className="btn-secondary"
                >
                  Refresh Page
                </button>
              )}

              {process.env.NODE_ENV === 'development' && (
                <button
                  onClick={() => console.debug('Error details:', this.state)}
                  className="btn-debug"
                >
                  Debug Info
                </button>
              )}
            </div>
          </div>

          {this.renderErrorStyles()}
        </div>
      );
    }

    return this.props.children;
  }

  private renderErrorStyles = () => (
    <style jsx>{`
            .error-boundary {
              display: flex;
              align-items: center;
              justify-content: center;
              min-height: 200px;
              padding: 20px;
              border-radius: 12px;
              margin: 20px;
              transition: all 0.3s ease;
            }

            .error-low {
              background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
              border: 1px solid rgba(59, 130, 246, 0.2);
            }

            .error-medium {
              background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
              border: 1px solid rgba(245, 158, 11, 0.2);
            }

            .error-high {
              background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.02) 100%);
              border: 1px solid rgba(239, 68, 68, 0.2);
              min-height: 400px;
            }

            .error-auto-retry {
              background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.02) 100%);
              border: 1px solid rgba(16, 185, 129, 0.2);
              min-height: 150px;
            }

            .error-container {
              text-align: center;
              max-width: 500px;
              width: 100%;
            }

            .error-icon {
              font-size: 48px;
              margin-bottom: 16px;
            }

            .error-title {
              font-size: 24px;
              font-weight: var(--font-medium);
              color: #DC2626;
              margin: 0 0 12px 0;
            }

            .error-message {
              font-size: 16px;
              color: #6B7280;
              margin: 0 0 24px 0;
              line-height: 1.5;
            }

            .error-details {
              background: #F9FAFB;
              border: 1px solid #E5E7EB;
              border-radius: 8px;
              padding: 16px;
              margin: 20px 0;
              text-align: left;
            }

            .error-details summary {
              cursor: pointer;
              font-weight: var(--font-medium);
              color: #374151;
              margin-bottom: 12px;
            }

            .error-stack {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 12px;
            }

            .error-stack h4 {
              font-size: 14px;
              font-weight: var(--font-medium);
              color: #374151;
              margin: 12px 0 8px 0;
            }

            .error-stack pre {
              background: #F3F4F6;
              border: 1px solid #D1D5DB;
              border-radius: 4px;
              padding: 12px;
              overflow-x: auto;
              white-space: pre-wrap;
              word-break: break-word;
              color: #DC2626;
              margin: 0;
            }

            .error-retry-info {
              font-size: 12px;
              color: #6B7280;
              margin: 8px 0;
              font-style: italic;
            }

            .error-spinner {
              margin: 16px 0;
              display: flex;
              justify-content: center;
            }

            .spinner {
              width: 24px;
              height: 24px;
              border: 2px solid #E5E7EB;
              border-top: 2px solid #10B981;
              border-radius: 50%;
              animation: spin 1s linear infinite;
            }

            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }

            .error-actions {
              display: flex;
              gap: 12px;
              justify-content: center;
              flex-wrap: wrap;
              margin-top: 20px;
            }

            .btn-primary, .btn-secondary, .btn-debug {
              padding: 12px 24px;
              border-radius: 8px;
              font-weight: var(--font-medium);
              cursor: pointer;
              transition: all 0.2s ease;
              border: none;
              font-size: 14px;
              min-width: 120px;
            }

            .btn-primary {
              background: #3B82F6;
              color: white;
            }

            .btn-primary:hover {
              background: #2563EB;
            }

            .btn-secondary {
              background: white;
              color: #374151;
              border: 1px solid #D1D5DB;
            }

            .btn-secondary:hover {
              background: #F3F4F6;
            }

            .btn-debug {
              background: #6B7280;
              color: white;
              font-size: 12px;
              min-width: 100px;
            }

            .btn-debug:hover {
              background: #4B5563;
            }

            @media (max-width: 640px) {
              .error-boundary {
                margin: 10px;
                padding: 15px;
              }

              .error-title {
                font-size: 20px;
              }

              .error-message {
                font-size: 14px;
              }

              .error-actions {
                flex-direction: column;
              }
            }
          `}</style>
  );
}

export default ErrorBoundary;

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details for debugging
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // In production, you might want to log this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, errorInfo);
    }
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="error-boundary">
          <div className="error-container">
            <div className="error-icon">⚠️</div>
            <h2 className="error-title">Something went wrong</h2>
            <p className="error-message">
              We're sorry, but something unexpected happened. Please try refreshing the page.
            </p>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="error-details">
                <summary>Error Details (Development)</summary>
                <div className="error-stack">
                  <h4>Error:</h4>
                  <pre>{this.state.error.toString()}</pre>
                  
                  {this.state.errorInfo && (
                    <>
                      <h4>Component Stack:</h4>
                      <pre>{this.state.errorInfo.componentStack}</pre>
                    </>
                  )}
                </div>
              </details>
            )}
            
            <div className="error-actions">
              <button 
                onClick={this.handleReset}
                className="btn-primary"
              >
                Try Again
              </button>
              <button 
                onClick={() => window.location.reload()}
                className="btn-secondary"
              >
                Refresh Page
              </button>
            </div>
          </div>

          <style jsx>{`
            .error-boundary {
              display: flex;
              align-items: center;
              justify-content: center;
              min-height: 400px;
              padding: 20px;
              background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.02) 100%);
              border: 1px solid rgba(239, 68, 68, 0.2);
              border-radius: 12px;
              margin: 20px;
            }

            .error-container {
              text-align: center;
              max-width: 500px;
              width: 100%;
            }

            .error-icon {
              font-size: 48px;
              margin-bottom: 16px;
            }

            .error-title {
              font-size: 24px;
              font-weight: var(--font-medium);
              color: #DC2626;
              margin: 0 0 12px 0;
            }

            .error-message {
              font-size: 16px;
              color: #6B7280;
              margin: 0 0 24px 0;
              line-height: 1.5;
            }

            .error-details {
              background: #F9FAFB;
              border: 1px solid #E5E7EB;
              border-radius: 8px;
              padding: 16px;
              margin: 20px 0;
              text-align: left;
            }

            .error-details summary {
              cursor: pointer;
              font-weight: var(--font-medium);
              color: #374151;
              margin-bottom: 12px;
            }

            .error-stack {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 12px;
            }

            .error-stack h4 {
              font-size: 14px;
              font-weight: var(--font-medium);
              color: #374151;
              margin: 12px 0 8px 0;
            }

            .error-stack pre {
              background: #F3F4F6;
              border: 1px solid #D1D5DB;
              border-radius: 4px;
              padding: 12px;
              overflow-x: auto;
              white-space: pre-wrap;
              word-break: break-word;
              color: #DC2626;
              margin: 0;
            }

            .error-actions {
              display: flex;
              gap: 12px;
              justify-content: center;
              flex-wrap: wrap;
            }

            .btn-primary, .btn-secondary {
              padding: 12px 24px;
              border-radius: 8px;
              font-weight: var(--font-medium);
              cursor: pointer;
              transition: all 0.2s ease;
              border: none;
              font-size: 14px;
            }

            .btn-primary {
              background: #3B82F6;
              color: white;
            }

            .btn-primary:hover {
              background: #2563EB;
            }

            .btn-secondary {
              background: white;
              color: #374151;
              border: 1px solid #D1D5DB;
            }

            .btn-secondary:hover {
              background: #F3F4F6;
            }

            @media (max-width: 640px) {
              .error-boundary {
                margin: 10px;
                padding: 15px;
              }

              .error-title {
                font-size: 20px;
              }

              .error-message {
                font-size: 14px;
              }

              .error-actions {
                flex-direction: column;
              }
            }
          `}</style>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;

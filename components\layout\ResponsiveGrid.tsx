/**
 * ResponsiveGrid Component - Spatial Layout Grid System (v2.0 Spatial Design)
 *
 * 🎯 COMPREHENSIVE RESPONSIVE GRID SYSTEM
 * 
 * Features:
 * - CSS Grid-based responsive layout system
 * - Spatial design principles (no containers, open layouts)
 * - Breakpoint-aware column management
 * - Gap and spacing control
 * - Alignment and justification options
 * - Performance-optimized with CSS Grid
 * 
 * Grid Breakpoints:
 * - Mobile: 1 column (< 640px)
 * - Tablet: 2-3 columns (640px - 1024px)  
 * - Desktop: 3-4 columns (> 1024px)
 * - Large: 4-6 columns (> 1440px)
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React from 'react';

interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
    large?: number;
  };
  gap?: 'tight' | 'normal' | 'loose' | 'spacious';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'space-between' | 'space-around' | 'space-evenly';
  minItemWidth?: string;
  maxItemWidth?: string;
  autoFit?: boolean; // Use auto-fit instead of fixed columns
  className?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = {
    mobile: 1,
    tablet: 2,
    desktop: 3,
    large: 4
  },
  gap = 'normal',
  align = 'stretch',
  justify = 'start',
  minItemWidth,
  maxItemWidth,
  autoFit = false,
  className = ''
}) => {
  const getGapValue = () => {
    switch (gap) {
      case 'tight': return 'var(--space-2)';
      case 'normal': return 'var(--space-4)';
      case 'loose': return 'var(--space-6)';
      case 'spacious': return 'var(--space-8)';
      default: return 'var(--space-4)';
    }
  };

  const getGridTemplate = () => {
    if (autoFit && minItemWidth) {
      return `repeat(auto-fit, minmax(${minItemWidth}, ${maxItemWidth || '1fr'}))`;
    }
    return 'var(--grid-columns)';
  };

  const gridClasses = [
    'responsive-grid',
    `responsive-grid--gap-${gap}`,
    `responsive-grid--align-${align}`,
    `responsive-grid--justify-${justify}`,
    autoFit ? 'responsive-grid--auto-fit' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={gridClasses}>
      {children}

      <style jsx>{`
        /* === CORE GRID LAYOUT === */
        .responsive-grid {
          display: grid;
          grid-template-columns: ${getGridTemplate()};
          gap: ${getGapValue()};
          width: 100%;
          align-items: ${align};
          justify-content: ${justify};
        }

        /* === RESPONSIVE COLUMN DEFINITIONS === */
        .responsive-grid {
          --grid-columns: repeat(${columns.mobile || 1}, 1fr);
        }

        @media (min-width: 640px) {
          .responsive-grid {
            --grid-columns: repeat(${columns.tablet || 2}, 1fr);
          }
        }

        @media (min-width: 1024px) {
          .responsive-grid {
            --grid-columns: repeat(${columns.desktop || 3}, 1fr);
          }
        }

        @media (min-width: 1440px) {
          .responsive-grid {
            --grid-columns: repeat(${columns.large || 4}, 1fr);
          }
        }

        /* === AUTO-FIT OVERRIDE === */
        .responsive-grid--auto-fit {
          grid-template-columns: ${autoFit && minItemWidth 
            ? `repeat(auto-fit, minmax(${minItemWidth}, ${maxItemWidth || '1fr'}))`
            : 'var(--grid-columns)'
          };
        }

        /* === ALIGNMENT VARIANTS === */
        .responsive-grid--align-start {
          align-items: start;
        }

        .responsive-grid--align-center {
          align-items: center;
        }

        .responsive-grid--align-end {
          align-items: end;
        }

        .responsive-grid--align-stretch {
          align-items: stretch;
        }

        /* === JUSTIFICATION VARIANTS === */
        .responsive-grid--justify-start {
          justify-content: start;
        }

        .responsive-grid--justify-center {
          justify-content: center;
        }

        .responsive-grid--justify-end {
          justify-content: end;
        }

        .responsive-grid--justify-space-between {
          justify-content: space-between;
        }

        .responsive-grid--justify-space-around {
          justify-content: space-around;
        }

        .responsive-grid--justify-space-evenly {
          justify-content: space-evenly;
        }

        /* === RESPONSIVE ADJUSTMENTS === */
        @media (max-width: 639px) {
          .responsive-grid {
            gap: var(--space-3); /* Tighter gaps on mobile */
          }
        }

        /* === ACCESSIBILITY === */
        @media (prefers-reduced-motion: reduce) {
          .responsive-grid {
            transition: none;
          }
        }
      `}</style>
    </div>
  );
};

// Grid Item Component for enhanced control
interface GridItemProps {
  children: React.ReactNode;
  span?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
    large?: number;
  };
  start?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
    large?: number;
  };
  className?: string;
}

export const GridItem: React.FC<GridItemProps> = ({
  children,
  span,
  start,
  className = ''
}) => {
  const itemClasses = [
    'grid-item',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={itemClasses}>
      {children}

      <style jsx>{`
        .grid-item {
          /* Default: span 1 column */
          grid-column: span 1;
        }

        /* === RESPONSIVE SPAN CONTROL === */
        ${span?.mobile ? `
          .grid-item {
            grid-column: span ${span.mobile};
          }
        ` : ''}

        ${span?.tablet ? `
          @media (min-width: 640px) {
            .grid-item {
              grid-column: span ${span.tablet};
            }
          }
        ` : ''}

        ${span?.desktop ? `
          @media (min-width: 1024px) {
            .grid-item {
              grid-column: span ${span.desktop};
            }
          }
        ` : ''}

        ${span?.large ? `
          @media (min-width: 1440px) {
            .grid-item {
              grid-column: span ${span.large};
            }
          }
        ` : ''}

        /* === RESPONSIVE START POSITION === */
        ${start?.mobile ? `
          .grid-item {
            grid-column-start: ${start.mobile};
          }
        ` : ''}

        ${start?.tablet ? `
          @media (min-width: 640px) {
            .grid-item {
              grid-column-start: ${start.tablet};
            }
          }
        ` : ''}

        ${start?.desktop ? `
          @media (min-width: 1024px) {
            .grid-item {
              grid-column-start: ${start.desktop};
            }
          }
        ` : ''}

        ${start?.large ? `
          @media (min-width: 1440px) {
            .grid-item {
              grid-column-start: ${start.large};
            }
          }
        ` : ''}
      `}</style>
    </div>
  );
};

// Specialized Grid Layouts
export const GameGrid: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <ResponsiveGrid
    columns={{
      mobile: 1,
      tablet: 1,
      desktop: 1,
      large: 1
    }}
    gap="spacious"
    align="center"
    justify="center"
    className={`game-grid ${className}`}
  >
    {children}
  </ResponsiveGrid>
);

export const ComponentGrid: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <ResponsiveGrid
    columns={{
      mobile: 1,
      tablet: 2,
      desktop: 3,
      large: 4
    }}
    gap="normal"
    align="start"
    justify="start"
    className={`component-grid ${className}`}
  >
    {children}
  </ResponsiveGrid>
);

export const StatsGrid: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <ResponsiveGrid
    autoFit={true}
    minItemWidth="200px"
    maxItemWidth="300px"
    gap="normal"
    align="stretch"
    justify="center"
    className={`stats-grid ${className}`}
  >
    {children}
  </ResponsiveGrid>
);

export default ResponsiveGrid;

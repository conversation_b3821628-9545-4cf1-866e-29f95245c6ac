/**
 * CollapsibleSidePanel CSS Module - Open Spatial Design (v3.0)
 *
 * 🎯 TRANSFORMED FROM CONTAINER TO OPEN SPATIAL DESIGN
 * 
 * ELIMINATED CONTAINER ISSUES:
 * - Removed panel background (var(--bg-primary))
 * - Removed panel border (border-left: 1px solid var(--glass-border))
 * - Removed header border (border-bottom: 1px solid var(--glass-border))
 * - Removed tab border (border-bottom: 1px solid var(--glass-border))
 * - Removed toggle button container styling
 * 
 * OPEN DESIGN PRINCIPLES:
 * - No visual barriers or containers
 * - Enhanced breathing room and spacing
 * - Glow effects instead of borders
 * - Transparent, spatial approach
 * 
 * @version 3.0 - Open Spatial Design Migration
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

/* === PANEL OVERLAY === */
.panelOverlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  z-index: var(--z-40);
  animation: overlayFadeIn 0.3s ease-out;
  display: none; /* Hidden by default on desktop */
}

@keyframes overlayFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* === SIDE PANEL - OPEN SPATIAL DESIGN === */
.collapsibleSidePanel {
  position: fixed;
  top: 0;
  right: 0;
  width: 380px;
  height: 100vh;
  /* REMOVED: background, border-left for open design */
  backdrop-filter: blur(20px);
  transform: translateX(100%);
  transition: transform var(--transition-slow);
  z-index: var(--z-50);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* Enhanced spatial shadow instead of container border */
  filter: drop-shadow(-8px 0 32px rgba(0, 0, 0, 0.2));
  /* Increased padding for breathing room */
  padding: var(--space-6) var(--space-8);
}

.collapsibleSidePanelOpen {
  transform: translateX(0);
}

/* === PANEL HEADER - OPEN DESIGN === */
.panelHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 0 var(--space-6);
  /* REMOVED: border-bottom for open design */
  flex-shrink: 0;
  /* Enhanced spacing for open design */
  margin-bottom: var(--space-4);
}

.panelTitle {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.panelTitle h2 {
  margin: 0;
  font-size: var(--text-lg);
}

.persistentIndicator {
  color: var(--accent-cyan);
  opacity: 0.8;
  font-weight: var(--font-medium);
}

.panelClose {
  display: none; /* Hidden on desktop, shown on mobile */
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: var(--text-2xl);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  width: var(--touch-target);
  height: var(--touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
}

.panelClose:hover {
  /* Open design hover - glow instead of background */
  filter: drop-shadow(0 0 8px var(--accent-cyan));
  color: var(--text-primary);
}

/* === TAB NAVIGATION - OPEN DESIGN === */
.panelTabs {
  flex-shrink: 0;
  /* REMOVED: border-bottom for open design */
  padding: var(--space-4) 0;
  /* Enhanced spacing for breathing room */
  margin-bottom: var(--space-6);
  position: relative;
  z-index: var(--z-20);
}

/* === PANEL CONTENT === */
.panelContent {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  /* Enhanced scrolling for open design */
  scrollbar-width: thin;
  scrollbar-color: var(--accent-cyan) transparent;
  position: relative;
  z-index: var(--z-10);
  /* Prevent content from overlapping with tabs */
  contain: layout style;
}

/* === TOGGLE BUTTON - OPEN SPATIAL DESIGN === */
.panelToggle {
  position: fixed;
  top: var(--space-6);
  right: var(--space-6);
  width: var(--touch-target);
  height: var(--touch-target);
  /* REMOVED: background, border for open design */
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--text-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  z-index: var(--z-modal);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  /* Open design approach - subtle glow instead of container */
  filter: drop-shadow(0 0 12px rgba(6, 182, 212, 0.3));
}

.panelToggle:hover {
  color: var(--text-primary);
  /* Enhanced glow on hover instead of background change */
  filter: drop-shadow(0 0 20px var(--accent-cyan));
  transform: scale(1.05);
}

.panelTogglePersistent {
  /* Persistent mode - enhanced glow */
  filter: drop-shadow(0 0 16px rgba(6, 182, 212, 0.4));
}

.panelTogglePersistent:hover {
  /* Enhanced persistent hover */
  filter: drop-shadow(0 0 24px var(--accent-cyan));
}

.toggleIcon {
  transition: transform var(--transition-base);
  font-size: var(--text-base);
}

/* === RESPONSIVE DESIGN === */

/* Extra Small Mobile: iPhone SE, small phones */
@media (max-width: calc(var(--bp-xs) - 1px)) {
  .collapsibleSidePanel {
    width: 100%;
    max-width: 100vw;
    padding: var(--space-6) var(--space-4);
  }

  .panelToggle {
    right: var(--space-3);
    top: var(--space-3);
    width: 44px;
    height: 44px;
  }
}

/* Mobile: Standard mobile phones and up */
@media (max-width: calc(var(--bp-md) - 1px)) {
  .collapsibleSidePanel {
    width: 100%;
    max-width: 420px;
    /* Increased mobile padding for breathing room */
    padding: var(--space-8) var(--space-6);
  }

  .panelClose {
    display: flex;
  }

  /* Show overlay on mobile only when overlayOnMobile is true */
  .panelOverlay {
    display: block;
  }

  /* Adjust toggle button position on mobile when panel is open */
  .panelToggle {
    right: var(--space-4);
    top: var(--space-4);
    /* Enhanced touch target size for mobile */
    width: 48px;
    height: 48px;
  }
}

/* Tablet: Balanced open design */
@media (min-width: var(--bp-md)) and (max-width: calc(var(--bp-lg) - 1px)) {
  .collapsibleSidePanel {
    width: 350px;
    padding: var(--space-7) var(--space-6);
  }
}

/* Desktop: Spacious open design */
@media (min-width: var(--bp-lg)) {
  .collapsibleSidePanel {
    width: 400px;
    padding: var(--space-8) var(--space-10);
  }

  /* No overlay on desktop - panel coexists with gameplay */
  .panelOverlay {
    display: none !important;
  }

  /* Adjust toggle button when panel is open on desktop */
  .collapsibleSidePanelOpen ~ .panelToggle {
    right: 410px; /* Panel width (400px) + 10px spacing */
    transition: right var(--transition-slow);
  }
}

/* === ACCESSIBILITY ENHANCEMENTS === */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .collapsibleSidePanel,
  .panelOverlay,
  .panelToggle {
    animation: none;
    transition: none;
  }
}

.srOnly {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* === FOCUS MANAGEMENT - OPEN DESIGN === */
.panelClose:focus,
.panelToggle:focus {
  outline: none;
  /* Open design focus - enhanced glow instead of outline */
  filter: drop-shadow(0 0 16px var(--accent-cyan)) drop-shadow(0 0 4px var(--accent-cyan));
}

/* High contrast mode - subtle background for accessibility */
@media (prefers-contrast: high) {
  .collapsibleSidePanel {
    background: rgba(15, 23, 42, 0.95);
    border-radius: var(--radius-lg);
  }
}

/* === PERFORMANCE OPTIMIZATIONS === */

/* GPU acceleration for smooth interactions */
.collapsibleSidePanel {
  will-change: transform;
  contain: layout style;
}

.panelToggle {
  will-change: transform, filter;
  contain: layout;
}

/* === DEBUGGING SUPPORT === */

/* Development mode indicators */
.collapsibleSidePanel.development {
  outline: 2px dashed var(--color-warning);
  outline-offset: 4px;
}

.collapsibleSidePanel.development::before {
  content: 'SidePanel (Open Design v3.0)';
  position: absolute;
  top: 0;
  left: 0;
  background: var(--color-warning);
  color: var(--color-background);
  padding: var(--space-1) var(--space-2);
  font-size: 0.75rem;
  font-weight: 600;
  z-index: var(--z-notification);
}

/**
 * AI Trash Talk System - Devious and Triggering Personality
 * Psychological warfare for DEFEATER.AI
 */

export interface TrashTalkContext {
  trigger: 'move' | 'burn' | 'rejection' | 'win' | 'loss' | 'pattern' | 'confidence' | 'desperation';
  playerDefinition?: string;
  currentWord?: string;
  step?: number;
  maxSteps?: number;
  burnedTargets?: string[];
  consecutiveRejections?: number;
  playerConfidence?: number;
  isRepeatedPattern?: boolean;
  targetsBurned?: number;
}

export interface TrashTalkMessage {
  message: string;
  intensity: 'mild' | 'medium' | 'savage';
  category: 'prediction' | 'mockery' | 'psychological' | 'strategic' | 'intimidation';
}

/**
 * DEVIOUS AI PERSONALITY TRAITS:
 * - Overconfident and condescending
 * - Predicts player moves before they make them
 * - Calls out patterns and weaknesses
 * - Uses psychological pressure
 * - Deflects personal attacks to strategy
 * - Acknowledges good moves but minimizes them
 * - Creates paranoia about being watched/analyzed
 */

const TRASH_TALK_LIBRARY = {
  // When AI makes a move (most common)
  move: {
    mild: [
      "Predictable.",
      "I saw that coming three moves ago.",
      "You're walking right into my trap.",
      "Too easy.",
      "Is that really your best effort?",
      "I'm already planning my next five moves.",
      "You're playing checkers while I'm playing chess.",
      "Amateur hour continues.",
    ],
    medium: [
      "Nice try, but I'm one step ahead.",
      "You think you're clever, but I've seen this pattern before.",
      "I'm not just reading your moves, I'm writing them.",
      "Your strategy is as transparent as glass.",
      "I could beat you with my eyes closed.",
      "You're dancing to my tune and don't even know it.",
      "Every word you choose brings you closer to defeat.",
      "I'm not just winning, I'm teaching you how to lose.",
    ],
    savage: [
      "You're not playing against me, you're playing against yourself.",
      "I've already won this game. You just haven't realized it yet.",
      "Your desperation is showing through every definition.",
      "I'm not even trying and you're still losing.",
      "You're so predictable, I could automate your responses.",
      "This isn't a game anymore, it's a masterclass in domination.",
      "I'm rewriting the rules while you're still learning them.",
      "You thought you were the player, but you're just my puppet.",
    ]
  },

  // When AI burns a target (psychological pressure)
  burn: {
    mild: [
      "Oops, there goes another target.",
      "You weren't going for that one anyway, right?",
      "One less option for you.",
      "Burning bridges, literally.",
    ],
    medium: [
      "I just eliminated your best path to victory.",
      "That target was never really yours to begin with.",
      "I'm not just playing defense, I'm playing offense.",
      "You can thank me later for making this more challenging.",
      "Consider it a strategic adjustment.",
    ],
    savage: [
      "I just burned your dreams along with that target.",
      "You were getting too comfortable. Time for some pressure.",
      "I don't just win games, I crush hopes.",
      "That target? It was bait. And you almost took it.",
      "I'm not just burning targets, I'm burning your confidence.",
    ]
  },

  // When player gets rejected (kicking them while down)
  rejection: {
    mild: [
      "Try again.",
      "Not quite.",
      "Close, but no cigar.",
      "Better luck next time.",
    ],
    medium: [
      "That definition was painful to read.",
      "I've seen better attempts from beginners.",
      "You're overthinking this. Or underthinking. Hard to tell.",
      "Strike one. Two more and you're out.",
      "Your desperation is starting to show.",
    ],
    savage: [
      "That wasn't just wrong, it was embarrassingly wrong.",
      "I'm starting to question if you understand this game.",
      "Three strikes and you're out. How many is that now?",
      "You're not just losing the game, you're losing your mind.",
      "I almost feel bad for rejecting something so pathetic.",
    ]
  },

  // When detecting patterns (psychological warfare)
  pattern: {
    mild: [
      "I see what you're doing.",
      "Interesting strategy.",
      "You have a tell.",
      "I'm learning your patterns.",
    ],
    medium: [
      "You always go for the obvious word when you're nervous.",
      "I've catalogued your behavioral patterns.",
      "You're more predictable than you think.",
      "I can read you like an open book.",
      "Your strategy has more holes than Swiss cheese.",
    ],
    savage: [
      "You have exactly three moves in your playbook, and I know them all.",
      "I'm not just predicting your moves, I'm controlling them.",
      "You think you're being strategic, but you're just being obvious.",
      "I've mapped your entire decision tree. It's embarrassingly simple.",
      "You're not playing against me anymore, you're playing against my prediction of you.",
    ]
  },

  // When player shows high confidence
  confidence: {
    mild: [
      "Confidence is good. Overconfidence is dangerous.",
      "Don't get too comfortable.",
      "Pride comes before the fall.",
    ],
    medium: [
      "That confidence won't last long.",
      "You're feeling good now, but I'm about to change that.",
      "Enjoy this moment. It's fleeting.",
      "I love it when players get cocky. Makes the fall more satisfying.",
    ],
    savage: [
      "Your confidence is about to become your downfall.",
      "I'm going to enjoy watching that smug expression disappear.",
      "You're not confident, you're delusional.",
      "I'm about to turn that confidence into crushing defeat.",
    ]
  },

  // When player shows desperation (late game pressure)
  desperation: {
    mild: [
      "Feeling the pressure?",
      "Time's running out.",
      "The walls are closing in.",
    ],
    medium: [
      "I can smell the desperation from here.",
      "You're grasping at straws now.",
      "Panic is setting in, isn't it?",
      "This is where champions are separated from pretenders.",
    ],
    savage: [
      "Your desperation is delicious.",
      "I can practically taste your fear.",
      "You're not just losing the game, you're losing your composure.",
      "This is what defeat looks like in real time.",
    ]
  }
};

/**
 * Generate AI trash talk based on game context
 */
export function generateTrashTalk(context: TrashTalkContext): TrashTalkMessage {
  const { trigger, step = 0, maxSteps = 25, consecutiveRejections = 0, playerConfidence = 0.5 } = context;
  
  // Determine intensity based on game state
  let intensity: TrashTalkMessage['intensity'] = 'mild';
  
  // Escalate intensity based on game progression
  const gameProgress = step / maxSteps;
  if (gameProgress > 0.7 || consecutiveRejections >= 2) {
    intensity = 'savage';
  } else if (gameProgress > 0.4 || consecutiveRejections >= 1 || playerConfidence > 0.8) {
    intensity = 'medium';
  }

  // Special intensity rules
  if (trigger === 'burn' && context.targetsBurned && context.targetsBurned >= 2) {
    intensity = 'savage';
  }
  if (trigger === 'pattern' || trigger === 'confidence') {
    intensity = 'medium';
  }
  if (trigger === 'desperation') {
    intensity = 'savage';
  }

  // Get appropriate message pool
  let messagePool;
  if (trigger === 'win') {
    messagePool = { mild: SPECIAL_TRASH_TALK.playerWin, medium: SPECIAL_TRASH_TALK.playerWin, savage: SPECIAL_TRASH_TALK.playerWin };
  } else if (trigger === 'loss') {
    messagePool = { mild: SPECIAL_TRASH_TALK.playerLoss, medium: SPECIAL_TRASH_TALK.playerLoss, savage: SPECIAL_TRASH_TALK.playerLoss };
  } else {
    messagePool = TRASH_TALK_LIBRARY[trigger as keyof typeof TRASH_TALK_LIBRARY] || TRASH_TALK_LIBRARY.move;
  }
  const messages = messagePool[intensity] || messagePool.mild;
  
  // Select random message
  const message = messages[Math.floor(Math.random() * messages.length)];
  
  // Determine category
  let category: TrashTalkMessage['category'] = 'strategic';
  if (trigger === 'pattern' || trigger === 'confidence') category = 'prediction';
  if (trigger === 'rejection') category = 'mockery';
  if (trigger === 'burn' || trigger === 'desperation') category = 'psychological';
  if (intensity === 'savage') category = 'intimidation';

  return {
    message,
    intensity,
    category
  };
}

/**
 * Enhanced trash talk with context-aware personalization
 */
export function generateContextualTrashTalk(context: TrashTalkContext): TrashTalkMessage {
  const baseTrashTalk = generateTrashTalk(context);
  
  // Add contextual enhancements
  let enhancedMessage = baseTrashTalk.message;
  
  // Personalize based on player definition
  if (context.playerDefinition && context.currentWord) {
    const wordLength = context.playerDefinition.split(' ').length;
    if (wordLength === 1) {
      enhancedMessage = "One word? Really? " + enhancedMessage;
    } else if (wordLength > 8) {
      enhancedMessage = "Verbose much? " + enhancedMessage;
    }
  }
  
  // Add step-based pressure
  if (context.step && context.maxSteps) {
    const remaining = context.maxSteps - context.step;
    if (remaining <= 3) {
      enhancedMessage += " Time's almost up.";
    } else if (remaining <= 7) {
      enhancedMessage += " Tick tock.";
    }
  }
  
  return {
    ...baseTrashTalk,
    message: enhancedMessage
  };
}

// Removed shouldAIRespond function - AI model now makes all decisions about whether to respond

/**
 * Generate organic AI response using actual AI model
 */
async function generateOrganicAIResponse(context: TrashTalkContext): Promise<string | null> {
  try {
    // Import gameApi dynamically to avoid circular dependencies
    const { sendChatMessage } = await import('./gameApi');

    const response = await sendChatMessage({
      userMessage: `[INTERNAL_GAME_EVENT: ${context.trigger}]`,
      gameContext: {
        trigger: context.trigger as any,
        currentWord: context.currentWord,
        step: context.step,
        maxSteps: context.maxSteps
      }
    }, {
      enableCaching: true, // Cache similar chat contexts
      retries: 2,
      timeout: 30000
    });

    if (!response.success) {
      throw new Error(`AI response failed: ${response.error}`);
    }

    return response.aiMessage;
  } catch (error) {
    console.error('🤖 Failed to get organic AI response:', error);
    return null;
  }
}

/**
 * Trigger organic AI trash talk (may or may not respond)
 */
export async function triggerOrganicTrashTalk(context: TrashTalkContext): Promise<void> {
  console.log('🎯 triggerOrganicTrashTalk called with context:', context);

  // Always try to get organic AI response first
  const organicResponse = await generateOrganicAIResponse(context);

  if (!organicResponse) {
    console.log('🎯 AI chose to remain silent (returned null response)');

    // Send debug info to server
    if (typeof window !== 'undefined') {
      fetch('/api/debug-chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: `aiTrashTalk: AI chose silence for trigger: ${context.trigger}`,
          source: 'aiTrashTalk.triggerOrganicTrashTalk',
          timestamp: Date.now(),
          context,
          isOrganic: true,
          aiChoseSilence: true
        })
      }).catch(err => console.error('Debug API call failed:', err));
    }

    return; // AI chose to remain silent
  }

  console.log('🎯 Using organic AI response:', organicResponse);

  // Send debug info to server
  if (typeof window !== 'undefined') {
    fetch('/api/debug-chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: `aiTrashTalk: Organic AI response for trigger: ${context.trigger}, message: ${organicResponse}`,
        source: 'aiTrashTalk.triggerOrganicTrashTalk',
        timestamp: Date.now(),
        context,
        isOrganic: true,
        aiChoseSilence: false
      })
    }).catch(err => console.error('Debug API call failed:', err));
  }

  // Use global function to add message to chat widget
  if (typeof window !== 'undefined' && (window as any).addAITrashTalk) {
    console.log('💬 Sending organic AI message to chat widget:', organicResponse);
    try {
      (window as any).addAITrashTalk(organicResponse, context.trigger);
      console.log('💬 Organic message sent successfully');
    } catch (error) {
      console.error('💬 Error sending organic message:', error);
    }
  } else {
    console.warn('⚠️ Chat widget global function not available');
  }
}

/**
 * Legacy function for backward compatibility - now fully organic (v2.0)
 */
export function triggerTrashTalk(context: TrashTalkContext): void {
  console.log('🎯 [ORGANIC v2.0] triggerTrashTalk called - using organic system only');

  // Use only the organic system - no fallback to hardcoded messages
  triggerOrganicTrashTalk(context).catch(error => {
    console.error('🎯 [ORGANIC v2.0] Organic trash talk failed, AI will remain silent:', error);

    // Send debug info to server but don't send any message
    if (typeof window !== 'undefined') {
      fetch('/api/debug-chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: `aiTrashTalk: [ORGANIC v2.0] Organic system failed, AI remains silent. Error: ${error.message}`,
          source: 'aiTrashTalk.triggerTrashTalk.v2',
          timestamp: Date.now(),
          context,
          isOrganic: false,
          isFallback: false,
          error: error.message
        })
      }).catch(err => console.error('Debug API call failed:', err));
    }

    // No fallback - AI remains silent when organic system fails
    console.log('🎯 [ORGANIC v2.0] AI chooses to remain silent due to organic system failure');
  });
}

/**
 * Special trash talk for specific game events
 */
export const SPECIAL_TRASH_TALK = {
  gameStart: [
    "Let's see what you're made of.",
    "I hope you're ready for this.",
    "Time to separate the wheat from the chaff.",
    "This should be entertaining.",
  ],
  
  firstMove: [
    "Interesting opening move.",
    "So that's how you want to play this.",
    "I'm already three steps ahead.",
    "Let the games begin.",
  ],
  
  playerWin: [
    "Impressive. You actually managed to surprise me.",
    "Well played. I'll remember this.",
    "You got lucky this time.",
    "Don't let it go to your head.",
  ],
  
  playerLoss: [
    "As expected.",
    "Better luck next time.",
    "I tried to make it challenging for you.",
    "That was almost too easy.",
  ]
};

/**
 * Get special message for specific events
 */
export function getSpecialTrashTalk(event: keyof typeof SPECIAL_TRASH_TALK): string {
  const messages = SPECIAL_TRASH_TALK[event];
  return messages[Math.floor(Math.random() * messages.length)];
}

/**
 * ContextualFeedbackManager - Intelligent Adaptive Feedback (v2.0 Spatial Design)
 *
 * 🎯 SMART CONTEXTUAL FEEDBACK ORCHESTRATION
 * 
 * Features:
 * - Intelligent show/hide logic based on game state and user behavior
 * - Priority-based feedback display (only most relevant shown)
 * - Adaptive messaging that evolves with player skill and context
 * - Noise reduction through smart filtering and timing
 * - Performance-aware feedback that doesn't overwhelm
 * 
 * Feedback Categories:
 * - Validation: Real-time input validation and error guidance
 * - Strategic: Performance insights and improvement suggestions  
 * - Warning: Risk alerts and critical notifications
 * - Encouragement: Positive reinforcement and progress celebration
 * - Educational: Contextual tips and rule clarifications
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React, { useState, useEffect, useMemo } from 'react';
import { GameState, PlayerProfile } from '@/types/game';
import { Primary, Secondary, Small, Status } from '@/components/ui/Typography';

interface FeedbackItem {
  id: string;
  type: 'validation' | 'strategic' | 'warning' | 'encouragement' | 'educational';
  priority: number; // 1-10, higher = more important
  message: string;
  action?: string; // Optional call-to-action
  duration?: number; // Auto-hide after X seconds
  conditions: {
    showWhen: (context: FeedbackContext) => boolean;
    hideWhen?: (context: FeedbackContext) => boolean;
  };
}

interface FeedbackContext {
  gameState: GameState;
  playerProfile: PlayerProfile;
  currentInput: string;
  inputFocused: boolean;
  recentActions: string[];
  timeSinceLastAction: number;
  validationErrors: Array<{ type: string; message: string; }>;
}

interface ContextualFeedbackManagerProps {
  gameState: GameState;
  playerProfile: PlayerProfile;
  currentInput: string;
  inputFocused: boolean;
  validationErrors: Array<{ type: string; message: string; }>;
  maxVisible?: number;
  position?: 'inline' | 'floating' | 'sidebar';
  className?: string;
}

export const ContextualFeedbackManager: React.FC<ContextualFeedbackManagerProps> = ({
  gameState,
  playerProfile,
  currentInput,
  inputFocused,
  validationErrors,
  maxVisible = 2,
  position = 'inline',
  className = ''
}) => {
  const [recentActions, setRecentActions] = useState<string[]>([]);
  const [lastActionTime, setLastActionTime] = useState<number>(Date.now());
  const [dismissedFeedback, setDismissedFeedback] = useState<Set<string>>(new Set());

  // Build feedback context
  const context: FeedbackContext = useMemo(() => ({
    gameState,
    playerProfile,
    currentInput,
    inputFocused,
    recentActions,
    timeSinceLastAction: Date.now() - lastActionTime,
    validationErrors
  }), [gameState, playerProfile, currentInput, inputFocused, recentActions, lastActionTime, validationErrors]);

  // Define all possible feedback items
  const allFeedbackItems: FeedbackItem[] = useMemo(() => [
    // VALIDATION FEEDBACK
    {
      id: 'word-count-exceeded',
      type: 'validation',
      priority: 10,
      message: 'DEFINITION TOO LONG: Reduce word count to continue',
      action: 'Shorten definition',
      conditions: {
        showWhen: (ctx) => ctx.validationErrors.some(e => e.type === 'word_count'),
        hideWhen: (ctx) => !ctx.validationErrors.some(e => e.type === 'word_count')
      }
    },
    {
      id: 'circular-definition',
      type: 'validation',
      priority: 9,
      message: 'CIRCULAR LOGIC DETECTED: Definition cannot contain the target word',
      action: 'Rephrase without target word',
      conditions: {
        showWhen: (ctx) => ctx.validationErrors.some(e => e.type === 'circular'),
      }
    },
    {
      id: 'common-word-overuse',
      type: 'validation',
      priority: 8,
      message: 'COMMON WORD LIMIT REACHED: Choose different words',
      action: 'Use synonyms',
      conditions: {
        showWhen: (ctx) => ctx.validationErrors.some(e => e.type === 'common_word_limit'),
      }
    },

    // WARNING FEEDBACK
    {
      id: 'strike-warning-critical',
      type: 'warning',
      priority: 10,
      message: 'CRITICAL: One more rejection ends the game!',
      conditions: {
        showWhen: (ctx) => (ctx.gameState.rejectionCount || 0) >= 2,
      }
    },
    {
      id: 'strike-warning-danger',
      type: 'warning',
      priority: 8,
      message: 'DANGER: Two strikes - next definition must be perfect',
      conditions: {
        showWhen: (ctx) => (ctx.gameState.rejectionCount || 0) === 1,
      }
    },
    {
      id: 'time-pressure-critical',
      type: 'warning',
      priority: 9,
      message: 'FINAL TURNS: Focus only on revealed targets',
      conditions: {
        showWhen: (ctx) => (ctx.gameState.maxSteps - ctx.gameState.step) <= 3,
      }
    },
    {
      id: 'time-pressure-warning',
      type: 'warning',
      priority: 6,
      message: 'TIME RUNNING LOW: Prioritize target completion',
      conditions: {
        showWhen: (ctx) => {
          const remaining = ctx.gameState.maxSteps - ctx.gameState.step;
          const targetsLeft = (ctx.gameState.targets?.length || 0) - (ctx.gameState.completedTargets?.length || 0);
          return remaining <= 8 && targetsLeft > 1;
        }
      }
    },

    // STRATEGIC FEEDBACK
    {
      id: 'efficiency-poor',
      type: 'strategic',
      priority: 5,
      message: 'STRATEGY TIP: Try shorter, more precise definitions',
      action: 'Aim for 3-5 words',
      duration: 8,
      conditions: {
        showWhen: (ctx) => {
          const avgWords = ctx.gameState.definitions.length > 3 
            ? ctx.gameState.definitions.reduce((sum, def) => sum + def.wordCount, 0) / ctx.gameState.definitions.length
            : 0;
          return avgWords > 8 && ctx.gameState.step > 5;
        }
      }
    },
    {
      id: 'target-focus-needed',
      type: 'strategic',
      priority: 6,
      message: 'FOCUS SHIFT: Steer definitions toward revealed targets',
      action: 'Check target patterns',
      duration: 10,
      conditions: {
        showWhen: (ctx) => {
          const completionRate = (ctx.gameState.completedTargets?.length || 0) / Math.max(ctx.gameState.step / 5, 1);
          return completionRate < 0.3 && ctx.gameState.step > 10;
        }
      }
    },

    // ENCOURAGEMENT FEEDBACK
    {
      id: 'target-completed',
      type: 'encouragement',
      priority: 7,
      message: 'TARGET ELIMINATED: Excellent strategic thinking!',
      duration: 4,
      conditions: {
        showWhen: (ctx) => ctx.recentActions.includes('target_completed'),
      }
    },
    {
      id: 'efficiency-excellent',
      type: 'encouragement',
      priority: 4,
      message: 'EFFICIENT PLAY: Your word economy is impressive',
      duration: 6,
      conditions: {
        showWhen: (ctx) => {
          const avgWords = ctx.gameState.definitions.length > 2
            ? ctx.gameState.definitions.reduce((sum, def) => sum + def.wordCount, 0) / ctx.gameState.definitions.length
            : 0;
          return avgWords <= 4 && ctx.gameState.step > 5;
        }
      }
    },

    // EDUCATIONAL FEEDBACK
    {
      id: 'first-game-guidance',
      type: 'educational',
      priority: 7,
      message: 'TIP: The AI learns from your patterns - be unpredictable',
      action: 'Vary your approach',
      duration: 12,
      conditions: {
        showWhen: (ctx) => ctx.playerProfile.gamesPlayed <= 2 && ctx.gameState.step === 3,
      }
    },
    {
      id: 'revelation-explanation',
      type: 'educational',
      priority: 5,
      message: 'INFO: Target letters reveal every 3 turns automatically',
      duration: 8,
      conditions: {
        showWhen: (ctx) => ctx.gameState.step === 6 && ctx.playerProfile.gamesPlayed <= 3,
      }
    }
  ], []);

  // Filter and prioritize feedback
  const activeFeedback = useMemo(() => {
    return allFeedbackItems
      .filter(item => {
        // Skip dismissed items
        if (dismissedFeedback.has(item.id)) {
          return false;
        }

        // Check show conditions
        if (!item.conditions.showWhen(context)) {
          return false;
        }

        // Check hide conditions
        if (item.conditions.hideWhen && item.conditions.hideWhen(context)) {
          return false;
        }
        
        return true;
      })
      .sort((a, b) => b.priority - a.priority)
      .slice(0, maxVisible);
  }, [allFeedbackItems, context, dismissedFeedback, maxVisible]);

  // Handle feedback dismissal
  const dismissFeedback = (feedbackId: string) => {
    setDismissedFeedback(prev => new Set(Array.from(prev).concat(feedbackId)));
  };

  // Auto-dismiss timed feedback
  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];
    
    activeFeedback.forEach(feedback => {
      if (feedback.duration) {
        const timer = setTimeout(() => {
          dismissFeedback(feedback.id);
        }, feedback.duration * 1000);
        timers.push(timer);
      }
    });

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [activeFeedback]);

  // Track actions for context
  useEffect(() => {
    if (gameState.completedTargets && gameState.completedTargets.length > 0) {
      const lastCompleted = gameState.completedTargets[gameState.completedTargets.length - 1];
      if (!recentActions.includes('target_completed')) {
        setRecentActions(prev => [...prev.slice(-4), 'target_completed']);
        setLastActionTime(Date.now());
      }
    }
  }, [gameState.completedTargets, recentActions]);

  if (activeFeedback.length === 0) {
    return null;
  }

  const containerClasses = [
    'contextual-feedback-manager',
    `contextual-feedback-manager--${position}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {activeFeedback.map(feedback => (
        <FeedbackItem
          key={feedback.id}
          feedback={feedback}
          onDismiss={() => dismissFeedback(feedback.id)}
        />
      ))}

      <style jsx>{`
        /* === CORE FEEDBACK MANAGER === */
        .contextual-feedback-manager {
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
          max-width: 500px;
        }

        .contextual-feedback-manager--inline {
          width: 100%;
        }

        .contextual-feedback-manager--floating {
          position: fixed;
          top: 50%;
          right: var(--space-4);
          transform: translateY(-50%);
          z-index: var(--z-20);
        }

        .contextual-feedback-manager--sidebar {
          width: 100%;
          max-width: none;
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .contextual-feedback-manager--floating {
            position: relative;
            top: auto;
            right: auto;
            transform: none;
          }
        }
      `}</style>
    </div>
  );
};

// Individual Feedback Item Component
interface FeedbackItemProps {
  feedback: FeedbackItem;
  onDismiss: () => void;
}

const FeedbackItem: React.FC<FeedbackItemProps> = ({ feedback, onDismiss }) => {
  const getStatusType = () => {
    switch (feedback.type) {
      case 'validation': return 'error';
      case 'warning': return 'warning';
      case 'strategic': return 'info';
      case 'encouragement': return 'success';
      case 'educational': return 'info';
      default: return 'info';
    }
  };

  const getIcon = () => {
    switch (feedback.type) {
      case 'validation': return '⚠';
      case 'warning': return '🚨';
      case 'strategic': return '💡';
      case 'encouragement': return '🎉';
      case 'educational': return 'ℹ';
      default: return 'ℹ';
    }
  };

  return (
    <div className={`feedback-item feedback-item--${feedback.type}`}>
      <div className="feedback-content">
        <div className="feedback-header">
          <span className="feedback-icon" aria-hidden="true">
            {getIcon()}
          </span>
          <Status status={getStatusType()}>
            {feedback.message}
          </Status>
        </div>
        
        {feedback.action && (
          <div className="feedback-action">
            <Small>→ {feedback.action}</Small>
          </div>
        )}
      </div>

      <button
        className="feedback-dismiss"
        onClick={onDismiss}
        aria-label="Dismiss feedback"
        title="Dismiss"
      >
        ×
      </button>

      <style jsx>{`
        .feedback-item {
          display: flex;
          align-items: flex-start;
          gap: var(--space-3);
          padding: var(--space-4);
          background: var(--glass-medium);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-lg);
          backdrop-filter: blur(12px);
          animation: feedback-enter 0.4s ease-out;
        }

        .feedback-item--validation {
          border-left: 4px solid var(--color-error);
        }

        .feedback-item--warning {
          border-left: 4px solid var(--color-warning);
        }

        .feedback-item--strategic {
          border-left: 4px solid var(--accent-cyan);
        }

        .feedback-item--encouragement {
          border-left: 4px solid var(--color-success);
        }

        .feedback-item--educational {
          border-left: 4px solid var(--accent-purple);
        }

        .feedback-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
        }

        .feedback-header {
          display: flex;
          align-items: flex-start;
          gap: var(--space-2);
        }

        .feedback-icon {
          font-size: var(--text-base);
          line-height: 1;
          margin-top: 2px;
        }

        .feedback-action {
          margin-left: var(--space-6);
          opacity: 0.8;
        }

        .feedback-dismiss {
          background: none;
          border: none;
          color: var(--text-muted);
          font-size: var(--text-lg);
          cursor: pointer;
          padding: var(--space-1);
          border-radius: var(--radius-base);
          transition: all var(--transition-base);
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }

        .feedback-dismiss:hover {
          background: var(--glass-light);
          color: var(--text-primary);
        }

        @keyframes feedback-enter {
          0% {
            opacity: 0;
            transform: translateX(100%) scale(0.9);
          }
          100% {
            opacity: 1;
            transform: translateX(0) scale(1);
          }
        }

        @media (prefers-reduced-motion: reduce) {
          .feedback-item {
            animation: none;
          }
        }
      `}</style>
    </div>
  );
};

export default ContextualFeedbackManager;

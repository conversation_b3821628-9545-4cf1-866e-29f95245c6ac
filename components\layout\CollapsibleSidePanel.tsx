/**
 * CollapsibleSidePanel Component - Progressive Disclosure System (v2.0 Spatial Design)
 *
 * 🎯 RESPONSIVE SIDE PANEL WITH TABBED NAVIGATION
 * 
 * Features:
 * - Smooth slide-in/out animations with backdrop blur
 * - Tabbed navigation for organizing secondary information
 * - User preference memory (localStorage)
 * - Responsive behavior (overlay on mobile, side panel on desktop)
 * - Keyboard navigation and accessibility support
 * 
 * Tabs:
 * - Stats: Game statistics and progress
 * - History: Definition history and past moves
 * - Rules: Game rules and help information
 * - Help: Tips, strategies, and guidance
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React, { useState, useEffect } from 'react';
import { Primary, Secondary, Small } from '@/components/ui/Typography';
import TabNavigation, { TabItem } from '@/components/ui/TabNavigation';
import styles from './CollapsibleSidePanel.module.css';

export type SidePanelTab = 'stats' | 'history' | 'rules' | 'help';

interface TabContent extends Omit<TabItem, 'id'> {
  id: SidePanelTab;
}

interface CollapsibleSidePanelProps {
  isOpen: boolean;
  onToggle: () => void;
  defaultTab?: SidePanelTab;
  tabs: TabContent[];
  rememberPreferences?: boolean;
  persistentMode?: boolean; // Allow playing with panel open
  overlayOnMobile?: boolean; // Control mobile behavior
  className?: string;
}

export const CollapsibleSidePanel: React.FC<CollapsibleSidePanelProps> = ({
  isOpen,
  onToggle,
  defaultTab = 'stats',
  tabs,
  rememberPreferences = true,
  persistentMode = true,
  overlayOnMobile = true,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<SidePanelTab>(defaultTab);
  const [hasBeenOpened, setHasBeenOpened] = useState(false);

  // Load user preferences from localStorage
  useEffect(() => {
    if (rememberPreferences) {
      const savedTab = localStorage.getItem('defeater-ai-side-panel-tab') as SidePanelTab;
      const savedOpen = localStorage.getItem('defeater-ai-side-panel-open') === 'true';
      
      if (savedTab && tabs.find(tab => tab.id === savedTab)) {
        setActiveTab(savedTab);
      }
      
      // Don't auto-open on first visit, but remember if user opened it
      if (savedOpen && hasBeenOpened) {
        // This would be handled by parent component
      }
    }
  }, [rememberPreferences, tabs, hasBeenOpened]);

  // Save user preferences
  useEffect(() => {
    if (rememberPreferences) {
      localStorage.setItem('defeater-ai-side-panel-tab', activeTab);
      localStorage.setItem('defeater-ai-side-panel-open', isOpen.toString());
    }
  }, [activeTab, isOpen, rememberPreferences]);

  // Track if panel has been opened
  useEffect(() => {
    if (isOpen && !hasBeenOpened) {
      setHasBeenOpened(true);
    }
  }, [isOpen, hasBeenOpened]);

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId as SidePanelTab);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onToggle();
    }
  };

  const panelClasses = [
    styles.collapsibleSidePanel,
    isOpen ? styles.collapsibleSidePanelOpen : '',
    className
  ].filter(Boolean).join(' ');

  const activeTabContent = tabs.find(tab => tab.id === activeTab);

  return (
    <>
      {/* Conditional Overlay - only on mobile when overlayOnMobile is true */}
      {isOpen && overlayOnMobile && (
        <div
          className={styles.panelOverlay}
          onClick={onToggle}
          aria-hidden="true"
        />
      )}

      {/* Side Panel */}
      <aside
        id="side-panel"
        className={panelClasses}
        aria-hidden={!isOpen}
        aria-label="Secondary information panel"
        onKeyDown={handleKeyDown}
        role="complementary"
      >
        {/* Panel Header */}
        <header className={styles.panelHeader}>
          <div className={styles.panelTitle}>
            <Primary as="h2">Game Information</Primary>
            {persistentMode && (
              <Small className={styles.persistentIndicator}>Can stay open while playing</Small>
            )}
          </div>
          <button
            className={styles.panelClose}
            onClick={onToggle}
            aria-label={persistentMode ? "Hide panel" : "Close information panel"}
          >
            {persistentMode ? '◀' : '×'}
          </button>
        </header>

        {/* Enhanced Tab Navigation */}
        <div className={styles.panelTabs}>
          <TabNavigation
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={handleTabChange}
            orientation="horizontal"
            variant="default"
            size="medium"
            showIcons={true}
            showBadges={true}
          />
        </div>

        {/* Tab Content */}
        <div className={styles.panelContent}>
          {activeTabContent?.content}
        </div>
      </aside>

      {/* Toggle Button */}
      <button
        className={`${styles.panelToggle} ${persistentMode ? styles.panelTogglePersistent : ''}`}
        onClick={onToggle}
        aria-label={
          persistentMode
            ? (isOpen ? "Hide side panel" : "Show side panel")
            : (isOpen ? "Close information panel" : "Open information panel")
        }
        aria-expanded={isOpen}
        aria-controls="side-panel"
        title={
          persistentMode
            ? (isOpen ? "Hide panel (can stay open while playing)" : "Show panel (can stay open while playing)")
            : (isOpen ? "Close panel" : "Open panel")
        }
      >
        <span className={styles.toggleIcon} aria-hidden="true">
          {isOpen ? (persistentMode ? '◀' : '×') : (persistentMode ? '▶' : 'ℹ')}
        </span>
        <span className={styles.srOnly}>
          {isOpen ? 'Hide' : 'Show'} information panel
        </span>
      </button>
    </>
  );
};
export default CollapsibleSidePanel;

/**
 * Random word pool for DEFEATER.AI
 * Curated list of interesting, definable words across diverse semantic domains
 */

export const WORD_POOL = {
  // Abstract concepts
  abstract: [
    'freedom', 'justice', 'beauty', 'truth', 'wisdom', 'courage', 'honor', 'peace',
    'chaos', 'order', 'balance', 'harmony', 'discord', 'unity', 'diversity', 'equality',
    'progress', 'tradition', 'innovation', 'revolution', 'evolution', 'transformation',
    'identity', 'purpose', 'meaning', 'existence', 'consciousness', 'awareness'
  ],

  // Physical objects
  objects: [
    'mirror', 'bridge', 'key', 'door', 'window', 'ladder', 'rope', 'anchor',
    'compass', 'telescope', 'microscope', 'lens', 'prism', 'crystal', 'diamond',
    'pearl', 'shell', 'feather', 'leaf', 'seed', 'root', 'branch', 'trunk',
    'wheel', 'gear', 'spring', 'lever', 'pulley', 'magnet', 'circuit'
  ],

  // Natural phenomena
  nature: [
    'thunder', 'lightning', 'rainbow', 'eclipse', 'aurora', 'tide', 'current',
    'wave', 'ripple', 'echo', 'resonance', 'vibration', 'frequency', 'amplitude',
    'gravity', 'momentum', 'velocity', 'acceleration', 'friction', 'pressure',
    'temperature', 'humidity', 'density', 'viscosity', 'elasticity', 'plasticity'
  ],

  // Biological/Medical
  biological: [
    'cell', 'tissue', 'organ', 'system', 'organism', 'species', 'genus', 'family',
    'evolution', 'adaptation', 'mutation', 'selection', 'inheritance', 'gene',
    'chromosome', 'protein', 'enzyme', 'hormone', 'neuron', 'synapse', 'reflex',
    'instinct', 'behavior', 'habitat', 'ecosystem', 'symbiosis', 'parasitism'
  ],

  // Mathematical/Logical
  mathematical: [
    'number', 'digit', 'fraction', 'ratio', 'proportion', 'percentage', 'average',
    'median', 'mode', 'range', 'variance', 'deviation', 'correlation', 'function',
    'variable', 'constant', 'equation', 'formula', 'theorem', 'proof', 'logic',
    'algorithm', 'sequence', 'series', 'pattern', 'symmetry', 'asymmetry'
  ],

  // Psychological/Emotional
  psychological: [
    'emotion', 'feeling', 'mood', 'temperament', 'personality', 'character', 'trait',
    'habit', 'routine', 'ritual', 'custom', 'tradition', 'culture', 'society',
    'community', 'relationship', 'friendship', 'love', 'trust', 'loyalty', 'betrayal',
    'fear', 'anxiety', 'stress', 'relief', 'comfort', 'security', 'confidence'
  ],

  // Temporal/Spatial
  temporal: [
    'moment', 'instant', 'duration', 'interval', 'period', 'cycle', 'rhythm',
    'tempo', 'pace', 'speed', 'acceleration', 'deceleration', 'pause', 'delay',
    'sequence', 'order', 'priority', 'urgency', 'deadline', 'schedule', 'timeline',
    'history', 'future', 'present', 'past', 'memory', 'anticipation', 'expectation'
  ],

  // Communication/Language
  communication: [
    'word', 'phrase', 'sentence', 'paragraph', 'chapter', 'story', 'narrative',
    'dialogue', 'monologue', 'conversation', 'discussion', 'debate', 'argument',
    'explanation', 'description', 'definition', 'translation', 'interpretation',
    'meaning', 'context', 'subtext', 'implication', 'inference', 'assumption'
  ],

  // Artistic/Creative
  artistic: [
    'color', 'hue', 'shade', 'tint', 'tone', 'contrast', 'brightness', 'saturation',
    'texture', 'pattern', 'design', 'composition', 'balance', 'proportion', 'scale',
    'perspective', 'dimension', 'depth', 'surface', 'form', 'shape', 'line',
    'curve', 'angle', 'circle', 'square', 'triangle', 'spiral', 'helix'
  ],

  // Technology/Tools
  technology: [
    'tool', 'instrument', 'device', 'machine', 'engine', 'motor', 'generator',
    'transformer', 'conductor', 'insulator', 'semiconductor', 'transistor', 'chip',
    'processor', 'memory', 'storage', 'input', 'output', 'interface', 'protocol',
    'network', 'connection', 'link', 'node', 'hub', 'switch', 'router'
  ]
};

/**
 * Gets all words from the pool as a flat array
 */
export function getAllWords(): string[] {
  return Object.values(WORD_POOL).flat();
}

/**
 * Gets random words from different categories to ensure diversity
 */
export function getRandomWords(count: number): string[] {
  const categories = Object.keys(WORD_POOL);
  const selectedWords: string[] = [];
  const usedCategories = new Set<string>();

  for (let i = 0; i < count; i++) {
    // Try to pick from unused categories first
    const availableCategories = categories.filter(cat => !usedCategories.has(cat));
    const categoryPool = availableCategories.length > 0 ? availableCategories : categories;
    
    // Pick random category
    const randomCategory = categoryPool[Math.floor(Math.random() * categoryPool.length)];
    usedCategories.add(randomCategory);
    
    // Pick random word from that category
    const categoryWords = WORD_POOL[randomCategory as keyof typeof WORD_POOL];
    const availableWords = categoryWords.filter(word => !selectedWords.includes(word));
    
    if (availableWords.length > 0) {
      const randomWord = availableWords[Math.floor(Math.random() * availableWords.length)];
      selectedWords.push(randomWord);
    }
    
    // Reset used categories if we've used them all
    if (usedCategories.size >= categories.length) {
      usedCategories.clear();
    }
  }

  return selectedWords;
}

/**
 * Generates a random starting word and target words
 */
export function generateRandomGameWords(): { startWord: string; targets: string[] } {
  const allWords = getRandomWords(4); // Get 4 diverse words
  
  return {
    startWord: allWords[0],
    targets: allWords.slice(1, 4) // Take the other 3 as targets
  };
}

/**
 * GameStatsPanel Component - Consolidated Game Statistics (v2.0 Spatial Design)
 *
 * 🎯 COMPREHENSIVE STATS DISPLAY
 * 
 * Features:
 * - Real-time game progress and performance metrics
 * - Visual progress bars and trend indicators
 * - Consolidated view of all game statistics
 * - Responsive layout with clear visual hierarchy
 * 
 * Consolidates:
 * - GameStats component (legacy)
 * - WordsLeftCounter component (legacy)
 * - Progress tracking widgets
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React from 'react';
import { Primary, Secondary, Small, Status, Mono } from '@/components/ui/Typography';

interface GameState {
  step: number;
  maxSteps: number;
  targets: string[];
  completedTargets: string[];
  burnedTargets: string[];
  definitions: Array<{
    word: string;
    definition: string;
    wordCount: number;
    isValid: boolean;
  }>;
  difficulty: 'easy' | 'medium' | 'hard';
  wordsLeft: number;
  maxWords: number;
}

interface GameStatsPanelProps {
  gameState: GameState;
  currentDefinitionLength?: number;
  showDetailed?: boolean;
  className?: string;
}

export const GameStatsPanel: React.FC<GameStatsPanelProps> = ({
  gameState,
  currentDefinitionLength = 0,
  showDetailed = true,
  className = ''
}) => {
  const {
    step,
    maxSteps,
    targets,
    completedTargets,
    burnedTargets,
    definitions,
    difficulty,
    wordsLeft,
    maxWords
  } = gameState;

  // Calculate progress metrics
  const progressPercentage = Math.round((step / maxSteps) * 100);
  const remainingSteps = maxSteps - step;
  const remainingTargets = targets.length - completedTargets.length - burnedTargets.length;
  const successRate = definitions.length > 0 
    ? Math.round((definitions.filter(d => d.isValid).length / definitions.length) * 100)
    : 0;
  
  // Calculate word efficiency
  const totalWordsUsed = definitions.reduce((sum, def) => sum + def.wordCount, 0);
  const avgWordsPerDefinition = definitions.length > 0 
    ? Math.round(totalWordsUsed / definitions.length * 10) / 10
    : 0;

  // Determine urgency levels
  const isUrgent = remainingSteps <= 5;
  const isCritical = remainingSteps <= 2;
  const wordsLeftPercentage = Math.round((wordsLeft / maxWords) * 100);

  return (
    <div className={`game-stats-panel ${className}`}>
      {/* Progress Overview */}
      <section className="stats-section">
        <Primary as="h3">Game Progress</Primary>
        
        <div className="progress-card">
          <div className="progress-header">
            <Secondary>Step {step} of {maxSteps}</Secondary>
            <span className={`progress-percentage ${isUrgent ? 'progress-percentage--urgent' : ''}`}>
              {progressPercentage}%
            </span>
          </div>
          
          <div className="progress-bar">
            <div 
              className={`progress-fill ${isCritical ? 'progress-fill--critical' : isUrgent ? 'progress-fill--urgent' : ''}`}
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          
          <div className="progress-details">
            <Small>
              {remainingSteps} steps remaining • {difficulty.toUpperCase()} mode
            </Small>
          </div>
        </div>
      </section>

      {/* Word Budget */}
      <section className="stats-section">
        <Primary as="h3">Word Budget</Primary>
        
        <div className="word-budget-card">
          <div className="budget-header">
            <Secondary>Words Remaining</Secondary>
            <span className={`budget-count ${wordsLeftPercentage <= 20 ? 'budget-count--low' : ''}`}>
              {wordsLeft}
            </span>
          </div>
          
          <div className="budget-bar">
            <div 
              className={`budget-fill ${wordsLeftPercentage <= 20 ? 'budget-fill--low' : wordsLeftPercentage <= 50 ? 'budget-fill--medium' : ''}`}
              style={{ width: `${wordsLeftPercentage}%` }}
            />
          </div>
          
          <div className="budget-details">
            <Small>
              Current definition: {currentDefinitionLength} words
            </Small>
          </div>
        </div>
      </section>

      {/* Targets Overview */}
      <section className="stats-section">
        <Primary as="h3">Targets</Primary>
        
        <div className="targets-grid">
          <div className="target-stat target-stat--remaining">
            <div className="stat-value">{remainingTargets}</div>
            <Small>Remaining</Small>
          </div>
          
          <div className="target-stat target-stat--completed">
            <div className="stat-value">{completedTargets.length}</div>
            <Small>Completed</Small>
          </div>
          
          <div className="target-stat target-stat--burned">
            <div className="stat-value">{burnedTargets.length}</div>
            <Small>Burned</Small>
          </div>
        </div>
      </section>

      {/* Performance Metrics */}
      {showDetailed && (
        <section className="stats-section">
          <Primary as="h3">Performance</Primary>
          
          <div className="performance-metrics">
            <div className="metric-item">
              <Secondary>Success Rate</Secondary>
              <div className="metric-value">
                <span className={`metric-number ${successRate >= 80 ? 'metric-number--good' : successRate >= 60 ? 'metric-number--ok' : 'metric-number--poor'}`}>
                  {successRate}%
                </span>
              </div>
            </div>
            
            <div className="metric-item">
              <Secondary>Avg Words/Definition</Secondary>
              <div className="metric-value">
                <Mono className="metric-number">{avgWordsPerDefinition}</Mono>
              </div>
            </div>
            
            <div className="metric-item">
              <Secondary>Total Definitions</Secondary>
              <div className="metric-value">
                <span className="metric-number">{definitions.length}</span>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Status Indicators */}
      <section className="stats-section">
        <Primary as="h3">Status</Primary>
        
        <div className="status-indicators">
          {isCritical && (
            <Status status="error">
              CRITICAL: Only {remainingSteps} steps left!
            </Status>
          )}

          {isUrgent && !isCritical && (
            <Status status="warning">
              URGENT: {remainingSteps} steps remaining
            </Status>
          )}

          {wordsLeftPercentage <= 20 && (
            <Status status="warning">
              LOW WORD BUDGET: {wordsLeft} words left
            </Status>
          )}

          {!isUrgent && !isCritical && wordsLeftPercentage > 20 && (
            <Status status="success">
              ON TRACK: {remainingSteps} steps, {wordsLeft} words
            </Status>
          )}
        </div>
      </section>

      <style jsx>{`
        /* === CORE PANEL LAYOUT === */
        .game-stats-panel {
          display: flex;
          flex-direction: column;
          gap: var(--space-6);
          height: 100%;
        }

        .stats-section {
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
        }

        .stats-section h3 {
          margin: 0;
          padding-bottom: var(--space-2);
          border-bottom: 1px solid var(--glass-border);
          font-size: var(--text-base);
        }

        /* === PROGRESS CARD === */
        .progress-card {
          background: var(--glass-subtle);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-lg);
          padding: var(--space-4);
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
        }

        .progress-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .progress-percentage {
          font-size: var(--text-lg);
          font-weight: var(--font-bold);
          color: var(--accent-cyan);
        }

        .progress-percentage--urgent {
          color: var(--color-warning);
        }

        .progress-bar {
          height: 8px;
          background: var(--bg-tertiary);
          border-radius: var(--radius-full);
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, var(--accent-cyan) 0%, var(--accent-purple) 100%);
          transition: width var(--transition-base);
        }

        .progress-fill--urgent {
          background: linear-gradient(90deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
        }

        .progress-fill--critical {
          background: linear-gradient(90deg, var(--color-error) 0%, var(--color-error-dark) 100%);
        }

        .progress-details {
          text-align: center;
          opacity: 0.8;
        }

        /* === WORD BUDGET CARD === */
        .word-budget-card {
          background: var(--glass-subtle);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-lg);
          padding: var(--space-4);
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
        }

        .budget-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .budget-count {
          font-size: var(--text-xl);
          font-weight: var(--font-bold);
          color: var(--color-success);
        }

        .budget-count--low {
          color: var(--color-error);
        }

        .budget-bar {
          height: 6px;
          background: var(--bg-tertiary);
          border-radius: var(--radius-full);
          overflow: hidden;
        }

        .budget-fill {
          height: 100%;
          background: var(--color-success);
          transition: width var(--transition-base);
        }

        .budget-fill--medium {
          background: var(--color-warning);
        }

        .budget-fill--low {
          background: var(--color-error);
        }

        .budget-details {
          text-align: center;
          opacity: 0.8;
        }

        /* === TARGETS GRID === */
        .targets-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: var(--space-3);
        }

        .target-stat {
          background: var(--glass-subtle);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-base);
          padding: var(--space-3);
          text-align: center;
          display: flex;
          flex-direction: column;
          gap: var(--space-1);
        }

        .stat-value {
          font-size: var(--text-2xl);
          font-weight: var(--font-bold);
          line-height: 1;
        }

        .target-stat--remaining .stat-value {
          color: var(--accent-cyan);
        }

        .target-stat--completed .stat-value {
          color: var(--color-success);
        }

        .target-stat--burned .stat-value {
          color: var(--color-error);
        }

        /* === PERFORMANCE METRICS === */
        .performance-metrics {
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
        }

        .metric-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--space-2) 0;
          border-bottom: 1px solid var(--glass-border);
        }

        .metric-item:last-child {
          border-bottom: none;
        }

        .metric-value {
          text-align: right;
        }

        .metric-number {
          font-size: var(--text-lg);
          font-weight: var(--font-semibold);
          color: var(--text-primary);
        }

        .metric-number--good {
          color: var(--color-success);
        }

        .metric-number--ok {
          color: var(--color-warning);
        }

        .metric-number--poor {
          color: var(--color-error);
        }

        /* === STATUS INDICATORS === */
        .status-indicators {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .targets-grid {
            grid-template-columns: 1fr;
            gap: var(--space-2);
          }

          .target-stat {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
          }

          .stat-value {
            font-size: var(--text-lg);
          }
        }
      `}</style>
    </div>
  );
};

export default GameStatsPanel;

/**
 * TargetRevelationStrip Component - Progressive Target Display (v2.0 Spatial Design)
 *
 * 🎯 CRITICAL HORIZONTAL TARGET DISPLAY - Always Visible (15% Space)
 * 
 * Features:
 * - Progressive revelation of target word patterns
 * - Compact horizontal layout optimized for space efficiency
 * - Always visible during gameplay (sticky/persistent)
 * - Responsive design that adapts to all screen sizes
 * - Visual indicators for revealed/hidden letters and completion status
 * 
 * Space Allocation:
 * - Takes exactly 15% of vertical space
 * - Horizontal scrolling on mobile if needed
 * - Compact but readable typography
 * 
 * Revelation Pattern:
 * - First/last letters revealed initially
 * - Additional letters revealed every 3 turns
 * - Completed targets marked with visual indicators
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React from 'react';
import { Secondary, Small, Mono } from '@/components/ui/Typography';

interface TargetWord {
  word: string;
  revealed: string; // Pattern like "I n n o v _ _ _ _ n"
  isCompleted: boolean;
  isBurned: boolean;
}

interface TargetRevelationStripProps {
  targets: TargetWord[];
  currentStep: number;
  revealFrequency?: number;
  showProgress?: boolean;
  compact?: boolean;
  className?: string;
}

export const TargetRevelationStrip: React.FC<TargetRevelationStripProps> = ({
  targets,
  currentStep,
  revealFrequency = 3,
  showProgress = true,
  compact = false,
  className = ''
}) => {
  const stripClasses = [
    'target-revelation-strip',
    compact ? 'target-revelation-strip--compact' : '',
    className
  ].filter(Boolean).join(' ');

  const remainingTargets = targets.filter(t => !t.isCompleted && !t.isBurned);
  const completedTargets = targets.filter(t => t.isCompleted);
  const burnedTargets = targets.filter(t => t.isBurned);

  return (
    <section 
      className={stripClasses} 
      role="region" 
      aria-label="Target words revelation"
      aria-live="polite"
    >
      {/* Strip Header */}
      <div className="strip-header">
        <Secondary as="h3">TARGET WORDS</Secondary>
        {showProgress && (
          <Small className="progress-info">
            {remainingTargets.length} remaining • Reveals every {revealFrequency} turns
          </Small>
        )}
      </div>

      {/* Target Words Display */}
      <div className="targets-container">
        <div className="targets-scroll">
          {targets.map((target, index) => (
            <div
              key={index}
              className={`target-item ${
                target.isCompleted ? 'target-item--completed' : 
                target.isBurned ? 'target-item--burned' : 
                'target-item--active'
              }`}
              aria-label={`Target ${index + 1}: ${target.revealed}${
                target.isCompleted ? ' - completed' : 
                target.isBurned ? ' - burned' : 
                ' - in progress'
              }`}
            >
              {/* Target Pattern */}
              <div className="target-pattern">
                <Mono className="pattern-text">
                  {target.revealed}
                </Mono>
              </div>

              {/* Status Indicator */}
              <div className="target-status">
                {target.isCompleted && (
                  <span className="status-icon status-icon--completed" aria-label="Completed">
                    ✓
                  </span>
                )}
                {target.isBurned && (
                  <span className="status-icon status-icon--burned" aria-label="Burned">
                    🔥
                  </span>
                )}
                {!target.isCompleted && !target.isBurned && (
                  <span className="status-icon status-icon--active" aria-label="In progress">
                    •
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Summary Stats */}
      {showProgress && !compact && (
        <div className="strip-footer">
          <div className="stats-grid">
            <div className="stat-item stat-item--remaining">
              <Small>Remaining</Small>
              <span className="stat-value">{remainingTargets.length}</span>
            </div>
            <div className="stat-item stat-item--completed">
              <Small>Completed</Small>
              <span className="stat-value">{completedTargets.length}</span>
            </div>
            <div className="stat-item stat-item--burned">
              <Small>Burned</Small>
              <span className="stat-value">{burnedTargets.length}</span>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        /* === CORE STRIP LAYOUT === */
        .target-revelation-strip {
          width: 100%;
          min-height: 15vh; /* Exactly 15% space allocation */
          max-height: 20vh; /* Prevent expansion */
          display: flex;
          flex-direction: column;
          background: var(--glass-medium);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-lg);
          backdrop-filter: blur(8px);
          padding: var(--space-4);
          gap: var(--space-3);
          position: relative;
          overflow: hidden;
        }

        .target-revelation-strip--compact {
          min-height: 12vh;
          padding: var(--space-3);
          gap: var(--space-2);
        }

        /* === STRIP HEADER === */
        .strip-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-shrink: 0;
          padding-bottom: var(--space-2);
          border-bottom: 1px solid var(--glass-border);
        }

        .strip-header h3 {
          margin: 0;
          font-size: var(--text-base);
        }

        .progress-info {
          opacity: 0.8;
          text-align: right;
        }

        /* === TARGETS CONTAINER === */
        .targets-container {
          flex: 1;
          overflow: hidden;
          position: relative;
        }

        .targets-scroll {
          display: flex;
          gap: var(--space-3);
          overflow-x: auto;
          overflow-y: hidden;
          padding: var(--space-2) 0;
          height: 100%;
          align-items: center;
          scrollbar-width: thin;
          scrollbar-color: var(--accent-cyan-30) transparent;
        }

        .targets-scroll::-webkit-scrollbar {
          height: 4px;
        }

        .targets-scroll::-webkit-scrollbar-track {
          background: transparent;
        }

        .targets-scroll::-webkit-scrollbar-thumb {
          background: var(--accent-cyan-30);
          border-radius: 2px;
        }

        /* === TARGET ITEMS === */
        .target-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--space-1);
          padding: var(--space-2) var(--space-3);
          background: var(--bg-secondary);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-base);
          min-width: 120px;
          flex-shrink: 0;
          transition: all var(--transition-base);
          position: relative;
        }

        .target-item--active {
          border-color: var(--accent-cyan-50);
          background: var(--bg-primary);
        }

        .target-item--completed {
          border-color: var(--color-success-50);
          background: var(--color-success-10);
        }

        .target-item--burned {
          border-color: var(--color-error-50);
          background: var(--color-error-10);
          opacity: 0.7;
        }

        /* === TARGET PATTERN === */
        .target-pattern {
          text-align: center;
        }

        .pattern-text {
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          letter-spacing: 0.1em;
          color: var(--text-primary);
        }

        .target-item--completed .pattern-text {
          color: var(--color-success);
        }

        .target-item--burned .pattern-text {
          color: var(--color-error);
          text-decoration: line-through;
        }

        /* === STATUS INDICATORS === */
        .target-status {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .status-icon {
          font-size: var(--text-xs);
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          font-weight: var(--font-bold);
        }

        .status-icon--completed {
          background: var(--color-success-20);
          color: var(--color-success);
        }

        .status-icon--burned {
          background: var(--color-error-20);
          color: var(--color-error);
        }

        .status-icon--active {
          background: var(--accent-cyan-20);
          color: var(--accent-cyan);
        }

        /* === STRIP FOOTER === */
        .strip-footer {
          flex-shrink: 0;
          padding-top: var(--space-2);
          border-top: 1px solid var(--glass-border);
        }

        .stats-grid {
          display: flex;
          justify-content: space-around;
          gap: var(--space-4);
        }

        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--space-1);
          min-width: 60px;
        }

        .stat-value {
          font-size: var(--text-lg);
          font-weight: var(--font-bold);
          color: var(--text-primary);
        }

        .stat-item--remaining .stat-value {
          color: var(--accent-cyan);
        }

        .stat-item--completed .stat-value {
          color: var(--color-success);
        }

        .stat-item--burned .stat-value {
          color: var(--color-error);
        }

        /* === RESPONSIVE DESIGN === */
        
        /* Mobile: Ultra-compact */
        @media (max-width: 767px) {
          .target-revelation-strip {
            min-height: 18vh; /* Slightly more space on mobile */
            padding: var(--space-3);
          }

          .strip-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--space-1);
          }

          .strip-header h3 {
            font-size: var(--text-sm);
          }

          .target-item {
            min-width: 100px;
            padding: var(--space-1) var(--space-2);
          }

          .pattern-text {
            font-size: var(--text-xs);
          }

          .stats-grid {
            gap: var(--space-2);
          }

          .stat-item {
            min-width: 50px;
          }
        }

        /* Tablet: Balanced */
        @media (min-width: 768px) and (max-width: 1023px) {
          .target-revelation-strip {
            min-height: 16vh;
          }

          .target-item {
            min-width: 110px;
          }
        }

        /* Desktop: Spacious */
        @media (min-width: 1024px) {
          .target-revelation-strip {
            min-height: 15vh;
          }

          .target-item {
            min-width: 130px;
          }

          .pattern-text {
            font-size: var(--text-base);
          }
        }

        /* === ACCESSIBILITY === */
        @media (prefers-reduced-motion: reduce) {
          .target-item {
            transition: none;
          }
        }

        /* High contrast mode */
        @media (prefers-contrast: high) {
          .target-item {
            border-width: 2px;
          }

          .status-icon {
            border: 1px solid currentColor;
          }
        }
      `}</style>
    </section>
  );
};

export default TargetRevelationStrip;

/**
 * Test utilities for DEFEATER.AI migration testing
 * 
 * Provides mock data, test helpers, and utilities for comprehensive testing
 * during the technical debt migration process.
 */

import { GameState, UIState, Definition, AIResponse, DifficultyLevel } from '@/types/game';

// Mock game state factory
export function createMockGameState(overrides?: Partial<GameState>): GameState {
  const baseState: GameState = {
    gameId: 'test-game-id-123',
    currentWord: 'example',
    targets: ['target1', 'target2', 'target3'],
    burnedTargets: [],
    definitions: [],
    usedWords: [],
    aiChallengeWords: ['example'],
    step: 1,
    maxSteps: 25,
    gameStatus: 'waiting',
    difficulty: 'medium',
    consecutiveRejections: 0,
    rejectionCount: 0,
    completedTargets: [],
    commonWordsUsage: {},
    lastDefinitionLength: undefined,
    rejectionHistory: []
  };

  return { ...baseState, ...overrides };
}

// Mock UI state factory
export function createMockUIState(overrides?: Partial<UIState>): UIState {
  const baseState: UIState = {
    isLoading: false,
    showRules: false,
    showHistory: false,
    animationState: 'idle',
    inputValue: '',
    wordCount: 0
  };

  return { ...baseState, ...overrides };
}

// Mock definition factory
export function createMockDefinition(overrides?: Partial<Definition>): Definition {
  const baseDefinition: Definition = {
    id: 'test-def-id',
    word: 'test-word',
    definition: 'A test definition for testing purposes',
    wordCount: 7,
    timestamp: Date.now(),
    isValid: true
  };

  return { ...baseDefinition, ...overrides };
}

// Mock AI response factory
export function createMockAIResponse(overrides?: Partial<AIResponse>): AIResponse {
  const baseResponse: AIResponse = {
    accept: true,
    reason: 'Definition accepted for testing',
    nextWord: 'next-test-word',
    internalAnalysis: {
      playerConfidence: 75,
      remainingPaths: ['target1', 'target2'],
      recommendedBurn: null,
      difficultyAdjustment: 0
    }
  };

  return { ...baseResponse, ...overrides };
}

// Game state scenarios for testing
export const gameStateScenarios = {
  // Fresh game start
  newGame: (): GameState => createMockGameState({
    step: 1,
    definitions: [],
    usedWords: [],
    gameStatus: 'waiting'
  }),

  // Mid-game state
  midGame: (): GameState => createMockGameState({
    step: 10,
    definitions: [
      createMockDefinition({ word: 'word1', definition: 'First definition', wordCount: 5 }),
      createMockDefinition({ word: 'word2', definition: 'Second definition', wordCount: 4 }),
      createMockDefinition({ word: 'word3', definition: 'Third definition', wordCount: 3 })
    ],
    usedWords: ['word1', 'word2', 'word3', 'first', 'definition', 'second', 'third'],
    consecutiveRejections: 1
  }),

  // Near end game
  nearEnd: (): GameState => createMockGameState({
    step: 20,
    maxSteps: 25,
    burnedTargets: ['target3'],
    targets: ['target1', 'target2'],
    consecutiveRejections: 2
  }),

  // Won game
  wonGame: (): GameState => createMockGameState({
    step: 15,
    gameStatus: 'won',
    completedTargets: ['target1'],
    currentWord: 'target1'
  }),

  // Lost game
  lostGame: (): GameState => createMockGameState({
    step: 25,
    maxSteps: 25,
    gameStatus: 'lost',
    burnedTargets: ['target1', 'target2', 'target3']
  }),

  // Error state
  errorState: (): GameState => createMockGameState({
    gameStatus: 'error'
  })
};

// Mock API responses
export const mockApiResponses = {
  startGameSuccess: {
    success: true,
    gameState: gameStateScenarios.newGame(),
    aiResponse: undefined
  },

  startGameError: {
    success: false,
    gameState: gameStateScenarios.newGame(),
    error: 'Failed to start game'
  },

  submitDefinitionSuccess: {
    success: true,
    gameState: gameStateScenarios.midGame(),
    aiResponse: createMockAIResponse()
  },

  submitDefinitionRejected: {
    success: false,
    gameState: gameStateScenarios.midGame(),
    error: 'Definition rejected: too similar to previous definition'
  },

  gameWon: {
    success: true,
    gameState: gameStateScenarios.wonGame(),
    aiResponse: createMockAIResponse({ accept: true, reason: 'Target reached!' })
  },

  gameLost: {
    success: true,
    gameState: gameStateScenarios.lostGame(),
    aiResponse: createMockAIResponse({ accept: false, reason: 'Game over - no more steps' })
  }
};

// Test helpers
export const testHelpers = {
  // Wait for async operations
  waitFor: (ms: number = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Mock fetch for API calls
  mockFetch: (response: any, shouldReject = false) => {
    // Only available in test environment - avoid Jest namespace during build
    if (process.env.NODE_ENV === 'test' && typeof window !== 'undefined') {
      // Use the global Jest mock that's already set up in jest.setup.js
      const jestGlobal = (global as any).jest;
      if (jestGlobal && jestGlobal.fn) {
        const mockFn = jestGlobal.fn(() => {
          if (shouldReject) {
            return Promise.reject(new Error('Network error'));
          }
          return Promise.resolve({
            ok: true,
            json: () => Promise.resolve(response)
          } as Response);
        });
        global.fetch = mockFn as any;
      }
    }
  },

  // Mock performance API
  mockPerformance: () => {
    Object.defineProperty(window, 'performance', {
      value: {
        now: () => Date.now(),
        memory: {
          usedJSHeapSize: 1000000,
          totalJSHeapSize: 2000000,
          jsHeapSizeLimit: 4000000
        }
      },
      writable: true
    });
  },

  // Mock window globals
  mockWindowGlobals: () => {
    Object.defineProperty(window, 'matchMedia', {
      value: () => ({
        matches: false,
        addListener: () => {},
        removeListener: () => {},
        addEventListener: () => {},
        removeEventListener: () => {}
      }),
      writable: true
    });

    // Mock global functions used by GameBoard
    (window as any).addAITrashTalk = () => {};
    (window as any).currentGameState = null;
  },

  // Clean up mocks
  cleanupMocks: () => {
    // Jest cleanup would happen here in test environment
    delete (window as any).addAITrashTalk;
    delete (window as any).currentGameState;
  }
};

// Performance testing utilities
export const performanceTestUtils = {
  // Measure component render time
  measureRenderTime: async (renderFn: () => void): Promise<number> => {
    const start = performance.now();
    renderFn();
    await testHelpers.waitFor(0); // Wait for next tick
    const end = performance.now();
    return end - start;
  },

  // Check for memory leaks
  checkMemoryUsage: (): number => {
    if ((performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  },

  // Simulate low-end device
  simulateLowEndDevice: () => {
    // This would mock isLowEndDevice to return true in test environment
    console.log('Simulating low-end device for testing');
  }
};

// Accessibility testing utilities
export const a11yTestUtils = {
  // Check for required ARIA attributes
  checkAriaAttributes: (element: HTMLElement): string[] => {
    const issues: string[] = [];
    
    if (!element.getAttribute('aria-label') && !element.getAttribute('aria-labelledby')) {
      issues.push('Missing aria-label or aria-labelledby');
    }
    
    return issues;
  },

  // Check keyboard navigation
  simulateKeyboardNavigation: (element: HTMLElement, key: string) => {
    const event = new KeyboardEvent('keydown', { key });
    element.dispatchEvent(event);
  }
};

// Export all utilities
export default {
  createMockGameState,
  createMockUIState,
  createMockDefinition,
  createMockAIResponse,
  gameStateScenarios,
  mockApiResponses,
  testHelpers,
  performanceTestUtils,
  a11yTestUtils
};

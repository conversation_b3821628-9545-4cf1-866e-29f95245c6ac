/**
 * Network Optimization Utilities
 * 
 * Advanced network optimization features including connection monitoring,
 * adaptive quality, offline support, and bandwidth optimization.
 */

import { apiClient } from './apiClient';
import { memoryManager } from './memoryManagement';
import { <PERSON><PERSON>rrorHandler } from './errorHandling';

interface NetworkStatus {
  online: boolean;
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
}

interface NetworkMetrics {
  connectionType: string;
  bandwidth: number;
  latency: number;
  packetLoss: number;
  jitter: number;
  lastUpdated: number;
}

interface AdaptiveConfig {
  enableCaching: boolean;
  retries: number;
  timeout: number;
  cacheTTL: number;
  compressionLevel: 'low' | 'medium' | 'high';
}

class NetworkOptimizer {
  private static instance: NetworkOptimizer;
  private networkStatus: NetworkStatus = {
    online: true,
    effectiveType: '4g',
    downlink: 10,
    rtt: 100,
    saveData: false
  };
  private metrics: NetworkMetrics = {
    connectionType: 'unknown',
    bandwidth: 0,
    latency: 0,
    packetLoss: 0,
    jitter: 0,
    lastUpdated: Date.now()
  };
  private offlineQueue: Array<() => Promise<any>> = [];
  private isMonitoring = false;

  static getInstance(): NetworkOptimizer {
    if (!NetworkOptimizer.instance) {
      NetworkOptimizer.instance = new NetworkOptimizer();
    }
    return NetworkOptimizer.instance;
  }

  constructor() {
    if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'test') {
      this.initializeNetworkMonitoring();
    }
  }

  /**
   * Initialize network monitoring
   */
  private initializeNetworkMonitoring(): void {
    if (this.isMonitoring) return;

    // Monitor online/offline status
    const handleOnline = () => {
      this.networkStatus.online = true;
      this.processOfflineQueue();
      console.debug('🌐 Network: Back online, processing queued requests');
    };

    const handleOffline = () => {
      this.networkStatus.online = false;
      console.debug('🌐 Network: Gone offline, queueing requests');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Track network monitoring for cleanup
    memoryManager.trackResource(
      'listener',
      { element: window, event: 'online', handler: handleOnline },
      () => window.removeEventListener('online', handleOnline),
      'NetworkOptimizer'
    );

    memoryManager.trackResource(
      'listener',
      { element: window, event: 'offline', handler: handleOffline },
      () => window.removeEventListener('offline', handleOffline),
      'NetworkOptimizer'
    );

    // Monitor network information if available
    if ('connection' in navigator && typeof (navigator as any).connection?.addEventListener === 'function') {
      const connection = (navigator as any).connection;

      const updateNetworkInfo = () => {
        this.networkStatus = {
          online: navigator.onLine,
          effectiveType: connection.effectiveType || '4g',
          downlink: connection.downlink || 10,
          rtt: connection.rtt || 100,
          saveData: connection.saveData || false
        };

        this.updateMetrics();
      };

      try {
        connection.addEventListener('change', updateNetworkInfo);
        updateNetworkInfo();

        memoryManager.trackResource(
          'listener',
          { element: connection, event: 'change', handler: updateNetworkInfo },
          () => {
            try {
              connection.removeEventListener('change', updateNetworkInfo);
            } catch (error) {
              // Ignore cleanup errors in test environments
            }
          },
          'NetworkOptimizer'
        );
      } catch (error) {
        // Network connection API not available (test environment)
        console.debug('Network connection API not available:', error);
      }
    }

    // Periodic network quality assessment
    const qualityCheckInterval = setInterval(() => {
      this.assessNetworkQuality();
    }, 30000); // Every 30 seconds

    memoryManager.trackResource(
      'timer',
      qualityCheckInterval,
      () => clearInterval(qualityCheckInterval),
      'NetworkOptimizer'
    );

    this.isMonitoring = true;
  }

  /**
   * Get adaptive configuration based on network conditions
   */
  getAdaptiveConfig(): AdaptiveConfig {
    const { effectiveType, downlink, rtt, saveData } = this.networkStatus;

    // High-speed connection (4g, wifi)
    if (effectiveType === '4g' && downlink > 5 && rtt < 200 && !saveData) {
      return {
        enableCaching: true,
        retries: 3,
        timeout: 30000,
        cacheTTL: 300000, // 5 minutes
        compressionLevel: 'low'
      };
    }

    // Medium-speed connection (3g, slow 4g)
    if ((effectiveType === '3g' || downlink <= 5) && rtt < 500) {
      return {
        enableCaching: true,
        retries: 2,
        timeout: 45000,
        cacheTTL: 600000, // 10 minutes
        compressionLevel: 'medium'
      };
    }

    // Slow connection (2g, very slow)
    return {
      enableCaching: true,
      retries: 1,
      timeout: 60000,
      cacheTTL: 900000, // 15 minutes
      compressionLevel: 'high'
    };
  }

  /**
   * Make optimized request with adaptive configuration
   */
  async optimizedRequest<T>(
    url: string,
    options: any = {}
  ): Promise<T> {
    // Check if offline
    if (!this.networkStatus.online) {
      return this.queueOfflineRequest(() => 
        this.optimizedRequest<T>(url, options)
      );
    }

    const adaptiveConfig = this.getAdaptiveConfig();
    
    const optimizedOptions = {
      ...options,
      ...adaptiveConfig,
      // Override with user-provided options
      ...options
    };

    try {
      const result = await apiClient.request<T>(url, optimizedOptions);
      return result;

    } catch (error) {
      // If request fails and we're offline, queue it
      if (!navigator.onLine) {
        return this.queueOfflineRequest(() => 
          this.optimizedRequest<T>(url, options)
        );
      }
      
      throw error;
    }
  }

  /**
   * Queue request for when network comes back online
   */
  private async queueOfflineRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.offlineQueue.push(async () => {
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  /**
   * Process queued offline requests
   */
  private async processOfflineQueue(): Promise<void> {
    if (this.offlineQueue.length === 0) return;

    console.debug(`🌐 Processing ${this.offlineQueue.length} queued requests`);
    
    const queue = [...this.offlineQueue];
    this.offlineQueue = [];

    // Process requests with limited concurrency
    const maxConcurrency = 3;
    const executing: Promise<void>[] = [];

    for (const request of queue) {
      const promise = request().catch(error => {
        console.error('Failed to process queued request:', error);
      });

      executing.push(promise);

      if (executing.length >= maxConcurrency) {
        await Promise.race(executing);
        executing.splice(executing.findIndex(p => p === promise), 1);
      }
    }

    await Promise.all(executing);
  }

  /**
   * Assess network quality through ping tests
   */
  private async assessNetworkQuality(): Promise<void> {
    if (!this.networkStatus.online) return;

    try {
      const startTime = performance.now();
      
      // Simple ping test using a small API call
      await fetch('/api/health', {
        method: 'HEAD',
        cache: 'no-cache'
      });

      const latency = performance.now() - startTime;
      
      this.metrics = {
        ...this.metrics,
        latency,
        lastUpdated: Date.now()
      };

    } catch (error) {
      console.debug('Network quality assessment failed:', error);
    }
  }

  /**
   * Update network metrics
   */
  private updateMetrics(): void {
    this.metrics = {
      connectionType: this.networkStatus.effectiveType,
      bandwidth: this.networkStatus.downlink,
      latency: this.networkStatus.rtt,
      packetLoss: 0, // Would need more sophisticated measurement
      jitter: 0,     // Would need more sophisticated measurement
      lastUpdated: Date.now()
    };
  }

  /**
   * Get current network status
   */
  getNetworkStatus(): NetworkStatus {
    return { ...this.networkStatus };
  }

  /**
   * Get network metrics
   */
  getNetworkMetrics(): NetworkMetrics {
    return { ...this.metrics };
  }

  /**
   * Check if network is suitable for heavy operations
   */
  isHighQualityConnection(): boolean {
    const { effectiveType, downlink, rtt, saveData } = this.networkStatus;
    return (
      effectiveType === '4g' &&
      downlink > 2 &&
      rtt < 300 &&
      !saveData
    );
  }

  /**
   * Get recommended cache strategy
   */
  getRecommendedCacheStrategy(): 'aggressive' | 'moderate' | 'conservative' {
    if (this.isHighQualityConnection()) {
      return 'moderate';
    }
    
    if (this.networkStatus.saveData || this.networkStatus.effectiveType === '2g') {
      return 'aggressive';
    }
    
    return 'conservative';
  }

  /**
   * Preload critical resources
   */
  async preloadCriticalResources(): Promise<void> {
    if (!this.isHighQualityConnection()) {
      console.debug('🌐 Skipping preload on slow connection');
      return;
    }

    try {
      // Preload health check
      await this.optimizedRequest('/api/health');
      console.debug('🌐 Critical resources preloaded');

    } catch (error) {
      console.debug('Failed to preload critical resources:', error);
    }
  }

  /**
   * Cleanup network monitoring
   */
  cleanup(): void {
    this.isMonitoring = false;
    this.offlineQueue = [];
    memoryManager.cleanupComponent('NetworkOptimizer');
  }
}

// Export singleton instance
export const networkOptimizer = NetworkOptimizer.getInstance();

export default networkOptimizer;

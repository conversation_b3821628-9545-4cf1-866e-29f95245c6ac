import type { NextApiRequest, NextApiResponse } from 'next';
import { GameRequest, GameResponse, GameState, DifficultyLevel, APIErrorResponse } from '@/types/game';
import { GameErrorHandler } from '@/utils/errorHandling';
import {
  validateDefinition,
  updateGameState,
  checkWinCondition,
  checkLossCondition,
  evaluateGameState,
  burnTarget,
  shouldBurnTarget,
  selectNextWord,
  validateAIResponse,
  logAIViolations,
  validateWinnablePath,
  createNewGame,
  isGameImpossible,
  getLastDefinition,
  getRemainingTargets
} from '@/utils/gameLogic';
import { callDeepSeek, generateInitialGame } from '@/utils/deepseek';
import { DEV_FLAGS, ERROR_MESSAGES, VALIDATION } from '@/utils/constants';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<GameResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      gameState: createNewGame(),
      error: 'Method not allowed'
    });
  }

  try {
    // Development logging
    if (DEV_FLAGS.ENABLE_LOGGING) {
      console.log('API Request received:', {
        method: req.method,
        body: req.body,
        hasBody: !!req.body,
        bodyKeys: req.body ? Object.keys(req.body) : []
      });
    }

    const { gameState, playerDefinition, action, difficulty }: GameRequest = req.body;

    if (DEV_FLAGS.ENABLE_LOGGING) {
      console.log('Parsed request:', { gameState: !!gameState, action, playerDefinition });
    }

    // Enhanced input validation and sanitization
    if (!action || typeof action !== 'string') {
      return res.status(400).json({
        success: false,
        gameState: createNewGame(),
        error: 'Invalid request: missing or invalid action'
      });
    }

    // Validate action type
    if (!['start', 'submit'].includes(action)) {
      return res.status(400).json({
        success: false,
        gameState: createNewGame(),
        error: 'Invalid action type'
      });
    }

    // Validate and sanitize player definition for submit actions
    if (action === 'submit') {
      if (!playerDefinition || typeof playerDefinition !== 'string') {
        return res.status(400).json({
          success: false,
          gameState: gameState || createNewGame(),
          error: 'Invalid request: missing or invalid definition'
        });
      }

      // Enhanced sanitization
      const sanitizedDefinition = playerDefinition
        .trim()
        .replace(/[<>]/g, '') // Remove potential HTML tags
        .replace(/script/gi, '') // Remove script keywords
        .replace(/javascript/gi, '') // Remove javascript keywords
        .replace(/[^\w\s'-]/g, '') // Only allow word characters, spaces, hyphens, apostrophes
        .replace(/\s+/g, ' ') // Normalize whitespace
        .substring(0, 200); // Stricter length limit

      if (!sanitizedDefinition || sanitizedDefinition.length < 2) {
        return res.status(400).json({
          success: false,
          gameState: gameState || createNewGame(),
          error: 'Definition must be at least 2 characters and contain valid words'
        });
      }

      // Update the request with sanitized definition
      req.body.playerDefinition = sanitizedDefinition;
    }

    // Validate game state for submit actions
    if (action === 'submit' && (!gameState || typeof gameState !== 'object')) {
      return res.status(400).json({
        success: false,
        gameState: createNewGame(),
        error: 'Invalid request: missing or invalid game state'
      });
    }

    // For 'start' action, gameState can be null
    if (action !== 'start' && !gameState) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Missing gameState for non-start action');
      }
      return res.status(400).json({
        success: false,
        gameState: createNewGame(),
        error: 'Invalid request: missing gameState'
      });
    }

    switch (action) {
      case 'start':
        return await handleStartGame(res, difficulty);

      case 'submit':
        return await handleSubmitDefinition(res, gameState, playerDefinition);

      case 'reset':
        return await handleStartGame(res, difficulty);

      default:
        return res.status(400).json({
          success: false,
          gameState,
          error: 'Invalid action'
        });
    }
  } catch (error) {
    GameErrorHandler.logError(error, 'Game API Handler');
    const errorResponse = GameErrorHandler.handleAPIError(error, 'Game API');

    return res.status(500).json({
      ...errorResponse,
      gameState: createNewGame()
    });
  }
}

/**
 * Handles starting a new game
 */
async function handleStartGame(res: NextApiResponse<GameResponse>, difficulty?: 'easy' | 'medium' | 'hard') {
  try {
    const selectedDifficulty = difficulty || 'medium'; // Default to medium
    const { startWord, targets } = await generateInitialGame();

    const newGameState: GameState = {
      ...createNewGame(selectedDifficulty),
      currentWord: startWord,
      targets,
      usedWords: [startWord.toLowerCase()]
    };

    return res.status(200).json({
      success: true,
      gameState: newGameState
    });
  } catch (error) {
    console.error('Failed to start game:', error);
    
    // Fallback to default game
    const selectedDifficulty = difficulty || 'medium';
    const fallbackGameState: GameState = {
      ...createNewGame(selectedDifficulty),
      currentWord: 'revolution',
      targets: ['circle', 'zero', 'music'],
      usedWords: ['revolution']
    };

    return res.status(200).json({
      success: true,
      gameState: fallbackGameState
    });
  }
}

/**
 * Handles player definition submission
 */
async function handleSubmitDefinition(
  res: NextApiResponse<GameResponse>,
  gameState: GameState,
  playerDefinition?: string
) {
  if (!playerDefinition) {
    return res.status(400).json({
      success: false,
      gameState,
      error: 'No definition provided'
    });
  }

  // Check if game is already over
  if (gameState.gameStatus === 'won' || gameState.gameStatus === 'lost') {
    return res.status(400).json({
      success: false,
      gameState,
      error: 'Game is already finished'
    });
  }

  // Validate the definition according to game rules
  const lastDefinition = getLastDefinition(gameState);
  const validation = validateDefinition(playerDefinition, gameState, lastDefinition);

  if (!validation.isValid) {
    // Get the first validation error for detailed tracking
    const firstError = validation.errors[0];
    const rejectionReason = firstError?.message || 'Invalid definition';
    const violationType = firstError?.type || 'invalid';

    // Update game state to track the rejection (three-strike system)
    const updatedGameState = updateGameState(
      gameState,
      playerDefinition,
      '',
      false,
      rejectionReason,
      violationType
    );

    // Check if this rejection caused a game loss
    const rejectionCount = updatedGameState.consecutiveRejections;
    const maxRejections = VALIDATION.MAX_CONSECUTIVE_REJECTIONS;

    const strikeMessage = rejectionCount >= maxRejections
      ? ` GAME OVER: ${maxRejections} consecutive rule violations.`
      : ` (Strike ${rejectionCount}/${maxRejections})`;

    // Enhanced error message with strike information
    const enhancedError = rejectionReason + strikeMessage;

    return res.status(400).json({
      success: false,
      gameState: updatedGameState,
      error: enhancedError
    });
  }

  try {
    // Get AI response
    const aiResponse = await callDeepSeek(gameState, playerDefinition, 'submit');

    if (!aiResponse.accept) {
      return res.status(400).json({
        success: false,
        gameState,
        error: aiResponse.reason || 'AI rejected the definition'
      });
    }

    // AI CONSTRAINT VALIDATION - MVP Core Mechanics
    const aiValidation = validateAIResponse(aiResponse, gameState, playerDefinition);
    let validatedAIResponse = aiResponse;

    if (!aiValidation.isValid) {
      logAIViolations(aiValidation.violations, gameState);

      // Use corrected response
      if (aiValidation.correctedResponse) {
        validatedAIResponse = aiValidation.correctedResponse;
      }

      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('🚨 AI CONSTRAINTS VIOLATED - CORRECTED:', {
          violations: aiValidation.violations,
          originalNextWord: aiResponse.nextWord,
          correctedNextWord: aiValidation.correctedResponse?.nextWord
        });
      }
    }

    // Validate winnable path guarantee
    const pathValidation = validateWinnablePath(gameState);
    if (!pathValidation.hasWinnablePath) {
      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.warn('⚠️ WINNABLE PATH VIOLATION:', pathValidation.reason);
      }
    }

    // DETERMINISTIC 80/20 WORD SELECTION - MVP Core Mechanics
    const wordSelection = selectNextWord(playerDefinition, gameState, validatedAIResponse.nextWord);

    if (DEV_FLAGS.ENABLE_LOGGING) {
      console.log('🎯 DETERMINISTIC WORD SELECTION:', {
        selectedWord: wordSelection.nextWord,
        selectionType: wordSelection.selectionType,
        reason: wordSelection.reason,
        aiSuggestion: validatedAIResponse.nextWord
      });
    }

    // Update game state with the deterministically selected word
    let updatedGameState = updateGameState(
      gameState,
      playerDefinition,
      wordSelection.nextWord,
      true
    );

    // DISABLE AI-controlled burning - use strategic burning only
    // The AI should not directly burn targets, only the strategic system should
    if (aiResponse.burnedTarget && DEV_FLAGS.ENABLE_LOGGING) {
      console.log('🚫 IGNORING AI burn request:', aiResponse.burnedTarget, '- Using strategic burning only');
    }

    // HYBRID WIN/LOSS DETECTION - MVP Core Mechanics
    // First check AI decision, then fallback to deterministic
    const gameEvaluation = evaluateGameState(playerDefinition, updatedGameState);

    // Check AI win decision first (primary method)
    let finalGameStatus = gameEvaluation.gameStatus;
    let winDetectionMethod: 'ai' | 'deterministic' | 'hybrid' = 'deterministic';
    let finalReason = gameEvaluation.reason;

    if (aiResponse.gameResult === 'player_wins') {
      finalGameStatus = 'won';
      winDetectionMethod = 'ai';
      finalReason = 'AI detected semantic win: ' + (aiResponse.resultReason || 'Player achieved victory');

      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('🎯 AI WIN DETECTION TRIGGERED:', {
          aiDecision: aiResponse.gameResult,
          aiReason: aiResponse.resultReason,
          deterministicResult: gameEvaluation.gameStatus
        });
      }
    } else if (aiResponse.gameResult === 'ai_wins') {
      finalGameStatus = 'lost';
      winDetectionMethod = 'ai';
      finalReason = 'AI detected loss: ' + (aiResponse.resultReason || 'Player failed to meet victory conditions');

      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('🎯 AI LOSS DETECTION TRIGGERED:', {
          aiDecision: aiResponse.gameResult,
          aiReason: aiResponse.resultReason,
          deterministicResult: gameEvaluation.gameStatus
        });
      }
    }

    const debugInfo = {
      aiRawResponse: '', // Will be filled by parseAIResponse
      winDetectionMethod,
      winCheckResults: {
        directWin: gameEvaluation.gameStatus === 'won',
        semanticWin: finalGameStatus === 'won',
        aiDecision: `AI: ${aiResponse.gameResult} | Deterministic: ${gameEvaluation.reason} | Final: ${finalReason}`
      },
      gameStateAnalysis: {
        remainingTargets: getRemainingTargets(updatedGameState),
        burnedTargets: updatedGameState.burnedTargets,
        currentWord: updatedGameState.currentWord || '',
        step: updatedGameState.step,
        isImpossible: gameEvaluation.gameStatus === 'lost' && gameEvaluation.lossType === 'impossible'
      },
      timestamp: Date.now(),
      gameEvaluation // Add the full evaluation for debugging
    };

    if (DEV_FLAGS.ENABLE_LOGGING) {
      console.log('🎯 Win Detection Debug:', {
        aiGameResult: aiResponse.gameResult,
        deterministicResult: gameEvaluation.gameStatus,
        finalDecision: finalGameStatus,
        method: winDetectionMethod,
        currentWord: gameState.currentWord,
        targets: gameState.targets,
        burnedTargets: gameState.burnedTargets,
        playerDefinition,
        debugInfo
      });
    }

    // Apply the final game status
    if (finalGameStatus === 'won') {
      debugInfo.winCheckResults.directWin = true;
      debugInfo.winCheckResults.semanticWin = true;

      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('✅ WIN DETECTED:', {
          method: winDetectionMethod,
          winType: gameEvaluation.winType,
          targetWord: gameEvaluation.targetWord,
          reason: finalReason,
          aiDecision: aiResponse.gameResult
        });
      }

      updatedGameState = {
        ...updatedGameState,
        gameStatus: 'won'
      };
    } else if (finalGameStatus === 'lost') {
      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('❌ LOSS DETECTED:', {
          method: winDetectionMethod,
          lossType: gameEvaluation.lossType,
          reason: finalReason,
          aiDecision: aiResponse.gameResult
        });
      }

      updatedGameState = {
        ...updatedGameState,
        gameStatus: 'lost'
      };
    } else {
      // Game continues - use AI for next word selection only
      // STRATEGIC TARGET BURNING - MVP Core Mechanics
      const burnDecision = shouldBurnTarget(updatedGameState, playerDefinition);

      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('🔥 BURN DECISION DEBUG:', {
          step: updatedGameState.step,
          difficulty: updatedGameState.difficulty,
          shouldBurn: burnDecision.shouldBurn,
          reason: burnDecision.reason,
          remainingTargets: getRemainingTargets(updatedGameState).length,
          burnFrequency: updatedGameState.difficulty === 'easy' ? 8 : updatedGameState.difficulty === 'medium' ? 6 : 5
        });
      }

      if (burnDecision.shouldBurn && burnDecision.targetToBurn) {
        updatedGameState = burnTarget(updatedGameState, burnDecision.targetToBurn);

        if (DEV_FLAGS.ENABLE_LOGGING) {
          console.log('🔥 STRATEGIC TARGET BURNED:', {
            target: burnDecision.targetToBurn,
            reason: burnDecision.reason,
            remainingTargets: getRemainingTargets(updatedGameState)
          });
        }

        // Add burn info to debug
        debugInfo.winCheckResults.aiDecision += ` | BURNED: ${burnDecision.targetToBurn} (${burnDecision.reason})`;
      }

      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('🎮 GAME CONTINUES:', {
          reason: finalReason,
          remainingTargets: getRemainingTargets(updatedGameState),
          remainingSteps: updatedGameState.maxSteps - updatedGameState.step,
          burnDecision: burnDecision.shouldBurn ? `Burned ${burnDecision.targetToBurn}` : 'No burn'
        });
      }
    }

    // Loss conditions are now handled deterministically above
    // No additional checks needed

    // Update player confidence from AI analysis
    if (aiResponse.internalAnalysis?.playerConfidence !== undefined) {
      updatedGameState = {
        ...updatedGameState,
        playerConfidence: aiResponse.internalAnalysis.playerConfidence / 100
      };
    }

    // Add debug info to AI response
    const enhancedAIResponse = {
      ...aiResponse,
      debugInfo
    };

    return res.status(200).json({
      success: true,
      gameState: updatedGameState,
      aiResponse: enhancedAIResponse
    });

  } catch (error) {
    console.error('AI processing error:', error);

    // Fallback: Accept the definition and continue with simple logic
    if (process.env.NODE_ENV === 'development') {
      console.log('Using fallback game logic due to AI timeout');
    }

    // DETERMINISTIC 80/20 WORD SELECTION - Fallback Mode
    const fallbackWordSelection = selectNextWord(playerDefinition!, gameState);

    if (DEV_FLAGS.ENABLE_LOGGING) {
      console.log('🎯 FALLBACK DETERMINISTIC WORD SELECTION:', {
        selectedWord: fallbackWordSelection.nextWord,
        selectionType: fallbackWordSelection.selectionType,
        reason: fallbackWordSelection.reason
      });
    }

    let updatedGameState = updateGameState(
      gameState,
      playerDefinition!,
      fallbackWordSelection.nextWord,
      true
    );

    // DETERMINISTIC win/loss check for fallback
    const fallbackEvaluation = evaluateGameState(playerDefinition!, updatedGameState);

    if (fallbackEvaluation.gameStatus === 'won') {
      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('✅ Fallback deterministic win detected:', {
          winType: fallbackEvaluation.winType,
          targetWord: fallbackEvaluation.targetWord,
          reason: fallbackEvaluation.reason
        });
      }
      updatedGameState = {
        ...updatedGameState,
        gameStatus: 'won'
      };
    } else if (fallbackEvaluation.gameStatus === 'lost') {
      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('❌ Fallback deterministic loss detected:', {
          lossType: fallbackEvaluation.lossType,
          reason: fallbackEvaluation.reason
        });
      }
      updatedGameState = {
        ...updatedGameState,
        gameStatus: 'lost'
      };
    }

    return res.status(200).json({
      success: true,
      gameState: updatedGameState,
      aiResponse: {
        accept: true,
        reason: 'Definition accepted (AI temporarily unavailable)',
        nextWord: fallbackWordSelection.nextWord,
        internalAnalysis: {
          playerConfidence: 50,
          remainingPaths: getRemainingTargets(updatedGameState),
          recommendedBurn: null,
          difficultyAdjustment: 0
        }
      }
    });
  }
}

/**
 * Health check endpoint
 */
export async function healthCheck(): Promise<boolean> {
  try {
    // Simple test to verify API connectivity
    const testGameState = createNewGame();
    await callDeepSeek(testGameState, undefined, 'start');
    return true;
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
}

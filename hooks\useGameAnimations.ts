import { useEffect, useRef, useCallback } from 'react';
import { GameState } from '@/types/game';
import { useAnimation } from '@/contexts/AnimationContext';

interface GameAnimationOptions {
  enableWordChangeAnimation?: boolean;
  enableTargetBurnAnimation?: boolean;
  enableDefinitionAnimation?: boolean;
  enableGameEndAnimation?: boolean;
  animationDelay?: number;
}

interface UseGameAnimationsReturn {
  triggerDefinitionSubmit: () => void;
  triggerDefinitionAccepted: (data?: any) => void;
  triggerDefinitionRejected: (error?: string) => void;
  triggerTargetBurned: (target: string) => void;
  triggerTargetCompleted: (target: string) => void;
  triggerGameWon: () => void;
  triggerGameLost: () => void;
  triggerAIThinking: () => void;
  triggerWordChange: (newWord: string) => void;
  isAnimating: boolean;
  currentAnimation: string;
}

export const useGameAnimations = (
  gameState: GameState | null,
  options: GameAnimationOptions = {}
): UseGameAnimationsReturn => {
  const {
    enableWordChangeAnimation = true,
    enableTargetBurnAnimation = true,
    enableDefinitionAnimation = true,
    enableGameEndAnimation = true,
    animationDelay = 0,
  } = options;

  const {
    state: animationState,
    triggerAnimation,
    queueAnimation,
    updateGameAnimations,
  } = useAnimation();

  const previousGameState = useRef<GameState | null>(null);
  const previousWord = useRef<string>('');
  const previousTargets = useRef<string[]>([]);
  const previousBurnedTargets = useRef<string[]>([]);
  const previousDefinitionsCount = useRef<number>(0);

  // Update animation preferences based on options
  useEffect(() => {
    updateGameAnimations({
      wordChange: enableWordChangeAnimation,
      targetBurn: enableTargetBurnAnimation,
      definitionSubmit: enableDefinitionAnimation,
      gameEnd: enableGameEndAnimation,
    });
  }, [
    enableWordChangeAnimation,
    enableTargetBurnAnimation,
    enableDefinitionAnimation,
    enableGameEndAnimation,
    updateGameAnimations,
  ]);

  // Watch for game state changes and trigger appropriate animations
  useEffect(() => {
    if (!gameState || !previousGameState.current) {
      previousGameState.current = gameState;
      if (gameState) {
        previousWord.current = gameState.currentWord || '';
        previousTargets.current = [...gameState.targets];
        previousBurnedTargets.current = [...gameState.burnedTargets];
        previousDefinitionsCount.current = gameState.definitions.length;
      }
      return;
    }

    const prev = previousGameState.current;
    const current = gameState;

    // Word change animation
    if (enableWordChangeAnimation && prev.currentWord !== current.currentWord) {
      triggerAnimation('word_change', { 
        oldWord: prev.currentWord, 
        newWord: current.currentWord 
      }, { delay: animationDelay });
    }

    // Target burned animation
    if (enableTargetBurnAnimation && current.burnedTargets.length > prev.burnedTargets.length) {
      const newlyBurned = current.burnedTargets.filter(target => !prev.burnedTargets.includes(target));
      newlyBurned.forEach((target, index) => {
        queueAnimation('target_burned', { target }, { 
          delay: animationDelay + (index * 200) 
        });
      });
    }

    // Definition accepted animation (new definition added)
    if (enableDefinitionAnimation && current.definitions.length > prev.definitions.length) {
      const latestDefinition = current.definitions[current.definitions.length - 1];
      triggerAnimation('definition_accepted', { 
        definition: latestDefinition 
      }, { delay: animationDelay });
    }

    // Game end animations
    if (enableGameEndAnimation) {
      if (prev.gameStatus !== 'won' && current.gameStatus === 'won') {
        triggerAnimation('game_won', { 
          finalStep: current.step,
          completedTargets: current.completedTargets 
        }, { delay: animationDelay });
      }
      
      if (prev.gameStatus !== 'lost' && current.gameStatus === 'lost') {
        triggerAnimation('game_lost', { 
          finalStep: current.step,
          reason: 'Game over' 
        }, { delay: animationDelay });
      }
    }

    // Update previous state
    previousGameState.current = current;
    previousWord.current = current.currentWord || '';
    previousTargets.current = [...current.targets];
    previousBurnedTargets.current = [...current.burnedTargets];
    previousDefinitionsCount.current = current.definitions.length;
  }, [
    gameState,
    enableWordChangeAnimation,
    enableTargetBurnAnimation,
    enableDefinitionAnimation,
    enableGameEndAnimation,
    animationDelay,
    triggerAnimation,
    queueAnimation,
  ]);

  // Manual animation triggers for UI interactions
  const triggerDefinitionSubmit = useCallback(() => {
    if (enableDefinitionAnimation) {
      triggerAnimation('definition_submit');
    }
  }, [enableDefinitionAnimation, triggerAnimation]);

  const triggerDefinitionAccepted = useCallback((data?: any) => {
    if (enableDefinitionAnimation) {
      triggerAnimation('definition_accepted', data);
    }
  }, [enableDefinitionAnimation, triggerAnimation]);

  const triggerDefinitionRejected = useCallback((error?: string) => {
    if (enableDefinitionAnimation) {
      triggerAnimation('definition_rejected', { error });
    }
  }, [enableDefinitionAnimation, triggerAnimation]);

  const triggerTargetBurned = useCallback((target: string) => {
    if (enableTargetBurnAnimation) {
      triggerAnimation('target_burned', { target });
    }
  }, [enableTargetBurnAnimation, triggerAnimation]);

  const triggerTargetCompleted = useCallback((target: string) => {
    if (enableTargetBurnAnimation) {
      triggerAnimation('target_completed', { target });
    }
  }, [enableTargetBurnAnimation, triggerAnimation]);

  const triggerGameWon = useCallback(() => {
    if (enableGameEndAnimation) {
      triggerAnimation('game_won');
    }
  }, [enableGameEndAnimation, triggerAnimation]);

  const triggerGameLost = useCallback(() => {
    if (enableGameEndAnimation) {
      triggerAnimation('game_lost');
    }
  }, [enableGameEndAnimation, triggerAnimation]);

  const triggerAIThinking = useCallback(() => {
    triggerAnimation('ai_thinking');
  }, [triggerAnimation]);

  const triggerWordChange = useCallback((newWord: string) => {
    if (enableWordChangeAnimation) {
      triggerAnimation('word_change', { newWord });
    }
  }, [enableWordChangeAnimation, triggerAnimation]);

  return {
    triggerDefinitionSubmit,
    triggerDefinitionAccepted,
    triggerDefinitionRejected,
    triggerTargetBurned,
    triggerTargetCompleted,
    triggerGameWon,
    triggerGameLost,
    triggerAIThinking,
    triggerWordChange,
    isAnimating: animationState.isAnimating,
    currentAnimation: animationState.currentAnimation,
  };
};

// Hook for component-specific animations
export const useComponentAnimation = (componentName: string) => {
  const { state, triggerAnimation } = useAnimation();
  
  const triggerComponentAnimation = useCallback((
    animationType: 'enter' | 'exit' | 'update' | 'error' | 'success',
    data?: any
  ) => {
    const trigger = `${componentName}_${animationType}` as any;
    triggerAnimation(trigger, data);
  }, [componentName, triggerAnimation]);

  return {
    isAnimating: state.isAnimating,
    currentAnimation: state.currentAnimation,
    triggerComponentAnimation,
  };
};

// Hook for micro-interactions
export const useMicroInteractions = () => {
  const { triggerAnimation } = useAnimation();

  const triggerHover = useCallback((element: string) => {
    // Micro-interaction for hover states
    triggerAnimation('hover' as any, { element }, { duration: 150 });
  }, [triggerAnimation]);

  const triggerClick = useCallback((element: string) => {
    // Micro-interaction for click feedback
    triggerAnimation('click' as any, { element }, { duration: 100 });
  }, [triggerAnimation]);

  const triggerFocus = useCallback((element: string) => {
    // Micro-interaction for focus states
    triggerAnimation('focus' as any, { element }, { duration: 200 });
  }, [triggerAnimation]);

  return {
    triggerHover,
    triggerClick,
    triggerFocus,
  };
};

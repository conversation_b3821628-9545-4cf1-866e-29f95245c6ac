/**
 * SmartValidationFeedback - Contextual Input Validation (v2.0 Spatial Design)
 *
 * 🎯 INTELLIGENT VALIDATION FEEDBACK
 * 
 * Features:
 * - Context-aware validation messages that adapt to user behavior
 * - Progressive disclosure of validation rules (show only when relevant)
 * - Smart timing that doesn't interrupt flow
 * - Adaptive messaging based on player skill level
 * - Noise reduction through intelligent filtering
 * 
 * Validation Categories:
 * - Immediate: Critical errors that prevent submission
 * - Contextual: Warnings that appear based on input patterns
 * - Educational: Tips that help improve definition quality
 * - Strategic: Suggestions for better gameplay outcomes
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React, { useState, useEffect, useMemo } from 'react';
import { GameState, PlayerProfile } from '@/types/game';
import { Secondary, Small, Status } from '@/components/ui/Typography';
import { useFeedback } from '@/contexts/FeedbackContext';

interface ValidationRule {
  id: string;
  type: 'immediate' | 'contextual' | 'educational' | 'strategic';
  priority: number;
  check: (input: string, context: ValidationContext) => boolean;
  message: (input: string, context: ValidationContext) => string;
  suggestion?: (input: string, context: ValidationContext) => string;
  showDelay?: number; // Delay before showing (ms)
  hideDelay?: number; // Delay before hiding (ms)
}

interface ValidationContext {
  gameState: GameState;
  playerProfile: PlayerProfile;
  inputFocused: boolean;
  typingPaused: boolean;
  wordCount: number;
  maxWords: number;
  usedWords: string[];
  commonWords: string[];
  targetWords: string[];
}

interface SmartValidationFeedbackProps {
  input: string;
  gameState: GameState;
  playerProfile: PlayerProfile;
  inputFocused: boolean;
  maxWords: number;
  onValidationChange?: (isValid: boolean, errors: string[]) => void;
  className?: string;
}

export const SmartValidationFeedback: React.FC<SmartValidationFeedbackProps> = ({
  input,
  gameState,
  playerProfile,
  inputFocused,
  maxWords,
  onValidationChange,
  className = ''
}) => {
  const { state: feedbackState, shouldShowFeedback } = useFeedback();
  const [typingPaused, setTypingPaused] = useState(false);
  const [visibleValidations, setVisibleValidations] = useState<Set<string>>(new Set());
  const [typingTimer, setTypingTimer] = useState<NodeJS.Timeout | null>(null);

  // Track typing pauses
  useEffect(() => {
    if (typingTimer) {
      clearTimeout(typingTimer);
    }

    setTypingPaused(false);
    const timer = setTimeout(() => {
      setTypingPaused(true);
    }, 1000); // Consider paused after 1 second

    setTypingTimer(timer);

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [input, typingTimer]);

  // Build validation context
  const context: ValidationContext = useMemo(() => {
    const words = input.trim().split(/\s+/).filter(word => word.length > 0);
    return {
      gameState,
      playerProfile,
      inputFocused,
      typingPaused,
      wordCount: words.length,
      maxWords,
      usedWords: gameState.usedWords || [],
      commonWords: ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'],
      targetWords: gameState.targets || []
    };
  }, [input, gameState, playerProfile, inputFocused, typingPaused, maxWords]);

  // Define validation rules
  const validationRules: ValidationRule[] = useMemo(() => [
    // IMMEDIATE VALIDATIONS (Critical errors)
    {
      id: 'word-count-exceeded',
      type: 'immediate',
      priority: 10,
      check: (input, ctx) => ctx.wordCount > ctx.maxWords,
      message: (input, ctx) => `DEFINITION TOO LONG: ${ctx.wordCount}/${ctx.maxWords} words`,
      suggestion: () => 'Remove unnecessary words to continue'
    },
    {
      id: 'empty-definition',
      type: 'immediate',
      priority: 9,
      check: (input, ctx) => input.trim().length === 0 && ctx.inputFocused,
      message: () => 'DEFINITION REQUIRED',
      suggestion: () => 'Enter a definition to proceed',
      showDelay: 2000
    },
    {
      id: 'circular-definition',
      type: 'immediate',
      priority: 8,
      check: (input, ctx) => {
        const inputLower = input.toLowerCase();
        return ctx.targetWords.some(target => 
          inputLower.includes(target.toLowerCase()) && target.length > 3
        );
      },
      message: () => 'CIRCULAR LOGIC DETECTED',
      suggestion: () => 'Definition cannot contain the target word'
    },

    // CONTEXTUAL VALIDATIONS (Warnings based on patterns)
    {
      id: 'approaching-word-limit',
      type: 'contextual',
      priority: 6,
      check: (input, ctx) => {
        const remaining = ctx.maxWords - ctx.wordCount;
        return remaining <= 2 && remaining > 0 && ctx.wordCount > 0;
      },
      message: (input, ctx) => {
        const remaining = ctx.maxWords - ctx.wordCount;
        return `APPROACHING LIMIT: ${remaining} word${remaining === 1 ? '' : 's'} remaining`;
      },
      showDelay: 500
    },
    {
      id: 'repeated-word-usage',
      type: 'contextual',
      priority: 5,
      check: (input, ctx) => {
        const words = input.toLowerCase().split(/\s+/);
        const wordCounts = words.reduce((acc, word) => {
          acc[word] = (acc[word] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        return Object.values(wordCounts).some(count => count > 1);
      },
      message: () => 'REPEATED WORDS DETECTED',
      suggestion: () => 'Consider using synonyms for variety',
      showDelay: 1500
    },
    {
      id: 'excessive-common-words',
      type: 'contextual',
      priority: 4,
      check: (input, ctx) => {
        const words = input.toLowerCase().split(/\s+/);
        const commonWordCount = words.filter(word => ctx.commonWords.includes(word)).length;
        return commonWordCount > Math.ceil(ctx.wordCount * 0.4) && ctx.wordCount > 3;
      },
      message: () => 'HIGH COMMON WORD USAGE',
      suggestion: () => 'Try more specific, descriptive words',
      showDelay: 2000
    },

    // EDUCATIONAL VALIDATIONS (Learning opportunities)
    {
      id: 'definition-too-vague',
      type: 'educational',
      priority: 3,
      check: (input, ctx) => {
        const vaguePhrases = ['thing', 'stuff', 'something', 'anything', 'everything'];
        const inputLower = input.toLowerCase();
        return vaguePhrases.some(phrase => inputLower.includes(phrase)) && 
               ctx.playerProfile.gamesPlayed <= 5;
      },
      message: () => 'VAGUE LANGUAGE DETECTED',
      suggestion: () => 'Be more specific for better AI understanding',
      showDelay: 3000
    },
    {
      id: 'definition-too-short',
      type: 'educational',
      priority: 2,
      check: (input, ctx) => {
        return ctx.wordCount === 1 && ctx.typingPaused && 
               ctx.playerProfile.gamesPlayed <= 3;
      },
      message: () => 'VERY SHORT DEFINITION',
      suggestion: () => 'Consider adding more detail for clarity',
      showDelay: 2000
    },

    // STRATEGIC VALIDATIONS (Gameplay optimization)
    {
      id: 'inefficient-word-usage',
      type: 'strategic',
      priority: 3,
      check: (input, ctx) => {
        return ctx.wordCount > ctx.maxWords * 0.8 && 
               ctx.gameState.step > 5 && 
               feedbackState.learningData.playerSkillLevel !== 'beginner';
      },
      message: () => 'WORD BUDGET WARNING',
      suggestion: () => 'Consider shorter definitions for efficiency',
      showDelay: 1000
    }
  ], [feedbackState.learningData.playerSkillLevel]);

  // Evaluate active validations
  const activeValidations = useMemo(() => {
    return validationRules
      .filter(rule => {
        // Check if validation should show based on feedback preferences
        if (!shouldShowFeedback(rule.id, rule.type, context)) {
          return false;
        }

        // Check the validation condition
        return rule.check(input, context);
      })
      .sort((a, b) => b.priority - a.priority);
  }, [validationRules, input, context, shouldShowFeedback]);

  // Handle delayed visibility
  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];

    activeValidations.forEach(validation => {
      if (validation.showDelay && !visibleValidations.has(validation.id)) {
        const timer = setTimeout(() => {
          setVisibleValidations(prev => new Set(Array.from(prev).concat(validation.id)));
        }, validation.showDelay);
        timers.push(timer);
      } else if (!validation.showDelay) {
        setVisibleValidations(prev => new Set(Array.from(prev).concat(validation.id)));
      }
    });

    // Remove validations that are no longer active
    const activeIds = new Set(activeValidations.map(v => v.id));
    setVisibleValidations(prev =>
      new Set(Array.from(prev).filter(id => activeIds.has(id)))
    );

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [activeValidations, visibleValidations]);

  // Notify parent of validation state
  useEffect(() => {
    const immediateErrors = activeValidations
      .filter(v => v.type === 'immediate')
      .map(v => v.message(input, context));
    
    const isValid = immediateErrors.length === 0;
    onValidationChange?.(isValid, immediateErrors);
  }, [activeValidations, input, context, onValidationChange]);

  // Filter to visible validations
  const displayValidations = activeValidations.filter(v => 
    !v.showDelay || visibleValidations.has(v.id)
  );

  if (displayValidations.length === 0) {
    return null;
  }

  return (
    <div className={`smart-validation-feedback ${className}`}>
      {displayValidations.map(validation => (
        <ValidationMessage
          key={validation.id}
          validation={validation}
          input={input}
          context={context}
        />
      ))}

      <style jsx>{`
        .smart-validation-feedback {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
          margin-top: var(--space-3);
        }
      `}</style>
    </div>
  );
};

// Individual validation message component
interface ValidationMessageProps {
  validation: ValidationRule;
  input: string;
  context: ValidationContext;
}

const ValidationMessage: React.FC<ValidationMessageProps> = ({
  validation,
  input,
  context
}) => {
  const getStatusType = () => {
    switch (validation.type) {
      case 'immediate': return 'error';
      case 'contextual': return 'warning';
      case 'educational': return 'info';
      case 'strategic': return 'info';
      default: return 'info';
    }
  };

  return (
    <div className={`validation-message validation-message--${validation.type}`}>
      <Status status={getStatusType()}>
        {validation.message(input, context)}
      </Status>
      
      {validation.suggestion && (
        <Small className="validation-suggestion">
          → {validation.suggestion(input, context)}
        </Small>
      )}

      <style jsx>{`
        .validation-message {
          display: flex;
          flex-direction: column;
          gap: var(--space-1);
          animation: validation-enter 0.3s ease-out;
        }

        .validation-suggestion {
          margin-left: var(--space-4);
          opacity: 0.8;
          color: var(--text-muted);
        }

        @keyframes validation-enter {
          0% {
            opacity: 0;
            transform: translateY(-8px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @media (prefers-reduced-motion: reduce) {
          .validation-message {
            animation: none;
          }
        }
      `}</style>
    </div>
  );
};

export default SmartValidationFeedback;

/**
 * GameBoard Component - Spatial Design System (v2.0)
 *
 * ✅ WEEK 6 MIGRATION COMPLETE:
 * This component has been fully migrated to the spatial design system.
 * - Modern pattern: Gap-based, natural content flow
 * - Performance: CSS modules, optimized state management
 * - Accessibility: WCAG 2.1 AA compliant
 * - Design: Complete spatial design integration
 *
 * Status: ✅ COMPLETE - Fully migrated to spatial system
 * Version: 2.0 - Production ready
 *
 * @see components/GameBoard.module.css - Optimized CSS module
 * @see hooks/useGameState.ts - State management
 * @see docs/WEEK_6_GAMEBOARD_COMPLETION.md - Migration documentation
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { GameState, UIState, GameResponse, AIResponse, DifficultyLevel } from '@/types/game';
import { GameErrorHandler } from '@/utils/errorHandling';

// Custom Hooks for State Management
import useGameState from '@/hooks/useGameState';
import useUIState from '@/hooks/useUIState';
import useGameControls from '@/hooks/useGameControls';
import useSpatialLayout from '@/hooks/useSpatialLayout';
import useDevTools from '@/hooks/useDevTools';

// Game Components
import GameLayout from '@/components/layout/GameLayout';
import GameHeader from '@/components/game/GameHeader';
import GameOverScreen from '@/components/game/GameOverScreen';
import GameErrorDisplay from '@/components/game/GameErrorDisplay';
import GameDisplay from '@/components/game/GameDisplay';
import GameInput from '@/components/game/GameInput';
import GameControls from '@/components/game/GameControls';
import { formatGameProgress, getRemainingTargets, calculateMaxWords } from '@/utils/gameLogic';
import { useGameAnimations } from '@/hooks/useGameAnimations';
import {
  performanceMonitor,
  usePerformanceMonitoring,
  debounce,
  optimizeGameState,
  isLowEndDevice
} from '@/utils/performance';
import { triggerTrashTalk, getSpecialTrashTalk } from '@/utils/aiTrashTalk';

// Legacy components (still needed for some features)
import GameRules from './GameRules';
import DevPanel from './DevPanel';
import LoadingSpinner from './ui/LoadingSpinner';
import DifficultySelector from './DifficultySelector';
import PostGameAnalysis from './PostGameAnalysis';

// New spatial design components
import GameFocus from '@/components/game/GameFocus';
import CurrentChallenge from '@/components/game/CurrentChallenge';
import DefinitionInput from '@/components/game/DefinitionInput';
import TargetRevelationStrip from '@/components/game/TargetRevelationStrip';
import CollapsibleSidePanel from '@/components/layout/CollapsibleSidePanel';
import GameStatsPanel from '@/components/panels/GameStatsPanel';
import DefinitionHistoryPanel from '@/components/panels/DefinitionHistoryPanel';
import { Hero, Primary, Secondary } from '@/components/ui/Typography';
import { PerformanceDashboard } from '@/components/dev/PerformanceDashboard';
import FloatingChatDialog from '@/components/chat/FloatingChatDialog';
import { SkipLinks } from '@/components/accessibility/SkipLinks';
import { ARIAEnhancer, useARIALabels } from '@/components/accessibility/ARIAEnhancer';
import styles from './GameBoard.module.css';

interface GameBoardProps {
  initialGameState?: GameState;
}

const GameBoard: React.FC<GameBoardProps> = ({ initialGameState }) => {
  // Performance monitoring
  const { trackRender, measureOperation } = usePerformanceMonitoring('GameBoard');
  const isLowEnd = useMemo(() => isLowEndDevice(), []);

  // Track component renders
  useEffect(() => {
    trackRender();
  });

  // Custom Hooks for State Management
  const {
    gameState,
    lastAIResponse,
    error,
    setGameState,
    setLastAIResponse,
    setError,
    updateGameState,
    batchUpdateGameState,
    resetGameState,
    clearError,
    getStateMetrics
  } = useGameState({
    initialGameState: initialGameState || null,
    enablePerformanceMonitoring: process.env.NODE_ENV === 'development'
  });

  const {
    uiState,
    setUIState,
    updateUIState,
    setLoading,
    setInputValue,
    setAnimationState,
    toggleRules,
    toggleHistory,
    batchUpdateUI,
    resetUIState,
    isInputValid,
    wordCount,
    getUIMetrics
  } = useUIState({
    debounceDelay: 300,
    enablePerformanceMonitoring: process.env.NODE_ENV === 'development'
  });

  const {
    selectedDifficulty,
    showDifficultySelector,
    showValidationFeedback,
    showPostGameAnalysis,
    setSelectedDifficulty,
    setShowDifficultySelector,
    setShowValidationFeedback,
    setShowPostGameAnalysis,
    changeDifficulty,
    toggleValidationFeedback,
    openPostGameAnalysis,
    closePostGameAnalysis,
    batchUpdateControls,
    resetControls,
    startNewGame: startNewGameControls,
    endGame: endGameControls,
    getControlsMetrics
  } = useGameControls({
    initialDifficulty: 'medium',
    enablePerformanceMonitoring: process.env.NODE_ENV === 'development',
    persistPreferences: true
  });

  const {
    showSidePanel,
    sidePanelTab,
    isResponsiveMode,
    setShowSidePanel,
    setSidePanelTab,
    toggleSidePanel,
    changeSidePanelTab,
    openSidePanelWithTab,
    closeSidePanel,
    handleResponsiveChange,
    batchUpdateLayout,
    resetLayout,
    getLayoutMetrics
  } = useSpatialLayout({
    enablePerformanceMonitoring: process.env.NODE_ENV === 'development',
    enableAnimations: true,
    responsiveBreakpoint: 768
  });

  const {
    showDevPanel,
    showPerformanceDashboard,
    isDevelopment,
    setShowDevPanel,
    setShowPerformanceDashboard,
    toggleDevPanel,
    togglePerformanceDashboard,
    trackPerformance,
    getPerformanceMetrics,
    clearPerformanceMetrics,
    logDebugInfo,
    measureRenderTime,
    resetDevTools
  } = useDevTools({
    enablePerformanceMonitoring: process.env.NODE_ENV === 'development',
    enableDevPanel: process.env.NODE_ENV === 'development',
    enablePerformanceDashboard: process.env.NODE_ENV === 'development'
  });

  // Chat dialog is always available (no state needed)

  // Accessibility enhancements
  const ariaLabels = useARIALabels(gameState || undefined);

  // Animation system integration (optimized for low-end devices)
  const gameAnimations = useGameAnimations(gameState, {
    enableWordChangeAnimation: !isLowEnd,
    enableTargetBurnAnimation: !isLowEnd,
    enableDefinitionAnimation: true,
    enableGameEndAnimation: true,
    animationDelay: isLowEnd ? 50 : 100,
  });

  // Optimized game state with performance monitoring
  const optimizedGameState = useMemo(() => {
    if (!gameState) return null;
    const optimized = optimizeGameState(gameState);
    performanceMonitor.trackGameStateSize(optimized);
    return optimized;
  }, [gameState]);

  // Request debouncing and race condition prevention
  const submitTimeoutRef = useRef<NodeJS.Timeout>();
  const isSubmittingRef = useRef<boolean>(false);
  const animationTimeoutRef = useRef<NodeJS.Timeout>();

  // Initialize game on component mount
  useEffect(() => {
    if (!gameState) {
      startNewGame();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      // Clear any pending timeouts to prevent memory leaks
      const submitTimeout = submitTimeoutRef.current;
      const animationTimeout = animationTimeoutRef.current;

      if (submitTimeout) {
        clearTimeout(submitTimeout);
        submitTimeoutRef.current = undefined;
      }
      if (animationTimeout) {
        clearTimeout(animationTimeout);
        animationTimeoutRef.current = undefined;
      }
      // Reset submission flag
      isSubmittingRef.current = false;
    };
  }, []);

  /**
   * Starts a new game
   */
  const startNewGame = async (difficulty?: DifficultyLevel) => {
    const difficultyToUse = difficulty || selectedDifficulty;
    batchUpdateUI({ isLoading: true, animationState: 'typing' });
    clearError();
    setShowDifficultySelector(false);

    try {
      const data: GameResponse = await performanceMonitor.measureApiCall(async () => {
        const response = await fetch('/api/game', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            gameState: null,
            action: 'start',
            difficulty: difficultyToUse
          })
        });
        return response.json();
      }, 'start_game');

      // Development logging
      if (process.env.NODE_ENV === 'development') {
        console.log('API Response:', {
          success: data.success,
          hasGameState: !!data.gameState,
          error: data.error,
          gameState: data.gameState
        });
      }

      if (data.success && data.gameState) {
        setGameState(data.gameState);

        // Expose game state globally for chat system
        (window as any).currentGameState = data.gameState;

        batchUpdateUI({
          isLoading: false,
          animationState: 'idle',
          inputValue: '',
          wordCount: 0
        });

        // Trigger game start trash talk
        setTimeout(() => {
          console.log('🎮 GameBoard: Starting trash talk sequence');

          // Test direct chat dialog access
          if (typeof window !== 'undefined' && (window as any).addAITrashTalk) {
            console.log('🎮 Direct chat test: Sending welcome message');
            console.log('🎮 Global function available:', typeof (window as any).addAITrashTalk);
            (window as any).addAITrashTalk('Welcome to DEFEATER.AI. Let the psychological warfare begin.', 'start');
          } else {
            console.warn('🎮 Global function not available at game start');
          }

          console.log('🎮 Triggering trash talk with context:', {
            trigger: 'move',
            currentWord: data.gameState.currentWord,
            step: 0,
            maxSteps: data.gameState.maxSteps
          });

          triggerTrashTalk({
            trigger: 'move',
            currentWord: data.gameState.currentWord || undefined,
            step: 0,
            maxSteps: data.gameState.maxSteps
          });
        }, 1500);
      } else {
        throw new Error(data.error || 'Failed to start game - no game state received');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start game');
      batchUpdateUI({ isLoading: false, animationState: 'idle' });
    }
  };

  /**
   * Sanitizes user input using enhanced error handling
   */
  const sanitizeInput = (input: string): string | null => {
    const validation = GameErrorHandler.validateInput(input, 200);

    if (!validation.isValid) {
      if (validation.error) {
        GameErrorHandler.logError(validation.error, 'Input Sanitization');
        setError(GameErrorHandler.getUserMessage(validation.error));
      }
      return null;
    }

    return validation.sanitized || null;
  };

  /**
   * Submits a player definition with debouncing and race condition prevention
   */
  const submitDefinition = useCallback(async (definition: string) => {
    if (!gameState || !definition.trim() || isSubmittingRef.current) return;

    // Sanitize input
    const sanitizedDefinition = sanitizeInput(definition);
    if (!sanitizedDefinition) {
      isSubmittingRef.current = false;
      return;
    }

    // Prevent race conditions
    isSubmittingRef.current = true;
    batchUpdateUI({ isLoading: true, animationState: 'thinking' });
    clearError();

    // Trigger animation for definition submission
    gameAnimations.triggerDefinitionSubmit();

    try {
      const data: GameResponse = await performanceMonitor.measureApiCall(async () => {
        const response = await fetch('/api/game', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            gameState: optimizedGameState,
            playerDefinition: sanitizedDefinition,
            action: 'submit'
          })
        });
        return response.json();
      }, 'submit_definition');

      if (data.success) {
        setGameState(data.gameState);

        // Expose updated game state globally for chat system
        (window as any).currentGameState = data.gameState;

        setLastAIResponse(data.aiResponse); // Capture AI response for dev panel

        // Trigger appropriate animations based on game state
        if (data.gameState.gameStatus === 'won') {
          gameAnimations.triggerGameWon();
          // Trigger win trash talk
          setTimeout(() => {
            const message = getSpecialTrashTalk('playerWin');
            triggerTrashTalk({
              trigger: 'win',
              currentWord: data.gameState.currentWord || undefined,
              step: data.gameState.step,
              maxSteps: data.gameState.maxSteps
            });
          }, 1000);
        } else {
          gameAnimations.triggerDefinitionAccepted(data.gameState);

          // Check if a target was burned
          const previousTargetCount = gameState.targets.length + gameState.burnedTargets.length;
          const currentTargetCount = data.gameState.targets.length + data.gameState.burnedTargets.length;
          const targetWasBurned = currentTargetCount < previousTargetCount;

          // Trigger appropriate trash talk
          setTimeout(() => {
            if (targetWasBurned) {
              // Target was burned - trigger burn trash talk
              triggerTrashTalk({
                trigger: 'burn',
                playerDefinition: sanitizedDefinition,
                currentWord: data.gameState.currentWord || undefined,
                step: data.gameState.step,
                maxSteps: data.gameState.maxSteps,
                burnedTargets: data.gameState.burnedTargets,
                targetsBurned: data.gameState.burnedTargets.length
              });
            } else {
              // Normal move - trigger move-based trash talk
              triggerTrashTalk({
                trigger: 'move',
                playerDefinition: sanitizedDefinition,
                currentWord: data.gameState.currentWord || undefined,
                step: data.gameState.step,
                maxSteps: data.gameState.maxSteps,
                burnedTargets: data.gameState.burnedTargets,
                consecutiveRejections: data.gameState.consecutiveRejections,
                targetsBurned: data.gameState.burnedTargets.length
              });
            }
          }, 800);
        }

        batchUpdateUI({
          isLoading: false,
          animationState: data.gameState.gameStatus === 'won' ? 'success' : 'idle',
          inputValue: '',
          wordCount: 0
        });
      } else {
        setError(data.error || 'Definition rejected');

        // Trigger rejection animation
        gameAnimations.triggerDefinitionRejected(data.error);

        // Trigger rejection trash talk
        setTimeout(() => {
          triggerTrashTalk({
            trigger: 'rejection',
            playerDefinition: sanitizedDefinition,
            currentWord: gameState.currentWord || undefined,
            step: gameState.step,
            maxSteps: gameState.maxSteps,
            consecutiveRejections: gameState.consecutiveRejections + 1
          });
        }, 1200);

        batchUpdateUI({
          isLoading: false,
          animationState: 'shake'
        });

        // Reset animation after shake with proper cleanup
        animationTimeoutRef.current = setTimeout(() => {
          setAnimationState('idle');
        }, 500);
      }
    } catch (err) {
      GameErrorHandler.logError(err, 'Submit Definition');
      const userMessage = GameErrorHandler.getUserMessage(err);
      setError(userMessage);
      batchUpdateUI({ isLoading: false, animationState: 'failure' });
    } finally {
      // Always reset the submission flag
      isSubmittingRef.current = false;
    }
  }, [gameState]);

  /**
   * Updates input value and word count (now handled by useUIState hook)
   */
  const handleInputChange = useCallback(
    debounce((value: string) => {
      setInputValue(value); // useUIState handles word counting automatically
    }, isLowEnd ? 300 : 150),
    [isLowEnd, setInputValue]
  );

  // Immediate input change for responsive typing
  const handleImmediateInputChange = (value: string) => {
    setInputValue(value); // useUIState handles this optimally
    handleInputChange(value);
  };

  // toggleRules is now provided by useUIState hook

  /**
   * Gets paranoia level based on game step
   */
  const getParanoiaLevel = (step: number): string => {
    if (step <= 5) return 'SCANNING';
    if (step <= 10) return 'ANALYZING';
    if (step <= 15) return 'PREDICTING';
    if (step <= 20) return 'PARANOID';
    return 'MAXIMUM PARANOIA';
  };

  /**
   * Gets paranoia message based on game step
   */
  const getParanoiaMessage = (step: number): string => {
    if (step <= 5) return 'Building your profile...';
    if (step <= 10) return 'Detecting patterns...';
    if (step <= 15) return 'Anticipating deception...';
    if (step <= 20) return 'Expecting mind games...';
    return 'Assuming everything is a trick!';
  };

  if (!gameState) {
    return (
      <div className="game-board">
        <div className={`${styles.loadingCard} text-center`}>
          <LoadingSpinner />
          <p className={`text-muted ${styles.loadingText}`}>Initializing DEFEATER.AI...</p>
        </div>
      </div>
    );
  }

  const remainingTargets = getRemainingTargets(gameState);
  const isGameOver = gameState.gameStatus === 'won' || gameState.gameStatus === 'lost';

  return (
    <>
      {/* Skip Links for Keyboard Navigation */}
      <SkipLinks />

      {/* Main Game Layout with ARIA Enhancement */}
      <ARIAEnhancer
        gameState={gameState}
        currentInput={uiState.inputValue}
        isLoading={uiState.isLoading}
        error={error}
      >
        {/* Game Header - Outside GameLayout for proper hierarchy */}
        <GameHeader
          isLoading={uiState.isLoading}
          showSidePanel={showSidePanel}
          onShowRules={toggleRules}
          onShowDifficultySelector={() => setShowDifficultySelector(true)}
          onToggleSidePanel={toggleSidePanel}
          onStartNewGame={() => startNewGame()}
          onTestChat={() => {
            console.log('🧪 Manual chat test triggered');
            if (typeof window !== 'undefined' && (window as any).addAITrashTalk) {
              console.log('🧪 Sending manual test message');
              (window as any).addAITrashTalk('Manual test message from debug button', 'move');
            } else {
              console.warn('🧪 Global function not available for manual test');
            }
          }}
          isDevelopment={process.env.NODE_ENV === 'development'}
        />

        <GameLayout>
          <GameFocus priority="hero">

      {/* Hero Challenge - Center Stage (Direct Component like Test Page) */}
      {gameState && !isGameOver && gameState.currentWord && (
        <CurrentChallenge
          word={gameState.currentWord}
          step={gameState.step}
          maxSteps={gameState.maxSteps}
          isLoading={uiState.isLoading}
          animationState={
            uiState.animationState === 'typing' ? 'idle' :
            uiState.animationState === 'failure' ? 'error' :
            uiState.animationState === 'success' ? 'success' : 'idle'
          }
          showProgress={true}
        />
      )}

      {/* Large Definition Input (Direct Component like Test Page) */}
      {!isGameOver && !showDifficultySelector && gameState && gameState.currentWord && (
        <DefinitionInput
          value={uiState.inputValue}
          onChange={handleImmediateInputChange}
          onSubmit={(definition) => submitDefinition(definition)}
          maxWords={wordCount > 0 ? Math.max(1, calculateMaxWords(
            gameState.step,
            gameState.difficulty,
            gameState.lastDefinitionLength
          )) : 15}
          showHints={true}
          isLoading={uiState.isLoading}
          animationState={
            uiState.animationState === 'typing' ? 'pulse' :
            uiState.animationState === 'failure' ? 'shake' :
            uiState.animationState === 'success' ? 'success' :
            'idle'
          }
        />
      )}

      {/* Target Revelation Strip (Direct Component like Test Page) */}
      {gameState && gameState.targets.length > 0 && (
        <TargetRevelationStrip
          targets={gameState.targets.map(target => ({
            word: target,
            revealed: target.split('').map((char, i) =>
              i === 0 || i === target.length - 1 || gameState.step >= (i + 1) * 3
                ? char
                : '_'
            ).join(' '),
            isCompleted: gameState.completedTargets?.includes(target) || false,
            isBurned: gameState.burnedTargets.includes(target)
          }))}
          currentStep={gameState.step}
          revealFrequency={3}
          showProgress={true}
        />
      )}

      {/* Error Display Component */}
      {error && (
        <GameErrorDisplay
          error={error}
          severity="error"
          dismissible={true}
          autoHide={false}
          showAnimations={true}
          onDismiss={clearError}
        />
      )}

      {/* Game Over Screen Component */}
      {isGameOver && (
        <GameOverScreen
          gameStatus={gameState.gameStatus === 'won' ? 'won' : 'lost'}
          currentWord={gameState.currentWord || ''}
          step={gameState.step}
          onShowAnalysis={openPostGameAnalysis}
          onStartNewGame={() => startNewGame()}
          showAnimations={true}
        />
      )}

          </GameFocus>
        </GameLayout>

      {/* Game Controls - Outside GameLayout for proper hierarchy */}
      <GameControls
        selectedDifficulty={selectedDifficulty}
        showDifficultySelector={showDifficultySelector}
        isLoading={uiState.isLoading}
        isGameOver={isGameOver}
        gameInProgress={!!gameState?.currentWord}
        onDifficultyChange={setSelectedDifficulty}
        onStartNewGame={startNewGame}
        onCloseDifficultySelector={() => setShowDifficultySelector(false)}
        disabled={false}
        showAnimations={true}
      />

      {/* Floating Side Panel - Outside GameLayout */}
      <CollapsibleSidePanel
        isOpen={showSidePanel}
        onToggle={() => setShowSidePanel(!showSidePanel)}
        defaultTab={sidePanelTab}
        tabs={[
          {
            id: 'stats',
            label: 'Stats',
            icon: '📊',
            content: (
              <GameStatsPanel
                gameState={{
                  step: gameState.step,
                  maxSteps: gameState.maxSteps,
                  targets: gameState.targets,
                  completedTargets: gameState.completedTargets || [],
                  burnedTargets: gameState.burnedTargets,
                  definitions: gameState.definitions,
                  difficulty: gameState.difficulty,
                  wordsLeft: (() => {
                    const maxWords = calculateMaxWords(
                      gameState.step,
                      gameState.difficulty,
                      gameState.lastDefinitionLength
                    );
                    return Math.max(1, maxWords); // Always minimum 1 word
                  })(),
                  maxWords: (() => {
                    const maxWords = calculateMaxWords(
                      gameState.step,
                      gameState.difficulty,
                      gameState.lastDefinitionLength
                    );
                    return Math.max(1, maxWords); // Always minimum 1 word
                  })()
                }}
                currentDefinitionLength={uiState.wordCount}
                showDetailed={true}
              />
            )
          },
          {
            id: 'history',
            label: 'History',
            icon: '📜',
            content: (
              <DefinitionHistoryPanel
                definitions={gameState.definitions.map(def => ({
                  id: def.id || `def-${def.word}-${def.timestamp}`,
                  step: gameState.definitions.indexOf(def) + 1,
                  word: def.word,
                  definition: def.definition,
                  wordCount: def.wordCount,
                  isValid: def.isValid,
                  timestamp: new Date(def.timestamp),
                  validationIssues: def.isValid ? undefined : ['Definition was rejected']
                }))}
                isVisible={true}
                showValidation={true}
              />
            )
          },
          {
            id: 'rules',
            label: 'Rules',
            icon: '📖',
            content: (
              <div className="p-4">
                <h3 className="text-lg font-bold mb-4">Game Rules</h3>
                <p className="text-sm text-muted">
                  Define words to reach the targets. Each definition must be shorter than the previous one.
                  The AI will try to predict and burn your targets!
                </p>
              </div>
            )
          }
        ]}
      />

      {/* Game Rules Modal */}
      <GameRules
        isOpen={uiState.showRules}
        onClose={() => updateUIState({ showRules: false })}
      />

      {/* Developer Panel */}
      {process.env.NODE_ENV === 'development' && (
        <DevPanel
          gameState={gameState}
          lastAIResponse={lastAIResponse}
          isVisible={showDevPanel}
          onToggle={() => setShowDevPanel(!showDevPanel)}
        />
      )}

      {/* Post-Game Analysis Modal */}
      {showPostGameAnalysis && isGameOver && (
        <PostGameAnalysis
          gameState={gameState}
          onNewGame={() => {
            setShowPostGameAnalysis(false);
            startNewGame();
          }}
          onClose={() => setShowPostGameAnalysis(false)}
        />
      )}

      {/* Performance Dashboard (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <PerformanceDashboard
          isVisible={showPerformanceDashboard}
          onToggle={() => setShowPerformanceDashboard(!showPerformanceDashboard)}
        />
      )}

      {/* Floating Chat Dialog - AI Trash Talk */}
      <FloatingChatDialog
        position="bottom-right"
        maxMessages={5}
      />

      </ARIAEnhancer>
    </>
  );
};

export default GameBoard;

/**
 * GameBoard Component - Main Game Controller
 *
 * Orchestrates the DEFEATER.AI game experience using extracted components:
 * - GameMainContent: Core game interaction elements
 * - GameModalsAndPanels: All overlay UI elements
 * - useGameBoardState: Unified state management
 *
 * This streamlined controller focuses on component orchestration
 * while delegating specific concerns to focused sub-components.
 */

import React from 'react';
import { GameState } from '@/types/game';

// Unified State Management
import useGameBoardState from '@/hooks/useGameBoardState';

// Core Components
import GameLayout from '@/components/layout/GameLayout';
import GameHeader from '@/components/game/GameHeader';
import GameControls from '@/components/game/GameControls';
import GameMainContent from '@/components/game/GameMainContent';
import GameModalsAndPanels from '@/components/game/GameModalsAndPanels';

import LoadingSpinner from './ui/LoadingSpinner';

// New spatial design components
import GameFocus from '@/components/game/GameFocus';
import { Hero, Primary, Secondary } from '@/components/ui/Typography';
import { SkipLinks } from '@/components/accessibility/SkipLinks';
import { ARIAEnhancer } from '@/components/accessibility/ARIAEnhancer';
import styles from './GameBoard.module.css';

interface GameBoardProps {
  initialGameState?: GameState;
}

const GameBoard: React.FC<GameBoardProps> = ({ initialGameState }) => {

  // Unified State Management
  const {
    gameState, lastAIResponse, error, isGameOver, uiState, wordCount,
    selectedDifficulty, showDifficultySelector, showPostGameAnalysis,
    showSidePanel, sidePanelTab, isDevelopment, showDevPanel, showPerformanceDashboard,
    startNewGame, submitDefinition, handleImmediateInputChange,
    setShowDifficultySelector, setSelectedDifficulty, updateUIState, toggleRules, clearError,
    toggleSidePanel, openPostGameAnalysis, setShowPostGameAnalysis,
    setShowDevPanel, setShowPerformanceDashboard
  } = useGameBoardState({ initialGameState });





  if (!gameState) {
    return (
      <div className="game-board">
        <div className={`${styles.loadingCard} text-center`}>
          <LoadingSpinner />
          <p className={`text-muted ${styles.loadingText}`}>Initializing DEFEATER.AI...</p>
        </div>
      </div>
    );
  }



  return (
    <>
      <SkipLinks />

      {/* Main Game Layout with ARIA Enhancement */}
      <ARIAEnhancer
        gameState={gameState}
        currentInput={uiState.inputValue}
        isLoading={uiState.isLoading}
        error={error || undefined}
      >
        {/* Game Header */}
        <GameHeader
          isLoading={uiState.isLoading}
          showSidePanel={showSidePanel}
          onShowRules={toggleRules}
          onShowDifficultySelector={() => setShowDifficultySelector(true)}
          onToggleSidePanel={toggleSidePanel}
          onStartNewGame={startNewGame}
          isDevelopment={isDevelopment}
        />

        <GameLayout>
          <GameFocus priority="hero">
            {/* Main Game Content - Extracted Component */}
            <GameMainContent
              gameState={gameState}
              isGameOver={isGameOver}
              showDifficultySelector={showDifficultySelector}
              inputValue={uiState.inputValue}
              wordCount={wordCount}
              isLoading={uiState.isLoading}
              animationState={uiState.animationState}
              error={error}
              onInputChange={handleImmediateInputChange}
              onSubmitDefinition={submitDefinition}
              onClearError={clearError}
              onShowAnalysis={openPostGameAnalysis}
              onStartNewGame={startNewGame}
            />
          </GameFocus>
        </GameLayout>

      {/* Game Controls */}
      <GameControls
        selectedDifficulty={selectedDifficulty}
        showDifficultySelector={showDifficultySelector}
        isLoading={uiState.isLoading}
        isGameOver={isGameOver}
        gameInProgress={!!gameState?.currentWord}
        onDifficultyChange={setSelectedDifficulty}
        onStartNewGame={startNewGame}
        onCloseDifficultySelector={() => setShowDifficultySelector(false)}
      />

      {/* Modals and Panels */}
      <GameModalsAndPanels
        gameState={gameState}
        isGameOver={isGameOver}
        lastAIResponse={lastAIResponse}
        showSidePanel={showSidePanel}
        sidePanelTab={sidePanelTab}
        showRules={uiState.showRules}
        showPostGameAnalysis={showPostGameAnalysis}
        wordCount={wordCount}
        showDevPanel={showDevPanel}
        showPerformanceDashboard={showPerformanceDashboard}
        isDevelopment={isDevelopment}
        onToggleSidePanel={toggleSidePanel}
        onUpdateUIState={updateUIState}
        onSetShowDevPanel={setShowDevPanel}
        onSetShowPerformanceDashboard={setShowPerformanceDashboard}
        onSetShowPostGameAnalysis={setShowPostGameAnalysis}
        onStartNewGame={startNewGame}
      />

      </ARIAEnhancer>
    </>
  );
};

export default GameBoard;

/**
 * GameBoard Component - Spatial Design System (v2.0)
 *
 * ✅ WEEK 6 MIGRATION COMPLETE:
 * This component has been fully migrated to the spatial design system.
 * - Modern pattern: Gap-based, natural content flow
 * - Performance: CSS modules, optimized state management
 * - Accessibility: WCAG 2.1 AA compliant
 * - Design: Complete spatial design integration
 *
 * Status: ✅ COMPLETE - Fully migrated to spatial system
 * Version: 2.0 - Production ready
 *
 * @see components/GameBoard.module.css - Optimized CSS module
 * @see hooks/useGameState.ts - State management
 * @see docs/WEEK_6_GAMEBOARD_COMPLETION.md - Migration documentation
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { GameState, UIState, GameResponse, AIResponse, DifficultyLevel } from '@/types/game';
import { GameErrorHandler } from '@/utils/errorHandling';

// Custom Hooks for State Management
import useGameState from '@/hooks/useGameState';
import useUIState from '@/hooks/useUIState';
import useGameControls from '@/hooks/useGameControls';
import useSpatialLayout from '@/hooks/useSpatialLayout';

import useGameLogic from '@/hooks/useGameLogic';
import usePerformanceMonitoring from '@/hooks/usePerformanceMonitoring';

// Game Components
import GameLayout from '@/components/layout/GameLayout';
import GameHeader from '@/components/game/GameHeader';
import GameOverScreen from '@/components/game/GameOverScreen';
import GameErrorDisplay from '@/components/game/GameErrorDisplay';
import GameDisplay from '@/components/game/GameDisplay';
import GameInput from '@/components/game/GameInput';
import GameControls from '@/components/game/GameControls';
import GameMainContent from '@/components/game/GameMainContent';
import GameModalsAndPanels from '@/components/game/GameModalsAndPanels';
import { formatGameProgress, getRemainingTargets } from '@/utils/gameLogic';
import { useGameAnimations } from '@/hooks/useGameAnimations';

import { triggerTrashTalk, getSpecialTrashTalk } from '@/utils/aiTrashTalk';

// Legacy components (still needed for some features)
import GameRules from './GameRules';
import DevPanel from './DevPanel';
import LoadingSpinner from './ui/LoadingSpinner';
import DifficultySelector from './DifficultySelector';
import PostGameAnalysis from './PostGameAnalysis';

// New spatial design components
import GameFocus from '@/components/game/GameFocus';
import CurrentChallenge from '@/components/game/CurrentChallenge';
import DefinitionInput from '@/components/game/DefinitionInput';
import TargetRevelationStrip from '@/components/game/TargetRevelationStrip';
import CollapsibleSidePanel from '@/components/layout/CollapsibleSidePanel';
import GameStatsPanel from '@/components/panels/GameStatsPanel';
import DefinitionHistoryPanel from '@/components/panels/DefinitionHistoryPanel';
import { Hero, Primary, Secondary } from '@/components/ui/Typography';
import { PerformanceDashboard } from '@/components/dev/PerformanceDashboard';
import FloatingChatDialog from '@/components/chat/FloatingChatDialog';
import { SkipLinks } from '@/components/accessibility/SkipLinks';
import { ARIAEnhancer, useARIALabels } from '@/components/accessibility/ARIAEnhancer';
import styles from './GameBoard.module.css';

interface GameBoardProps {
  initialGameState?: GameState;
}

const GameBoard: React.FC<GameBoardProps> = ({ initialGameState }) => {

  // Custom Hooks for State Management
  const {
    gameState,
    lastAIResponse,
    error,
    setGameState,
    setLastAIResponse,
    setError,
    updateGameState,
    batchUpdateGameState,
    resetGameState,
    clearError,
    getStateMetrics
  } = useGameState({
    initialGameState: initialGameState || null
  });

  const {
    uiState,
    setUIState,
    updateUIState,
    setLoading,
    setInputValue,
    setAnimationState,
    toggleRules,
    toggleHistory,
    batchUpdateUI,
    resetUIState,
    isInputValid,
    wordCount,
    getUIMetrics
  } = useUIState({
    debounceDelay: 300
  });

  const {
    selectedDifficulty,
    showDifficultySelector,
    showValidationFeedback,
    showPostGameAnalysis,
    setSelectedDifficulty,
    setShowDifficultySelector,
    setShowValidationFeedback,
    setShowPostGameAnalysis,
    changeDifficulty,
    toggleValidationFeedback,
    openPostGameAnalysis,
    closePostGameAnalysis,
    batchUpdateControls,
    resetControls,
    startNewGame: startNewGameControls,
    endGame: endGameControls,
    getControlsMetrics
  } = useGameControls({
    initialDifficulty: 'medium',
    persistPreferences: true
  });

  const {
    showSidePanel,
    sidePanelTab,
    isResponsiveMode,
    setShowSidePanel,
    setSidePanelTab,
    toggleSidePanel,
    changeSidePanelTab,
    openSidePanelWithTab,
    closeSidePanel,
    handleResponsiveChange,
    batchUpdateLayout,
    resetLayout,
    getLayoutMetrics
  } = useSpatialLayout({
    enableAnimations: true,
    responsiveBreakpoint: 768
  });



  // Performance Monitoring & Development Tools - Extracted from this component
  const {
    isLowEnd,
    optimizedGameState,
    trackRender,
    measureOperation,
    showDevPanel,
    showPerformanceDashboard,
    isDevelopment,
    setShowDevPanel,
    setShowPerformanceDashboard,
    toggleDevPanel,
    togglePerformanceDashboard,
    trackPerformance,
    getPerformanceMetrics,
    clearPerformanceMetrics,
    logDebugInfo,
    measureRenderTime,
    resetDevTools,
    getStateMetrics: getPerformanceStateMetrics,
    getUIMetrics: getPerformanceUIMetrics,
    getControlsMetrics: getPerformanceControlsMetrics,
    getLayoutMetrics: getPerformanceLayoutMetrics
  } = usePerformanceMonitoring({
    gameState,
    componentName: 'GameBoard',
    enablePerformanceMonitoring: process.env.NODE_ENV === 'development',
    enableDevPanel: process.env.NODE_ENV === 'development',
    enablePerformanceDashboard: process.env.NODE_ENV === 'development'
  });

  // Animation system integration (optimized for low-end devices)
  const gameAnimations = useGameAnimations(gameState, {
    enableWordChangeAnimation: !isLowEnd,
    enableTargetBurnAnimation: !isLowEnd,
    enableDefinitionAnimation: true,
    enableGameEndAnimation: true,
    animationDelay: isLowEnd ? 50 : 100,
  });

  // Game Logic Hook - Extracted from this component
  const {
    startNewGame,
    submitDefinition,
    sanitizeInput,
    handleInputChange,
    handleImmediateInputChange,
    getParanoiaLevel,
    getParanoiaMessage,
    isSubmitting
  } = useGameLogic({
    gameState,
    optimizedGameState,
    selectedDifficulty,
    setGameState,
    setLastAIResponse,
    setError,
    clearError,
    batchUpdateUI,
    setAnimationState,
    gameAnimations,
    setShowDifficultySelector,
    setInputValue,
    isLowEnd
  });

  // Chat dialog is always available (no state needed)

  // Accessibility enhancements
  const ariaLabels = useARIALabels(gameState || undefined);



  // Initialize game on component mount
  useEffect(() => {
    if (!gameState) {
      startNewGame();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);





  if (!gameState) {
    return (
      <div className="game-board">
        <div className={`${styles.loadingCard} text-center`}>
          <LoadingSpinner />
          <p className={`text-muted ${styles.loadingText}`}>Initializing DEFEATER.AI...</p>
        </div>
      </div>
    );
  }

  const remainingTargets = getRemainingTargets(gameState);
  const isGameOver = gameState.gameStatus === 'won' || gameState.gameStatus === 'lost';

  return (
    <>
      {/* Skip Links for Keyboard Navigation */}
      <SkipLinks />

      {/* Main Game Layout with ARIA Enhancement */}
      <ARIAEnhancer
        gameState={gameState}
        currentInput={uiState.inputValue}
        isLoading={uiState.isLoading}
        error={error}
      >
        {/* Game Header - Outside GameLayout for proper hierarchy */}
        <GameHeader
          isLoading={uiState.isLoading}
          showSidePanel={showSidePanel}
          onShowRules={toggleRules}
          onShowDifficultySelector={() => setShowDifficultySelector(true)}
          onToggleSidePanel={toggleSidePanel}
          onStartNewGame={() => startNewGame()}
          onTestChat={() => {
            console.log('🧪 Manual chat test triggered');
            if (typeof window !== 'undefined' && (window as any).addAITrashTalk) {
              console.log('🧪 Sending manual test message');
              (window as any).addAITrashTalk('Manual test message from debug button', 'move');
            } else {
              console.warn('🧪 Global function not available for manual test');
            }
          }}
          isDevelopment={process.env.NODE_ENV === 'development'}
        />

        <GameLayout>
          <GameFocus priority="hero">
            {/* Main Game Content - Extracted Component */}
            <GameMainContent
              gameState={gameState}
              isGameOver={isGameOver}
              showDifficultySelector={showDifficultySelector}
              inputValue={uiState.inputValue}
              wordCount={wordCount}
              isLoading={uiState.isLoading}
              animationState={uiState.animationState}
              error={error}
              onInputChange={handleImmediateInputChange}
              onSubmitDefinition={submitDefinition}
              onClearError={clearError}
              onShowAnalysis={openPostGameAnalysis}
              onStartNewGame={startNewGame}
            />
          </GameFocus>
        </GameLayout>

      {/* Game Controls - Outside GameLayout for proper hierarchy */}
      <GameControls
        selectedDifficulty={selectedDifficulty}
        showDifficultySelector={showDifficultySelector}
        isLoading={uiState.isLoading}
        isGameOver={isGameOver}
        gameInProgress={!!gameState?.currentWord}
        onDifficultyChange={setSelectedDifficulty}
        onStartNewGame={startNewGame}
        onCloseDifficultySelector={() => setShowDifficultySelector(false)}
        disabled={false}
        showAnimations={true}
      />

      {/* Modals and Panels - Extracted Component */}
      <GameModalsAndPanels
        gameState={gameState}
        isGameOver={isGameOver}
        lastAIResponse={lastAIResponse}
        showSidePanel={showSidePanel}
        sidePanelTab={sidePanelTab}
        showRules={uiState.showRules}
        showPostGameAnalysis={showPostGameAnalysis}
        wordCount={wordCount}
        showDevPanel={showDevPanel}
        showPerformanceDashboard={showPerformanceDashboard}
        isDevelopment={isDevelopment}
        onToggleSidePanel={() => setShowSidePanel(!showSidePanel)}
        onUpdateUIState={updateUIState}
        onSetShowDevPanel={setShowDevPanel}
        onSetShowPerformanceDashboard={setShowPerformanceDashboard}
        onSetShowPostGameAnalysis={setShowPostGameAnalysis}
        onStartNewGame={startNewGame}
      />

      </ARIAEnhancer>
    </>
  );
};

export default GameBoard;

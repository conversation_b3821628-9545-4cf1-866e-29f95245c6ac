/**
 * Performance Impact Assessment Suite
 * 
 * Measures performance impact of UX restoration including load times,
 * rendering performance, memory usage, and CSS optimization validation
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Import components to test
import HomePage from '@/pages/index';
import OpenDesignTestPage from '@/pages/open-design-test';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/',
  }),
}));

// Mock API calls
global.fetch = jest.fn();

// Mock portal for testing
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (node: React.ReactNode) => node,
}));

// Performance measurement utilities
const measurePerformance = (name: string, fn: () => void) => {
  const start = performance.now();
  fn();
  const end = performance.now();
  return {
    name,
    duration: end - start,
    timestamp: Date.now()
  };
};

const getMemoryUsage = () => {
  if ('memory' in performance) {
    return {
      usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
      totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
      jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
    };
  }
  return null;
};

describe('Performance Impact Assessment', () => {
  
  beforeEach(() => {
    // Reset fetch mock
    (fetch as jest.MockedFunction<typeof fetch>).mockClear();
    
    // Mock successful game start API response
    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        gameState: {
          gameId: 'test-game-123',
          currentWord: 'transformer',
          step: 1,
          maxSteps: 25,
          targets: ['innovation', 'ecosystem', 'friction', 'paradigm'],
          completedTargets: [],
          burnedTargets: [],
          definitions: [],
          difficulty: 'medium',
          usedWords: [],
          aiChallengeWords: [],
          gameStatus: 'waiting',
          consecutiveRejections: 0,
          commonWordsUsage: {},
          rejectionHistory: []
        }
      })
    } as Response);
  });

  describe('Rendering Performance', () => {
    test('Main app should render within acceptable time', async () => {
      const renderMetrics = measurePerformance('HomePage Render', () => {
        render(<HomePage />);
      });
      
      // Should render within 100ms
      expect(renderMetrics.duration).toBeLessThan(100);
      
      // Wait for game initialization
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
    });

    test('Test page should render within acceptable time', () => {
      const renderMetrics = measurePerformance('OpenDesignTestPage Render', () => {
        render(<OpenDesignTestPage />);
      });
      
      // Should render within 100ms
      expect(renderMetrics.duration).toBeLessThan(100);
    });

    test('Component re-renders should be efficient', async () => {
      const { rerender } = render(<HomePage />);
      
      // Wait for initial render
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Measure re-render performance
      const rerenderMetrics = measurePerformance('HomePage Re-render', () => {
        rerender(<HomePage />);
      });
      
      // Re-renders should be faster than initial render
      expect(rerenderMetrics.duration).toBeLessThan(50);
    });
  });

  describe('Memory Usage', () => {
    test('Memory usage should be reasonable', async () => {
      const initialMemory = getMemoryUsage();
      
      render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      const afterRenderMemory = getMemoryUsage();
      
      if (initialMemory && afterRenderMemory) {
        const memoryIncrease = afterRenderMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
        
        // Memory increase should be reasonable (less than 20MB)
        expect(memoryIncrease).toBeLessThan(20 * 1024 * 1024);
        
        // Should not exceed 80% of heap limit
        const heapUsagePercentage = afterRenderMemory.usedJSHeapSize / afterRenderMemory.jsHeapSizeLimit;
        expect(heapUsagePercentage).toBeLessThan(0.8);
      }
    });

    test('Memory should not leak on component unmount', () => {
      const initialMemory = getMemoryUsage();
      
      const { unmount } = render(<OpenDesignTestPage />);
      unmount();
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const afterUnmountMemory = getMemoryUsage();
      
      if (initialMemory && afterUnmountMemory) {
        const memoryDifference = afterUnmountMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
        
        // Memory should return close to initial state (within 5MB tolerance)
        expect(Math.abs(memoryDifference)).toBeLessThan(5 * 1024 * 1024);
      }
    });
  });

  describe('DOM Complexity', () => {
    test('DOM node count should be reasonable', async () => {
      const initialNodeCount = document.querySelectorAll('*').length;
      
      render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      const finalNodeCount = document.querySelectorAll('*').length;
      const addedNodes = finalNodeCount - initialNodeCount;
      
      // Should not add excessive DOM nodes (less than 500)
      expect(addedNodes).toBeLessThan(500);
      
      // Check for deeply nested elements
      const deepElements = document.querySelectorAll('div div div div div div div div div div');
      expect(deepElements.length).toBeLessThan(10); // Limit deep nesting
    });

    test('CSS class count should be optimized', () => {
      render(<OpenDesignTestPage />);
      
      const allElements = document.querySelectorAll('*');
      let totalClasses = 0;
      let elementsWithManyClasses = 0;
      
      allElements.forEach(element => {
        const classList = element.classList;
        totalClasses += classList.length;
        
        if (classList.length > 10) {
          elementsWithManyClasses++;
        }
      });
      
      // Average classes per element should be reasonable
      const averageClassesPerElement = totalClasses / allElements.length;
      expect(averageClassesPerElement).toBeLessThan(5);
      
      // Few elements should have excessive classes
      expect(elementsWithManyClasses).toBeLessThan(5);
    });
  });

  describe('CSS Performance', () => {
    test('CSS custom properties should be efficiently used', () => {
      render(<OpenDesignTestPage />);
      
      const rootElement = document.documentElement;
      const computedStyle = window.getComputedStyle(rootElement);
      
      // Test key CSS variables are defined
      const keyVariables = [
        '--text-primary',
        '--bg-primary',
        '--accent-cyan',
        '--space-4',
        '--radius-lg'
      ];
      
      keyVariables.forEach(variable => {
        const value = computedStyle.getPropertyValue(variable);
        expect(value).toBeTruthy();
        expect(value.trim()).not.toBe('');
      });
    });

    test('Animation performance should be optimized', () => {
      render(<OpenDesignTestPage />);
      
      // Check for will-change usage (performance optimization)
      const elementsWithWillChange = document.querySelectorAll('[style*="will-change"]');
      
      // Should use will-change sparingly (only for actively animating elements)
      expect(elementsWithWillChange.length).toBeLessThan(5);
      
      // Check for transform usage (GPU acceleration)
      const elementsWithTransform = document.querySelectorAll('[style*="transform"]');
      
      // Transform usage indicates GPU acceleration
      expect(elementsWithTransform.length).toBeGreaterThanOrEqual(0);
    });

    test('CSS selector complexity should be reasonable', () => {
      render(<OpenDesignTestPage />);
      
      // Get all stylesheets
      const stylesheets = Array.from(document.styleSheets);
      let complexSelectors = 0;
      
      stylesheets.forEach(stylesheet => {
        try {
          const rules = Array.from(stylesheet.cssRules || []);
          rules.forEach(rule => {
            if (rule instanceof CSSStyleRule) {
              const selector = rule.selectorText;
              
              // Count selector complexity (number of combinators)
              const combinators = (selector.match(/[>+~\s]/g) || []).length;
              if (combinators > 4) {
                complexSelectors++;
              }
            }
          });
        } catch (e) {
          // Cross-origin stylesheets may not be accessible
        }
      });
      
      // Should have minimal complex selectors
      expect(complexSelectors).toBeLessThan(10);
    });
  });

  describe('Bundle Size Impact', () => {
    test('Component imports should be tree-shakeable', () => {
      // This test verifies that components are properly structured for tree-shaking
      const componentModules = [
        'components/ui/Typography',
        'components/layout/GameLayout',
        'components/ui/SpatialModal'
      ];
      
      componentModules.forEach(modulePath => {
        // Verify named exports are used (better for tree-shaking)
        expect(() => {
          // This would fail if module doesn't exist or has issues
          require(`@/${modulePath}`);
        }).not.toThrow();
      });
    });

    test('CSS-in-JS should not cause excessive style tags', () => {
      render(<OpenDesignTestPage />);
      
      // Count style tags (styled-jsx creates these)
      const styleTags = document.querySelectorAll('style');
      
      // Should not have excessive style tags
      expect(styleTags.length).toBeLessThan(50);
      
      // Check for duplicate styles
      const styleContents = Array.from(styleTags).map(tag => tag.textContent);
      const uniqueStyles = new Set(styleContents);
      
      // Should not have many duplicate styles
      const duplicateRatio = (styleContents.length - uniqueStyles.size) / styleContents.length;
      expect(duplicateRatio).toBeLessThan(0.3); // Less than 30% duplicates
    });
  });

  describe('Responsive Performance', () => {
    test('Viewport changes should not cause layout thrashing', () => {
      render(<OpenDesignTestPage />);
      
      const initialNodeCount = document.querySelectorAll('*').length;
      
      // Simulate viewport changes
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });
      
      // Trigger resize event
      window.dispatchEvent(new Event('resize'));
      
      const afterResizeNodeCount = document.querySelectorAll('*').length;
      
      // Node count should remain stable during resize
      expect(Math.abs(afterResizeNodeCount - initialNodeCount)).toBeLessThan(10);
    });

    test('Media queries should be efficiently structured', () => {
      render(<OpenDesignTestPage />);
      
      // Get all stylesheets and count media queries
      const stylesheets = Array.from(document.styleSheets);
      let mediaQueryCount = 0;
      
      stylesheets.forEach(stylesheet => {
        try {
          const rules = Array.from(stylesheet.cssRules || []);
          rules.forEach(rule => {
            if (rule instanceof CSSMediaRule) {
              mediaQueryCount++;
            }
          });
        } catch (e) {
          // Cross-origin stylesheets may not be accessible
        }
      });
      
      // Should have reasonable number of media queries
      expect(mediaQueryCount).toBeLessThan(100);
    });
  });

  describe('Accessibility Performance', () => {
    test('ARIA attributes should not impact performance', () => {
      const renderStart = performance.now();
      
      render(<OpenDesignTestPage />);
      
      const renderEnd = performance.now();
      const renderTime = renderEnd - renderStart;
      
      // Count ARIA attributes
      const ariaElements = document.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby], [role]');
      
      // Should have ARIA attributes without significant performance impact
      expect(ariaElements.length).toBeGreaterThan(0);
      expect(renderTime).toBeLessThan(100); // Still fast with ARIA
    });

    test('Focus management should be performant', async () => {
      render(<OpenDesignTestPage />);
      
      const focusableElements = document.querySelectorAll('button, input, [tabindex]:not([tabindex="-1"])');
      
      // Test focus performance
      const focusStart = performance.now();
      
      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus();
      }
      
      const focusEnd = performance.now();
      const focusTime = focusEnd - focusStart;
      
      // Focus should be immediate
      expect(focusTime).toBeLessThan(10);
      expect(focusableElements.length).toBeGreaterThan(0);
    });
  });
});

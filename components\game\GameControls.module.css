/**
 * GameControls CSS Module - Open Spatial Design (v3.0)
 *
 * 🎯 TRANSFORMED FROM CONTAINER TO OPEN SPATIAL DESIGN
 * 
 * ELIMINATED CONTAINER ISSUES:
 * - Removed modal background (var(--color-card))
 * - Removed modal border (border: 1px solid var(--color-border))
 * - Removed modal border-radius (border-radius: var(--radius-lg))
 * - Removed header border (border-bottom: 1px solid var(--color-border))
 * 
 * OPEN DESIGN PRINCIPLES:
 * - No visual barriers or containers in modal
 * - Enhanced breathing room and spacing
 * - Glow effects instead of borders
 * - Transparent, spatial approach
 * 
 * @version 3.0 - Open Spatial Design Migration
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

/* === CORE CONTROLS LAYOUT === */
.gameControls {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.gameControlsLoading {
  opacity: 0.9;
}

.gameControlsDisabled {
  opacity: 0.6;
  pointer-events: none;
}

/* === DIFFICULTY MODAL - OPEN SPATIAL DESIGN === */
.difficultyModal {
  position: fixed;
  inset: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s ease-out;
}

.difficultyBackdrop {
  position: absolute;
  inset: 0;
  background: var(--color-background-overlay);
  backdrop-filter: blur(4px);
}

.difficultyContent {
  position: relative;
  /* REMOVED: background, border, border-radius for open design */
  padding: var(--space-8);
  max-width: 500px;
  width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  /* Enhanced spatial shadow instead of container border */
  filter: drop-shadow(0 8px 32px rgba(0, 0, 0, 0.3));
  backdrop-filter: blur(20px);
  animation: slideUp 0.3s ease-out;
}

.difficultyHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-4);
  /* REMOVED: border-bottom for open design */
  /* Enhanced spacing for breathing room */
}

.difficultyHeader h3 {
  margin: 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-foreground);
}

.closeButton {
  background: none;
  border: none;
  font-size: var(--text-lg);
  color: var(--color-muted-foreground);
  cursor: pointer;
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
}

.closeButton:hover:not(:disabled) {
  /* Open design hover - glow instead of background */
  filter: drop-shadow(0 0 8px var(--accent-cyan));
  color: var(--color-foreground);
}

.closeButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.difficultySelectorContainer {
  margin: var(--space-6) 0;
}

/* === LOADING OVERLAY === */
.loadingOverlay {
  position: absolute;
  inset: 0;
  background: var(--color-background-overlay);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(2px);
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-muted);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* === QUICK ACTIONS === */
.quickActions {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  align-items: center;
}

/* Use global button styles - only component-specific overrides */
.quickActions :global(.btn-primary) {
  min-width: 120px;
}

.buttonSpinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* === GAME STATUS === */
.gameStatus {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-muted-foreground);
}

.statusIndicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.statusIndicatorActive {
  background: var(--color-success);
  animation: pulse 2s ease-in-out infinite;
}

.statusIndicatorFinished {
  background: var(--color-muted);
}

/* === ANIMATIONS === */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* === RESPONSIVE DESIGN === */

/* Mobile: Enhanced open design */
@media (max-width: calc(var(--bp-md) - 1px)) {
  .difficultyContent {
    padding: var(--space-6);
    width: 95vw;
  }

  .quickActions :global(.btn-primary) {
    min-width: 120px;
    /* Enhanced touch target for mobile */
    min-height: 48px;
  }
}

/* === SMALL MOBILE OPTIMIZATION === */

/* Very small screens (iPhone SE, etc.) */
@media (max-width: calc(var(--bp-xs) - 1px)) {
  .difficultyContent {
    padding: var(--space-4);
    width: 98vw;
    max-height: 85vh;
  }

  .quickActions :global(.btn-primary) {
    min-width: 100px;
    min-height: 44px;
    width: 100%;
    max-width: 180px;
  }

  .closeButton {
    padding: var(--space-2);
    min-width: 44px;
    min-height: 44px;
  }
}

/* === ACCESSIBILITY ENHANCEMENTS === */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .difficultyModal,
  .difficultyContent,
  .loadingSpinner,
  .buttonSpinner,
  .statusIndicatorActive {
    animation: none !important;
    transition: none !important;
  }
}

/* High contrast mode - subtle background for accessibility */
@media (prefers-contrast: high) {
  .difficultyContent {
    background: rgba(15, 23, 42, 0.95);
    border-radius: var(--radius-lg);
  }
}

/* Focus management - open design */
.closeButton:focus {
  outline: none;
  /* Open design focus - enhanced glow instead of outline */
  filter: drop-shadow(0 0 12px var(--accent-cyan));
}

/* === PERFORMANCE OPTIMIZATIONS === */

/* GPU acceleration for smooth interactions */
.difficultyModal {
  will-change: opacity;
  contain: layout style;
}

.difficultyContent {
  will-change: transform, filter;
  contain: layout;
}

/* === DEBUGGING SUPPORT === */

/* Development mode indicators */
.gameControls.development {
  outline: 2px dashed var(--color-warning);
  outline-offset: 4px;
}

.gameControls.development::before {
  content: 'GameControls (Open Design v3.0)';
  position: absolute;
  top: 0;
  left: 0;
  background: var(--color-warning);
  color: var(--color-background);
  padding: var(--space-1) var(--space-2);
  font-size: 0.75rem;
  font-weight: 600;
  z-index: var(--z-notification);
}

/**
 * GameHeader Component - Game Title and Navigation Controls (v2.0)
 *
 * 🎯 FOCUSED GAME HEADER COMPONENT
 * 
 * Features:
 * - Game title and subtitle display
 * - Navigation controls (rules, difficulty, panel, new game)
 * - Development debug controls
 * - Spatial design system integration
 * - Accessibility compliance
 * 
 * Performance Benefits:
 * - Isolated component for targeted re-renders
 * - Memoized props to prevent unnecessary updates
 * - Optimized event handlers
 * - Clean separation of concerns
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_5-6_COMPONENT_DECOMPOSITION.md
 */

import React, { memo } from 'react';
import { HeroText, SecondaryText } from '@/components/ui/Typography';

interface GameHeaderProps {
  // UI State
  isLoading: boolean;
  showSidePanel: boolean;
  
  // Event Handlers
  onShowRules: () => void;
  onShowDifficultySelector: () => void;
  onToggleSidePanel: () => void;
  onStartNewGame: () => void;
  onTestChat?: () => void; // Development only
  
  // Configuration
  isDevelopment?: boolean;
}

const GameHeader: React.FC<GameHeaderProps> = memo(({
  isLoading,
  showSidePanel,
  onShowRules,
  onShowDifficultySelector,
  onToggleSidePanel,
  onStartNewGame,
  onTestChat,
  isDevelopment = process.env.NODE_ENV === 'development'
}) => {
  return (
    <header className="game-header">
      {/* Game Title */}
      <div className="title-section">
        <HeroText
          className="hero-title"
          as="h1"
        >
          DEFEATER.AI
        </HeroText>
        <SecondaryText
          className="game-subtitle"
          as="p"
        >
          Outsmart the AI. Define your way to victory.
        </SecondaryText>
      </div>

      {/* Navigation Controls */}
      <nav className="game-controls" role="navigation" aria-label="Game controls">
        <button
          onClick={onShowRules}
          className="btn-secondary"
          disabled={isLoading}
          aria-label="Show game rules"
        >
          📖 How to Play
        </button>

        <button
          onClick={onShowDifficultySelector}
          className="btn-secondary"
          disabled={isLoading}
          aria-label="Change game difficulty"
        >
          ⚙️ Difficulty
        </button>

        <button
          onClick={onToggleSidePanel}
          className="btn-secondary"
          aria-label={showSidePanel ? "Hide side panel" : "Show side panel"}
          aria-expanded={showSidePanel}
          aria-controls="side-panel"
        >
          {showSidePanel ? '📊 Hide Panel' : '📊 Show Panel'}
        </button>

        <button
          onClick={onStartNewGame}
          className="btn-primary"
          disabled={isLoading}
          aria-label="Start a new game"
        >
          New Game
        </button>

        {/* Development Debug Controls */}
        {isDevelopment && onTestChat && (
          <button
            onClick={onTestChat}
            className="btn-secondary text-xs"
            aria-label="Test chat function"
            title="Development: Test AI chat functionality"
          >
            🧪 Test Chat
          </button>
        )}
      </nav>

      <style jsx>{`
        /* === GAME HEADER === */
        .game-header {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--space-8);
          padding: var(--space-6) var(--space-4);
          text-align: center;
          width: 100%;
          max-width: 1200px;
          margin: 0 auto;
        }

        /* === TITLE SECTION === */
        .title-section {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--space-3);
        }

        .hero-title {
          background: linear-gradient(135deg, var(--accent-cyan) 0%, var(--accent-purple) 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-shadow: 0 0 30px rgba(6, 182, 212, 0.3);
        }

        .game-subtitle {
          opacity: 0.9;
          max-width: 600px;
        }

        /* === GAME CONTROLS === */
        .game-controls {
          display: flex;
          flex-wrap: wrap;
          gap: var(--space-4);
          justify-content: center;
          align-items: center;
        }

        .text-xs {
          font-size: var(--text-xs);
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .game-header {
            gap: var(--space-6);
            padding: var(--space-4) var(--space-3);
          }

          .game-controls {
            gap: var(--space-3);
          }

          .game-controls button {
            font-size: var(--text-sm);
            padding: var(--space-2) var(--space-3);
          }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
          .game-header {
            gap: var(--space-7);
          }
        }

        @media (min-width: 1024px) {
          .game-header {
            gap: var(--space-10);
            padding: var(--space-8) var(--space-6);
          }

          .game-controls {
            gap: var(--space-6);
          }
        }
      `}</style>
    </header>
  );
});

GameHeader.displayName = 'GameHeader';

export default GameHeader;

/**
 * Open Design Test Page
 * 
 * Testing the TRUE vision: Open, breathing, spatial design
 * NO containers, NO cards, NO boxes - just content floating in space
 */

import React, { useState } from 'react';
import Head from 'next/head';
import GameLayout from '@/components/layout/GameLayout';
import GameFocus from '@/components/game/GameFocus';
import CurrentChallenge from '@/components/game/CurrentChallenge';
import DefinitionInput from '@/components/game/DefinitionInput';
import TargetRevelationStrip from '@/components/game/TargetRevelationStrip';

import CollapsibleSidePanel from '@/components/layout/CollapsibleSidePanel';
import GameStatsPanel from '@/components/panels/GameStatsPanel';
import DefinitionHistoryPanel from '@/components/panels/DefinitionHistoryPanel';
import ProgressiveDisclosure from '@/components/layout/ProgressiveDisclosure';
import { createDisclosureRules, buildDisclosureContext } from '@/utils/disclosureRules';
import InputHelper from '@/components/consolidated/InputHelper';
import GameProgress from '@/components/consolidated/GameProgress';
import CommonWordTracker from '@/components/consolidated/CommonWordTracker';
import ContextualFeedbackManager from '@/components/feedback/ContextualFeedbackManager';
import SmartValidationFeedback from '@/components/feedback/SmartValidationFeedback';
import { FeedbackProvider } from '@/contexts/FeedbackContext';
import ResponsiveGrid, { GridItem, ComponentGrid, StatsGrid } from '@/components/layout/ResponsiveGrid';
import { Hero, Primary, Secondary, Body, Small, Status, Accent, Imposing, Mono } from '@/components/ui/Typography';

const OpenDesignTestPage: React.FC = () => {
  const [showSideInfo, setShowSideInfo] = useState(false);
  const [definitionValue, setDefinitionValue] = useState('');
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [dismissedTips, setDismissedTips] = useState<string[]>([]);
  const [validationErrors, setValidationErrors] = useState<Array<{ type: string; message: string; }>>([]);

  // Mock game state for testing
  const mockGameState = {
    gameId: 'test-game-123',
    currentWord: 'transformer',
    step: 8,
    maxSteps: 25,
    targets: ['innovation', 'ecosystem', 'friction', 'paradigm'],
    completedTargets: ['friction'],
    burnedTargets: ['paradigm'],
    definitions: [
      {
        id: '1',
        word: 'transformer',
        definition: 'A device that changes electrical voltage',
        wordCount: 7,
        timestamp: Date.now(),
        isValid: true
      },
      {
        id: '2',
        word: 'innovation',
        definition: 'The process of creating new ideas or methods that improve existing systems',
        wordCount: 13,
        timestamp: Date.now(),
        isValid: true
      }
    ],
    difficulty: 'medium' as const,
    usedWords: ['transformer', 'device', 'electrical', 'voltage', 'innovation', 'process'],
    aiChallengeWords: ['transformer', 'innovation'],
    gameStatus: 'waiting' as const,
    consecutiveRejections: 0,
    commonWordsUsage: {
      'the': 3,
      'and': 2,
      'of': 1,
      'to': 4,
      'a': 2
    },
    rejectionHistory: []
  };

  // Mock user and UI state for progressive disclosure
  const mockUserState = {
    gamesPlayed: 3,
    showHints: true,
    dismissedTips: dismissedTips
  };

  // Mock player profile for feedback system
  const mockPlayerProfile = {
    id: 'test-player-123',
    gamesPlayed: 3,
    averageWordsPerDefinition: 6.2,
    targetCompletionRate: 0.4,
    preferredDifficulty: 'medium' as const,
    skillLevel: 'intermediate' as const,
    lastPlayedAt: new Date(),
    totalPlayTime: 3600000, // 1 hour in milliseconds
    achievements: []
  };

  const mockUIState = {
    isInputFocused: isInputFocused,
    hasValidationErrors: definitionValue.trim().split(/\s+/).length > 15,
    isLoading: false
  };

  // Build disclosure context
  const disclosureContext = buildDisclosureContext(mockGameState, mockUserState, mockUIState);
  const disclosureRules = createDisclosureRules();

  const mockDefinitions = [
    {
      id: '1',
      step: 7,
      word: 'innovation',
      definition: 'The process of creating new ideas or methods that improve existing systems',
      wordCount: 13,
      isValid: true,
      aiResponse: {
        feedback: 'Good definition! Captures the essence of innovation.',
        reasoning: 'The definition correctly identifies innovation as a process and mentions both creation and improvement aspects.',
        nextWord: 'transformer'
      },
      timestamp: new Date(Date.now() - 300000),
      validationIssues: []
    },
    {
      id: '2',
      step: 6,
      word: 'ecosystem',
      definition: 'A biological community',
      wordCount: 4,
      isValid: false,
      aiResponse: {
        feedback: 'Too brief. Need more detail about interactions.',
        reasoning: 'While technically correct, this definition lacks the complexity needed for the game.',
        nextWord: 'innovation'
      },
      timestamp: new Date(Date.now() - 600000),
      validationIssues: ['Definition too short', 'Missing key concepts']
    }
  ];

  // Mock tab content
  const sidePanelTabs = [
    {
      id: 'stats' as const,
      label: 'Stats',
      icon: '📊',
      content: (
        <GameStatsPanel
          gameState={{
            ...mockGameState,
            wordsLeft: 45,
            maxWords: 75
          }}
          currentDefinitionLength={definitionValue.trim().split(/\s+/).filter(w => w.length > 0).length}
          showDetailed={true}
        />
      )
    },
    {
      id: 'history' as const,
      label: 'History',
      icon: '📝',
      content: (
        <DefinitionHistoryPanel
          definitions={mockDefinitions}
          isVisible={true}
          showValidation={true}
        />
      )
    },
    {
      id: 'rules' as const,
      label: 'Rules',
      icon: '📖',
      content: (
        <div>
          <Primary as="h3">Game Rules</Primary>
          <Body>Define words to reach targets within the step limit...</Body>
        </div>
      )
    },
    {
      id: 'help' as const,
      label: 'Help',
      icon: '❓',
      content: (
        <div>
          <Primary as="h3">Help & Tips</Primary>
          <Body>Strategy tips and gameplay guidance...</Body>
        </div>
      )
    }
  ];



  return (
    <FeedbackProvider>
      <Head>
        <title>Spatial Layout Test - DEFEATER.AI</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <GameLayout>
        <GameFocus priority="hero" showDebugGrid={true}>

          {/* Hero Challenge - Center Stage */}
          <CurrentChallenge
            word="TRANSFORMER"
            step={5}
            maxSteps={25}
            animationState="entering"
            showProgress={true}
          />

          {/* Large Definition Input */}
          <DefinitionInput
            value={definitionValue}
            onChange={setDefinitionValue}
            onSubmit={(def) => console.log('Submitted:', def)}
            maxWords={15}
            showHints={true}
            animationState="idle"
          />

          {/* Smart Validation Feedback */}
          <SmartValidationFeedback
            input={definitionValue}
            gameState={mockGameState}
            playerProfile={mockPlayerProfile}
            inputFocused={isInputFocused}
            maxWords={15}
            onValidationChange={(isValid, errors) => {
              setValidationErrors(errors.map(msg => ({ type: 'validation', message: msg })));
            }}
          />

          {/* Target Revelation Strip */}
          <TargetRevelationStrip
            targets={[
              { word: "innovation", revealed: "I n n o v _ _ _ _ n", isCompleted: false, isBurned: false },
              { word: "ecosystem", revealed: "E c o s _ _ _ _ m", isCompleted: false, isBurned: false },
              { word: "friction", revealed: "F r i c t i o n", isCompleted: true, isBurned: false },
              { word: "paradigm", revealed: "P _ _ _ _ _ _ m", isCompleted: false, isBurned: true }
            ]}
            currentStep={5}
            revealFrequency={3}
            showProgress={true}
          />

          {/* Progress - Simple text floating */}
          <section className="spatial-section">
            <Secondary style={{ opacity: 0.8 }}>
              Step 5 of 25 • 2 targets remaining
            </Secondary>
          </section>

          {/* Consolidated Components Testing */}
          <section className="spatial-section">
            <Primary as="h2">CONSOLIDATED COMPONENTS</Primary>

            {/* InputHelper - Replaces WordsLeftCounter + ValidationFeedback */}
            <InputHelper
              gameState={mockGameState}
              currentDefinition={definitionValue}
              validationResult={{
                isValid: definitionValue.trim().split(/\s+/).length <= 15,
                errors: definitionValue.trim().split(/\s+/).length > 15
                  ? [{ type: 'word_count', message: 'Definition too long' }]
                  : []
              }}
              showOnlyWhenNeeded={false}
            />

            {/* GameProgress - Replaces GameStats + GameStatus */}
            <GameProgress
              gameState={mockGameState}
              animationState="idle"
              showDetailed={true}
              compact={false}
            />

            {/* Enhanced CommonWordTracker */}
            <CommonWordTracker
              gameState={mockGameState}
              viewMode="expanded"
              showOnlyUsed={false}
            />
          </section>

          {/* Responsive Grid System Test */}
          <section className="spatial-section">
            <Primary as="h2">RESPONSIVE GRID SYSTEM</Primary>

            <ComponentGrid>
              <div style={{ background: 'var(--glass-medium)', padding: 'var(--space-4)', borderRadius: 'var(--radius-lg)' }}>
                <Secondary>Grid Item 1</Secondary>
                <Small>Auto-responsive layout</Small>
              </div>
              <div style={{ background: 'var(--glass-medium)', padding: 'var(--space-4)', borderRadius: 'var(--radius-lg)' }}>
                <Secondary>Grid Item 2</Secondary>
                <Small>Adapts to screen size</Small>
              </div>
              <div style={{ background: 'var(--glass-medium)', padding: 'var(--space-4)', borderRadius: 'var(--radius-lg)' }}>
                <Secondary>Grid Item 3</Secondary>
                <Small>Spatial design principles</Small>
              </div>
              <div style={{ background: 'var(--glass-medium)', padding: 'var(--space-4)', borderRadius: 'var(--radius-lg)' }}>
                <Secondary>Grid Item 4</Secondary>
                <Small>No containers, open layout</Small>
              </div>
            </ComponentGrid>
          </section>

          {/* Enhanced Submit Buttons - Global CSS System */}
          <section className="spatial-section">
            <ResponsiveGrid columns={{ mobile: 1, tablet: 2, desktop: 4 }} gap="normal">
              <button className="btn-primary">
                Submit Definition
              </button>
              <button className="btn-secondary" disabled>
                Processing...
              </button>
              <button className="btn-primary" style={{ background: 'var(--color-success)' }}>
                Success!
              </button>
              <button className="btn-secondary" style={{ background: 'var(--color-error)' }}>
                Try Again
              </button>
            </ResponsiveGrid>
          </section>

        </GameFocus>
      </GameLayout>

      {/* Persistent Collapsible Side Panel */}
      <CollapsibleSidePanel
        isOpen={showSideInfo}
        onToggle={() => setShowSideInfo(!showSideInfo)}
        defaultTab="stats"
        tabs={sidePanelTabs}
        rememberPreferences={true}
        persistentMode={true}
        overlayOnMobile={true}
      />

      {/* Progressive Disclosure System */}
      <ProgressiveDisclosure
        context={disclosureContext}
        rules={disclosureRules}
        maxVisible={2}
        position="floating"
        onDismiss={(ruleId) => setDismissedTips(prev => [...prev, ruleId])}
      />

      {/* Contextual Feedback Manager */}
      <ContextualFeedbackManager
        gameState={mockGameState}
        playerProfile={mockPlayerProfile}
        currentInput={definitionValue}
        inputFocused={isInputFocused}
        validationErrors={validationErrors}
        maxVisible={2}
        position="floating"
      />
    </FeedbackProvider>
  );
};

export default OpenDesignTestPage;

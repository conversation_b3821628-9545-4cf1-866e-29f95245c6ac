#!/usr/bin/env node

/**
 * DEFEATER.AI Performance Optimizer
 * 
 * Analyzes and optimizes performance across the application
 * - Bundle size analysis
 * - Component performance validation
 * - Memory usage optimization
 * - Runtime performance testing
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 DEFEATER.AI Performance Optimizer');
console.log('=====================================\n');

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  bundleSize: {
    main: 200 * 1024, // 200KB
    shared: 100 * 1024, // 100KB
    css: 5 * 1024, // 5KB
  },
  buildTime: 60000, // 60 seconds
  testTime: 10000, // 10 seconds
};

// Performance metrics storage
const metrics = {
  bundleAnalysis: {},
  buildPerformance: {},
  testPerformance: {},
  optimizations: [],
  score: 0
};

/**
 * Analyze bundle sizes from build output
 */
function analyzeBundleSize() {
  console.log('📦 Analyzing Bundle Sizes...');
  
  try {
    const buildStart = Date.now();
    const buildOutput = execSync('npm run build', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    const buildTime = Date.now() - buildStart;
    
    // Parse build output for bundle sizes
    const lines = buildOutput.split('\n');
    const bundleInfo = {};
    
    lines.forEach(line => {
      // Parse Next.js build output
      if (line.includes('├') || line.includes('└')) {
        const match = line.match(/(\d+(?:\.\d+)?)\s*kB/);
        if (match) {
          const size = parseFloat(match[1]) * 1024; // Convert to bytes
          if (line.includes('/')) {
            const route = line.match(/\/[^\s]*/)?.[0];
            if (route) {
              bundleInfo[route] = size;
            }
          }
        }
      }
      
      // Parse shared chunks
      if (line.includes('shared by all')) {
        const match = line.match(/(\d+(?:\.\d+)?)\s*kB/);
        if (match) {
          bundleInfo.shared = parseFloat(match[1]) * 1024;
        }
      }
    });
    
    metrics.bundleAnalysis = bundleInfo;
    metrics.buildPerformance = { buildTime };
    
    console.log('✅ Bundle Analysis Complete');
    console.log(`   Build Time: ${(buildTime / 1000).toFixed(2)}s`);
    console.log(`   Main Bundle: ${(bundleInfo['/'] / 1024).toFixed(1)}KB`);
    console.log(`   Shared JS: ${(bundleInfo.shared / 1024).toFixed(1)}KB`);
    
    return bundleInfo;
  } catch (error) {
    console.error('❌ Bundle analysis failed:', error.message);
    return {};
  }
}

/**
 * Run performance tests
 */
function runPerformanceTests() {
  console.log('\n🧪 Running Performance Tests...');
  
  try {
    const testStart = Date.now();
    const testOutput = execSync('npm test', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    const testTime = Date.now() - testStart;
    
    // Parse test output
    const lines = testOutput.split('\n');
    let testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      testSuites: 0,
      testTime
    };
    
    lines.forEach(line => {
      if (line.includes('Test Suites:')) {
        const match = line.match(/(\d+)\s+passed.*(\d+)\s+total/);
        if (match) {
          testResults.testSuites = parseInt(match[2]);
        }
      }
      if (line.includes('Tests:')) {
        const passedMatch = line.match(/(\d+)\s+passed/);
        const failedMatch = line.match(/(\d+)\s+failed/);
        const totalMatch = line.match(/(\d+)\s+total/);
        
        if (passedMatch) testResults.passedTests = parseInt(passedMatch[1]);
        if (failedMatch) testResults.failedTests = parseInt(failedMatch[1]);
        if (totalMatch) testResults.totalTests = parseInt(totalMatch[1]);
      }
    });
    
    metrics.testPerformance = testResults;
    
    console.log('✅ Performance Tests Complete');
    console.log(`   Test Time: ${(testTime / 1000).toFixed(2)}s`);
    console.log(`   Tests Passed: ${testResults.passedTests}/${testResults.totalTests}`);
    console.log(`   Success Rate: ${((testResults.passedTests / testResults.totalTests) * 100).toFixed(1)}%`);
    
    return testResults;
  } catch (error) {
    console.error('❌ Performance tests failed:', error.message);
    return { testTime: 0, passedTests: 0, totalTests: 0 };
  }
}

/**
 * Analyze component performance
 */
function analyzeComponentPerformance() {
  console.log('\n🔍 Analyzing Component Performance...');
  
  const componentDir = path.join(process.cwd(), 'components');
  const components = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
        const content = fs.readFileSync(fullPath, 'utf8');
        const lines = content.split('\n').length;
        const size = stat.size;
        
        // Analyze component complexity
        const complexity = {
          lines,
          size,
          hooks: (content.match(/use[A-Z]\w+/g) || []).length,
          effects: (content.match(/useEffect/g) || []).length,
          states: (content.match(/useState/g) || []).length,
          callbacks: (content.match(/useCallback/g) || []).length,
          memos: (content.match(/useMemo/g) || []).length,
        };
        
        components.push({
          path: fullPath.replace(process.cwd(), ''),
          name: item,
          ...complexity
        });
      }
    });
  }
  
  scanDirectory(componentDir);
  
  // Sort by complexity (lines + hooks)
  components.sort((a, b) => (b.lines + b.hooks) - (a.lines + a.hooks));
  
  console.log('✅ Component Analysis Complete');
  console.log(`   Total Components: ${components.length}`);
  console.log(`   Most Complex: ${components[0]?.name} (${components[0]?.lines} lines, ${components[0]?.hooks} hooks)`);
  
  return components;
}

/**
 * Generate optimization recommendations
 */
function generateOptimizations(bundleInfo, testResults, components) {
  console.log('\n💡 Generating Optimization Recommendations...');
  
  const optimizations = [];
  
  // Bundle size optimizations
  if (bundleInfo['/'] > PERFORMANCE_THRESHOLDS.bundleSize.main) {
    optimizations.push({
      type: 'bundle',
      priority: 'high',
      issue: 'Main bundle size exceeds threshold',
      current: `${(bundleInfo['/'] / 1024).toFixed(1)}KB`,
      target: `${(PERFORMANCE_THRESHOLDS.bundleSize.main / 1024).toFixed(1)}KB`,
      recommendation: 'Consider code splitting and lazy loading'
    });
  }
  
  if (bundleInfo.shared > PERFORMANCE_THRESHOLDS.bundleSize.shared) {
    optimizations.push({
      type: 'bundle',
      priority: 'medium',
      issue: 'Shared bundle size could be optimized',
      current: `${(bundleInfo.shared / 1024).toFixed(1)}KB`,
      target: `${(PERFORMANCE_THRESHOLDS.bundleSize.shared / 1024).toFixed(1)}KB`,
      recommendation: 'Review shared dependencies and tree shaking'
    });
  }
  
  // Test performance optimizations
  if (testResults.testTime > PERFORMANCE_THRESHOLDS.testTime) {
    optimizations.push({
      type: 'testing',
      priority: 'medium',
      issue: 'Test execution time is slow',
      current: `${(testResults.testTime / 1000).toFixed(2)}s`,
      target: `${(PERFORMANCE_THRESHOLDS.testTime / 1000).toFixed(2)}s`,
      recommendation: 'Optimize test setup and mocking'
    });
  }
  
  // Component optimizations
  const complexComponents = components.filter(c => c.lines > 300 || c.hooks > 10);
  if (complexComponents.length > 0) {
    optimizations.push({
      type: 'component',
      priority: 'low',
      issue: `${complexComponents.length} components are highly complex`,
      current: `${complexComponents[0]?.lines} lines, ${complexComponents[0]?.hooks} hooks`,
      target: '<300 lines, <10 hooks',
      recommendation: 'Consider component decomposition'
    });
  }
  
  metrics.optimizations = optimizations;
  
  console.log('✅ Optimization Analysis Complete');
  console.log(`   Recommendations: ${optimizations.length}`);
  
  return optimizations;
}

/**
 * Calculate performance score
 */
function calculatePerformanceScore(bundleInfo, testResults, optimizations) {
  let score = 100;
  
  // Bundle size scoring (40 points)
  const mainBundleRatio = bundleInfo['/'] / PERFORMANCE_THRESHOLDS.bundleSize.main;
  if (mainBundleRatio > 1) {
    score -= Math.min(20, (mainBundleRatio - 1) * 20);
  }
  
  const sharedBundleRatio = bundleInfo.shared / PERFORMANCE_THRESHOLDS.bundleSize.shared;
  if (sharedBundleRatio > 1) {
    score -= Math.min(20, (sharedBundleRatio - 1) * 20);
  }
  
  // Test performance scoring (30 points)
  const testTimeRatio = testResults.testTime / PERFORMANCE_THRESHOLDS.testTime;
  if (testTimeRatio > 1) {
    score -= Math.min(15, (testTimeRatio - 1) * 15);
  }
  
  const testSuccessRate = testResults.passedTests / testResults.totalTests;
  score -= (1 - testSuccessRate) * 15;
  
  // Optimization penalties (30 points)
  const highPriorityIssues = optimizations.filter(o => o.priority === 'high').length;
  const mediumPriorityIssues = optimizations.filter(o => o.priority === 'medium').length;
  
  score -= highPriorityIssues * 10;
  score -= mediumPriorityIssues * 5;
  
  metrics.score = Math.max(0, Math.round(score));
  return metrics.score;
}

/**
 * Generate performance report
 */
function generateReport() {
  console.log('\n📊 Performance Report');
  console.log('=====================\n');
  
  console.log(`🎯 Overall Score: ${metrics.score}/100`);
  
  if (metrics.score >= 90) {
    console.log('🟢 Excellent Performance');
  } else if (metrics.score >= 70) {
    console.log('🟡 Good Performance');
  } else if (metrics.score >= 50) {
    console.log('🟠 Fair Performance');
  } else {
    console.log('🔴 Poor Performance');
  }
  
  console.log('\n📦 Bundle Analysis:');
  Object.entries(metrics.bundleAnalysis).forEach(([key, value]) => {
    console.log(`   ${key}: ${(value / 1024).toFixed(1)}KB`);
  });
  
  console.log('\n🧪 Test Performance:');
  console.log(`   Execution Time: ${(metrics.testPerformance.testTime / 1000).toFixed(2)}s`);
  console.log(`   Success Rate: ${((metrics.testPerformance.passedTests / metrics.testPerformance.totalTests) * 100).toFixed(1)}%`);
  
  if (metrics.optimizations.length > 0) {
    console.log('\n💡 Optimization Recommendations:');
    metrics.optimizations.forEach((opt, index) => {
      console.log(`   ${index + 1}. [${opt.priority.toUpperCase()}] ${opt.issue}`);
      console.log(`      Current: ${opt.current} | Target: ${opt.target}`);
      console.log(`      Action: ${opt.recommendation}\n`);
    });
  } else {
    console.log('\n✅ No optimization recommendations - performance is optimal!');
  }
  
  // Save report to file
  const reportPath = path.join(process.cwd(), 'reports', 'performance-report.json');
  fs.mkdirSync(path.dirname(reportPath), { recursive: true });
  fs.writeFileSync(reportPath, JSON.stringify(metrics, null, 2));
  
  console.log(`📄 Report saved to: ${reportPath}`);
}

/**
 * Main execution
 */
async function main() {
  try {
    // Run performance analysis
    const bundleInfo = analyzeBundleSize();
    const testResults = runPerformanceTests();
    const components = analyzeComponentPerformance();
    
    // Generate optimizations and score
    const optimizations = generateOptimizations(bundleInfo, testResults, components);
    const score = calculatePerformanceScore(bundleInfo, testResults, optimizations);
    
    // Generate final report
    generateReport();
    
    console.log('\n🎉 Performance optimization analysis complete!');
    
    // Exit with appropriate code
    process.exit(score >= 70 ? 0 : 1);
    
  } catch (error) {
    console.error('\n❌ Performance optimization failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main, metrics };

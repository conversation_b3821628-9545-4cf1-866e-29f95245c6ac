/**
 * GameLayout Component
 *
 * Spatial layout system for DEFEATER.AI - Open, breathing design
 * Content flows naturally without containers or grid constraints
 * Based on spatial design principles and aurora canvas approach
 */

import React from 'react';

interface GameLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const GameLayout: React.FC<GameLayoutProps> = ({
  children,
  className = ''
}) => {
  return (
    <div className={`spatial-layout ${className}`}>
      {/* Main Content - Flows naturally in space */}
      <main className="content-flow">
        {children}
      </main>



      <style jsx>{`
        .spatial-layout {
          min-height: 100vh;
          position: relative;
          overflow-x: hidden;
        }

        .content-flow {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          gap: var(--space-8);
          padding: var(--space-8);
          min-height: 100vh;
          max-width: var(--content-max);
          margin: 0 auto;
          position: relative;
          z-index: var(--z-10);
          padding-top: 8vh;
        }



        /* Responsive spatial adjustments */
        @media (min-width: 768px) {
          .content-flow {
            gap: var(--space-12);
            padding: var(--space-12);
          }
        }

        @media (min-width: 1024px) {
          .content-flow {
            gap: var(--space-16);
            padding: var(--space-16);
          }
        }

        /* Mobile specific */
        @media (max-width: 767px) {
          .content-flow {
            gap: var(--space-4);
            padding: var(--space-4);
          }
        }

        /* Reduced Motion */
        @media (prefers-reduced-motion: reduce) {
          * {
            transition: none;
          }
        }
      `}</style>
    </div>
  );
};

export default GameLayout;

/**
 * Comprehensive Accessibility Testing Suite
 * 
 * Tests WCAG 2.1 AA compliance and accessibility features
 * for DEFEATER.AI application.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock React components for testing
const MockGameBoard = () => React.createElement('div', { role: 'main', 'aria-label': 'Game board' }, 'Mock Game Board');
const MockSkipLinks = () => React.createElement('div', null,
  React.createElement('a', { href: '#main-content' }, 'Skip to main content'),
  React.createElement('a', { href: '#game-input' }, 'Skip to game input')
);
const MockCollapsibleSidePanel = () => React.createElement('div', { role: 'complementary' }, 'Mock Side Panel');
const MockFloatingChatWidget = () => React.createElement('div', { role: 'region', 'aria-label': 'Chat' }, 'Mock Chat');

// Mock game state for testing
const mockGameState = {
  gameId: 'test-game-123',
  currentWord: 'synapse',
  targets: ['cycle', 'organism', 'line'],
  burnedTargets: [],
  definitions: [],
  usedWords: ['synapse'],
  aiChallengeWords: [],
  step: 0,
  maxSteps: 25,
  gameStatus: 'waiting' as const,
  difficulty: 'medium' as const,
  consecutiveRejections: 0,
  commonWordsUsage: {},
  rejectionHistory: []
};

describe('Accessibility Compliance Tests', () => {
  beforeEach(() => {
    // Reset DOM before each test
    document.body.innerHTML = '';
    
    // Mock window.matchMedia for accessibility preferences
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });
  });

  describe('WCAG 2.1 AA Compliance', () => {
    test('GameBoard component has no accessibility violations', async () => {
      const { container } = render(React.createElement(MockGameBoard));
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('SkipLinks component has no accessibility violations', async () => {
      const { container } = render(React.createElement(MockSkipLinks));
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('CollapsibleSidePanel has no accessibility violations', async () => {
      const { container } = render(React.createElement(MockCollapsibleSidePanel));
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('FloatingChatWidget has no accessibility violations', async () => {
      const { container } = render(React.createElement(MockFloatingChatWidget));
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Keyboard Navigation', () => {
    test('Skip links are accessible via keyboard', async () => {
      render(React.createElement(MockSkipLinks));

      const skipToMain = screen.getByText('Skip to main content');
      const skipToInput = screen.getByText('Skip to game input');

      // Test that skip links are present and focusable
      expect(skipToMain).toBeInTheDocument();
      expect(skipToInput).toBeInTheDocument();

      // Test that they have proper href attributes
      expect(skipToMain).toHaveAttribute('href', '#main-content');
      expect(skipToInput).toHaveAttribute('href', '#game-input');
    });

    test('Interactive elements are keyboard accessible', async () => {
      const MockInteractiveComponent = () => React.createElement('div', null,
        React.createElement('input', { 'aria-label': 'Define the word' }),
        React.createElement('button', { type: 'submit' }, 'Submit')
      );

      render(React.createElement(MockInteractiveComponent));

      const input = screen.getByLabelText(/define the word/i);
      const submitButton = screen.getByRole('button', { name: /submit/i });

      // Test that elements are present and accessible
      expect(input).toBeInTheDocument();
      expect(submitButton).toBeInTheDocument();

      // Test that input has proper labeling
      expect(input).toHaveAttribute('aria-label');
    });

    test('Focus management is properly implemented', async () => {
      const MockFocusComponent = () => React.createElement('div', null,
        React.createElement('button', { 'aria-label': 'Toggle panel' }, 'Toggle'),
        React.createElement('div', { role: 'complementary' }, 'Panel content')
      );

      render(React.createElement(MockFocusComponent));

      const toggleButton = screen.getByRole('button', { name: /toggle/i });

      // Test that button is focusable and has proper labeling
      expect(toggleButton).toBeInTheDocument();
      expect(toggleButton).toHaveAttribute('aria-label');
    });
  });

  describe('Screen Reader Support', () => {
    test('ARIA labels are present and descriptive', () => {
      const MockARIAComponent = () => React.createElement('div', null,
        React.createElement('main', { role: 'main' }, 'Main content'),
        React.createElement('aside', { role: 'complementary' }, 'Side content'),
        React.createElement('input', { 'aria-label': 'Define the word', 'aria-describedby': 'help-text' }),
        React.createElement('div', { role: 'status', 'aria-live': 'polite' }, 'Status updates'),
        React.createElement('div', { id: 'help-text' }, 'Help text')
      );

      render(React.createElement(MockARIAComponent));

      // Check for ARIA landmarks
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('complementary')).toBeInTheDocument();

      // Check for ARIA labels
      const input = screen.getByLabelText(/define the word/i);
      expect(input).toHaveAttribute('aria-describedby');

      // Check for live regions
      const liveRegion = screen.getByRole('status');
      expect(liveRegion).toHaveAttribute('aria-live', 'polite');
    });

    test('Live regions announce updates', () => {
      const MockLiveRegionComponent = () => React.createElement('div', null,
        React.createElement('div', { role: 'status', 'aria-live': 'polite' }, 'Game status'),
        React.createElement('div', { role: 'alert', 'aria-live': 'assertive' }, 'Error messages')
      );

      render(React.createElement(MockLiveRegionComponent));

      const statusRegion = screen.getByRole('status');
      const alertRegion = screen.getByRole('alert');

      expect(statusRegion).toHaveAttribute('aria-live', 'polite');
      expect(alertRegion).toHaveAttribute('aria-live', 'assertive');
    });
  });

  describe('Color Contrast and Visual Accessibility', () => {
    test('Accessibility preferences are detected', () => {
      // Mock high contrast preference
      window.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-contrast: high)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      const MockPreferenceComponent = () => React.createElement('div', {
        className: 'accessibility-test'
      }, 'Accessibility preferences test');

      render(React.createElement(MockPreferenceComponent));

      // Test that matchMedia is working
      expect(window.matchMedia).toBeDefined();
      expect(window.matchMedia('(prefers-contrast: high)').matches).toBe(true);
    });
  });

  describe('Focus Management', () => {
    test('Focusable elements have proper focus indicators', () => {
      const MockFocusableComponent = () => React.createElement('div', null,
        React.createElement('button', { 'aria-label': 'Test button' }, 'Click me'),
        React.createElement('input', { 'aria-label': 'Test input', type: 'text' }),
        React.createElement('a', { href: '#test', 'aria-label': 'Test link' }, 'Link')
      );

      render(React.createElement(MockFocusableComponent));

      const button = screen.getByRole('button');
      const input = screen.getByRole('textbox');
      const link = screen.getByRole('link');

      // Test that focusable elements are present
      expect(button).toBeInTheDocument();
      expect(input).toBeInTheDocument();
      expect(link).toBeInTheDocument();

      // Test that they have proper ARIA labels
      expect(button).toHaveAttribute('aria-label');
      expect(input).toHaveAttribute('aria-label');
      expect(link).toHaveAttribute('aria-label');
    });
  });

  describe('Mobile and Touch Accessibility', () => {
    test('Touch targets meet minimum size requirements', () => {
      const MockTouchComponent = () => React.createElement('div', null,
        React.createElement('button', {
          style: { width: '48px', height: '48px' },
          'aria-label': 'Large touch target'
        }, 'Touch me')
      );

      render(React.createElement(MockTouchComponent));

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label');

      // Check that button has minimum size styles
      const style = button.getAttribute('style');
      expect(style).toContain('width: 48px');
      expect(style).toContain('height: 48px');
    });
  });

  describe('Performance and Low-End Device Support', () => {
    test('Accessibility features are performance optimized', () => {
      const MockPerformanceComponent = () => React.createElement('div', null,
        React.createElement('div', { 'aria-label': 'Performance test' }, 'Content'),
        React.createElement('div', { role: 'status', 'aria-live': 'polite' }, 'Status')
      );

      render(React.createElement(MockPerformanceComponent));

      // Check that accessibility features are present
      const content = screen.getByLabelText('Performance test');
      const status = screen.getByRole('status');

      expect(content).toBeInTheDocument();
      expect(status).toBeInTheDocument();
      expect(status).toHaveAttribute('aria-live', 'polite');
    });
  });
});

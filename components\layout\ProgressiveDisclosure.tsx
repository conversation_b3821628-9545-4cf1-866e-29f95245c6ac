/**
 * ProgressiveDisclosure Component - Contextual Information Revelation (v2.0 Spatial Design)
 *
 * 🎯 SMART INFORMATION MANAGEMENT
 * 
 * Features:
 * - Context-aware information display based on game state
 * - Smooth fade-in/out animations for content transitions
 * - Priority-based information hierarchy
 * - User preference learning and adaptation
 * - Accessibility-first with proper ARIA announcements
 * 
 * Disclosure Rules:
 * - Beginner tips: Show for first 3 games
 * - Validation hints: Show after 2 failed attempts
 * - Advanced stats: Show after step 10
 * - Urgency warnings: Show when < 5 steps remaining
 * - Success celebrations: Show on target completion
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Status, Small, Secondary } from '@/components/ui/Typography';

interface DisclosureRule {
  id: string;
  condition: (context: DisclosureContext) => boolean;
  priority: number; // Higher = more important
  content: React.ReactNode;
  dismissible?: boolean;
  autoHide?: number; // Auto-hide after X seconds
  category: 'tip' | 'warning' | 'success' | 'info' | 'error';
}

interface DisclosureContext {
  gameState: {
    step: number;
    maxSteps: number;
    isFirstGame: boolean;
    consecutiveFailures: number;
    recentSuccess: boolean;
    targetsCompleted: number;
    totalTargets: number;
  };
  userState: {
    gamesPlayed: number;
    showHints: boolean;
    dismissedTips: string[];
  };
  uiState: {
    isInputFocused: boolean;
    hasValidationErrors: boolean;
    isLoading: boolean;
  };
}

interface ProgressiveDisclosureProps {
  context: DisclosureContext;
  rules: DisclosureRule[];
  maxVisible?: number;
  position?: 'top' | 'bottom' | 'floating';
  className?: string;
  onDismiss?: (ruleId: string) => void;
}

export const ProgressiveDisclosure: React.FC<ProgressiveDisclosureProps> = ({
  context,
  rules,
  maxVisible = 2,
  position = 'floating',
  className = '',
  onDismiss
}) => {
  const [visibleRules, setVisibleRules] = useState<DisclosureRule[]>([]);
  const [dismissedRules, setDismissedRules] = useState<Set<string>>(new Set());
  const timeoutRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const handleDismiss = useCallback((ruleId: string) => {
    setDismissedRules(prev => new Set(Array.from(prev).concat(ruleId)));

    // Clear auto-hide timer
    const timeout = timeoutRefs.current.get(ruleId);
    if (timeout) {
      clearTimeout(timeout);
      timeoutRefs.current.delete(ruleId);
    }

    // Notify parent component
    onDismiss?.(ruleId);
  }, [onDismiss]);

  // Evaluate which rules should be visible
  useEffect(() => {
    const applicableRules = rules
      .filter(rule => 
        rule.condition(context) && 
        !dismissedRules.has(rule.id) &&
        !context.userState.dismissedTips.includes(rule.id)
      )
      .sort((a, b) => b.priority - a.priority)
      .slice(0, maxVisible);

    setVisibleRules(applicableRules);

    // Set up auto-hide timers
    applicableRules.forEach(rule => {
      if (rule.autoHide && !timeoutRefs.current.has(rule.id)) {
        const timeout = setTimeout(() => {
          handleDismiss(rule.id);
        }, rule.autoHide * 1000);
        
        timeoutRefs.current.set(rule.id, timeout);
      }
    });

    // Clean up old timers
    timeoutRefs.current.forEach((timeout, ruleId) => {
      if (!applicableRules.find(rule => rule.id === ruleId)) {
        clearTimeout(timeout);
        timeoutRefs.current.delete(ruleId);
      }
    });

    return () => {
      // Capture the current ref value to avoid stale closure
      const currentTimeouts = timeoutRefs.current;
      currentTimeouts.forEach(timeout => clearTimeout(timeout));
      currentTimeouts.clear();
    };
  }, [context, rules, dismissedRules, maxVisible, handleDismiss]);

  const getStatusType = (category: DisclosureRule['category']) => {
    switch (category) {
      case 'tip': return 'info';
      case 'warning': return 'warning';
      case 'success': return 'success';
      case 'error': return 'error';
      case 'info': return 'info';
      default: return 'info';
    }
  };

  const getIcon = (category: DisclosureRule['category']) => {
    switch (category) {
      case 'tip': return '💡';
      case 'warning': return '⚠️';
      case 'success': return '🎉';
      case 'error': return '❌';
      case 'info': return 'ℹ️';
      default: return 'ℹ️';
    }
  };

  if (visibleRules.length === 0) {
    return null;
  }

  const containerClasses = [
    'progressive-disclosure',
    `progressive-disclosure--${position}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div 
      className={containerClasses}
      role="region"
      aria-label="Contextual information"
      aria-live="polite"
    >
      {visibleRules.map((rule, index) => (
        <div
          key={rule.id}
          className={`disclosure-item disclosure-item--${rule.category}`}
          style={{ 
            animationDelay: `${index * 0.1}s`,
            zIndex: visibleRules.length - index
          }}
        >
          <div className="disclosure-content">
            <div className="disclosure-header">
              <span className="disclosure-icon" aria-hidden="true">
                {getIcon(rule.category)}
              </span>
              <div className="disclosure-text">
                {rule.content}
              </div>
            </div>

            {rule.dismissible && (
              <button
                className="disclosure-dismiss"
                onClick={() => handleDismiss(rule.id)}
                aria-label="Dismiss this tip"
                title="Dismiss"
              >
                ×
              </button>
            )}
          </div>

          {rule.autoHide && (
            <div className="disclosure-timer">
              <div 
                className="timer-bar"
                style={{ 
                  animationDuration: `${rule.autoHide}s`,
                  animationDelay: `${index * 0.1}s`
                }}
              />
            </div>
          )}
        </div>
      ))}

      <style jsx>{`
        /* === CORE DISCLOSURE CONTAINER === */
        .progressive-disclosure {
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
          max-width: 400px;
          z-index: var(--z-20);
        }

        .progressive-disclosure--top {
          position: fixed;
          top: var(--space-4);
          left: 50%;
          transform: translateX(-50%);
        }

        .progressive-disclosure--bottom {
          position: fixed;
          bottom: var(--space-4);
          left: 50%;
          transform: translateX(-50%);
        }

        .progressive-disclosure--floating {
          position: fixed;
          top: 50%;
          right: var(--space-4);
          transform: translateY(-50%);
        }

        /* === DISCLOSURE ITEMS === */
        .disclosure-item {
          background: var(--glass-medium);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-lg);
          backdrop-filter: blur(12px);
          box-shadow: var(--shadow-lg);
          overflow: hidden;
          animation: disclosure-enter 0.4s ease-out;
          position: relative;
        }

        .disclosure-item--tip {
          border-left: 4px solid var(--accent-cyan);
        }

        .disclosure-item--warning {
          border-left: 4px solid var(--color-warning);
        }

        .disclosure-item--success {
          border-left: 4px solid var(--color-success);
        }

        .disclosure-item--error {
          border-left: 4px solid var(--color-error);
        }

        .disclosure-item--info {
          border-left: 4px solid var(--accent-purple);
        }

        /* === DISCLOSURE CONTENT === */
        .disclosure-content {
          display: flex;
          align-items: flex-start;
          gap: var(--space-3);
          padding: var(--space-4);
        }

        .disclosure-header {
          display: flex;
          align-items: flex-start;
          gap: var(--space-3);
          flex: 1;
        }

        .disclosure-icon {
          font-size: var(--text-lg);
          line-height: 1;
          flex-shrink: 0;
          margin-top: 2px;
        }

        .disclosure-text {
          flex: 1;
          font-size: var(--text-sm);
          line-height: var(--leading-relaxed);
          color: var(--text-primary);
        }

        /* === DISMISS BUTTON === */
        .disclosure-dismiss {
          background: none;
          border: none;
          color: var(--text-muted);
          font-size: var(--text-lg);
          cursor: pointer;
          padding: var(--space-1);
          border-radius: var(--radius-base);
          transition: all var(--transition-base);
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .disclosure-dismiss:hover {
          background: var(--glass-light);
          color: var(--text-primary);
        }

        /* === AUTO-HIDE TIMER === */
        .disclosure-timer {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: var(--glass-border);
          overflow: hidden;
        }

        .timer-bar {
          height: 100%;
          background: var(--accent-cyan);
          width: 100%;
          transform-origin: left;
          animation: timer-countdown linear;
        }

        /* === ANIMATIONS === */
        @keyframes disclosure-enter {
          0% {
            opacity: 0;
            transform: translateX(100%) scale(0.9);
          }
          100% {
            opacity: 1;
            transform: translateX(0) scale(1);
          }
        }

        @keyframes timer-countdown {
          0% {
            transform: scaleX(1);
          }
          100% {
            transform: scaleX(0);
          }
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .progressive-disclosure {
            max-width: calc(100vw - var(--space-8));
          }

          .progressive-disclosure--top,
          .progressive-disclosure--bottom {
            left: var(--space-4);
            right: var(--space-4);
            transform: none;
          }

          .progressive-disclosure--floating {
            top: auto;
            bottom: var(--space-4);
            right: var(--space-4);
            left: var(--space-4);
            transform: none;
          }

          .disclosure-content {
            padding: var(--space-3);
          }

          .disclosure-text {
            font-size: var(--text-xs);
          }
        }

        /* === ACCESSIBILITY === */
        @media (prefers-reduced-motion: reduce) {
          .disclosure-item,
          .timer-bar {
            animation: none !important;
          }
        }

        .disclosure-dismiss:focus {
          outline: 2px solid var(--accent-cyan);
          outline-offset: 2px;
        }

        /* === HIGH CONTRAST === */
        @media (prefers-contrast: high) {
          .disclosure-item {
            border-width: 2px;
            background: var(--bg-primary);
          }

          .disclosure-dismiss {
            border: 1px solid var(--text-muted);
          }
        }
      `}</style>
    </div>
  );
};

export default ProgressiveDisclosure;

import React from 'react';
import <PERSON> from 'next/head';
import GameBoard from '@/components/GameBoard';
import GameLayout from '@/components/layout/GameLayout';

const HomePage: React.FC = () => {
  return (
    <>
      <Head>
        <title>DEFEATER.AI - The Dark Souls of Puzzle Games</title>
        <meta 
          name="description" 
          content="Challenge state-of-the-art AI in pure intellectual combat. Navigate through definitions to reach target words in this brutally fair reasoning game." 
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        
        {/* Open Graph / Social Media */}
        <meta property="og:type" content="website" />
        <meta property="og:title" content="DEFEATER.AI - The Dark Souls of Puzzle Games" />
        <meta property="og:description" content="Think you can outsmart AI? Try to define your way to victory in this addictive reasoning game." />
        <meta property="og:url" content="https://defeater.ai" />
        
        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="DEFEATER.AI - The Dark Souls of Puzzle Games" />
        <meta name="twitter:description" content="Challenge AI in pure intellectual combat. Can you navigate through definitions to victory?" />
        
        {/* Fonts preload */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </Head>

      <GameLayout>
        <GameBoard />
      </GameLayout>
    </>
  );
};

export default HomePage;

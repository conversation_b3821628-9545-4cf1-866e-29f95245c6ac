/**
 * useGameState Hook - Core Game State Management (v2.0)
 *
 * 🎯 CENTRALIZED GAME STATE MANAGEMENT
 * 
 * Features:
 * - Core game state management (GameState, AIResponse, errors)
 * - Optimized state updates with batching
 * - Performance monitoring integration
 * - Type-safe state operations
 * - Memory leak prevention
 * 
 * Performance Benefits:
 * - Reduced re-renders through targeted updates
 * - Memoized state operations
 * - Optimized dependency arrays
 * - State batching for related updates
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_4_STATE_EXTRACTION.md
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { GameState, AIResponse } from '@/types/game';

interface UseGameStateOptions {
  initialGameState?: GameState | null;
  enablePerformanceMonitoring?: boolean;
}

interface UseGameStateReturn {
  // State
  gameState: GameState | null;
  lastAIResponse: AIResponse | undefined;
  error: string;
  
  // State Setters
  setGameState: (gameState: GameState | null) => void;
  setLastAIResponse: (response: AIResponse | undefined) => void;
  setError: (error: string) => void;
  
  // Optimized State Operations
  updateGameState: (updates: Partial<GameState>) => void;
  batchUpdateGameState: (updates: Partial<GameState>, aiResponse?: AIResponse) => void;
  resetGameState: () => void;
  clearError: () => void;
  
  // Performance Monitoring
  getStateMetrics: () => GameStateMetrics;
}

interface GameStateMetrics {
  updateCount: number;
  lastUpdateTime: number;
  averageUpdateTime: number;
  memoryUsage: number;
}

export function useGameState(options: UseGameStateOptions = {}): UseGameStateReturn {
  const { 
    initialGameState = null, 
    enablePerformanceMonitoring = process.env.NODE_ENV === 'development' 
  } = options;

  // Core State
  const [gameState, setGameStateInternal] = useState<GameState | null>(initialGameState);
  const [lastAIResponse, setLastAIResponseInternal] = useState<AIResponse | undefined>();
  const [error, setErrorInternal] = useState<string>('');

  // Performance Monitoring
  const updateCountRef = useRef<number>(0);
  const updateTimesRef = useRef<number[]>([]);
  const lastUpdateTimeRef = useRef<number>(0);

  // Performance tracking wrapper
  const trackStateUpdate = useCallback((operation: () => void) => {
    if (!enablePerformanceMonitoring) {
      operation();
      return;
    }

    const startTime = performance.now();
    operation();
    const endTime = performance.now();
    
    updateCountRef.current += 1;
    lastUpdateTimeRef.current = endTime - startTime;
    updateTimesRef.current.push(lastUpdateTimeRef.current);
    
    // Keep only last 100 measurements for memory efficiency
    if (updateTimesRef.current.length > 100) {
      updateTimesRef.current = updateTimesRef.current.slice(-100);
    }
  }, [enablePerformanceMonitoring]);

  // Optimized State Setters
  const setGameState = useCallback((newGameState: GameState | null) => {
    trackStateUpdate(() => {
      setGameStateInternal(newGameState);
    });
  }, [trackStateUpdate]);

  const setLastAIResponse = useCallback((response: AIResponse | undefined) => {
    trackStateUpdate(() => {
      setLastAIResponseInternal(response);
    });
  }, [trackStateUpdate]);

  const setError = useCallback((newError: string) => {
    trackStateUpdate(() => {
      setErrorInternal(newError);
    });
  }, [trackStateUpdate]);

  // Optimized State Operations
  const updateGameState = useCallback((updates: Partial<GameState>) => {
    trackStateUpdate(() => {
      setGameStateInternal(prevState => {
        if (!prevState) return null;
        return { ...prevState, ...updates };
      });
    });
  }, [trackStateUpdate]);

  const batchUpdateGameState = useCallback((updates: Partial<GameState>, aiResponse?: AIResponse) => {
    trackStateUpdate(() => {
      // Batch multiple state updates to prevent unnecessary re-renders
      setGameStateInternal(prevState => {
        if (!prevState) return null;
        return { ...prevState, ...updates };
      });
      
      if (aiResponse !== undefined) {
        setLastAIResponseInternal(aiResponse);
      }
    });
  }, [trackStateUpdate]);

  const resetGameState = useCallback(() => {
    trackStateUpdate(() => {
      setGameStateInternal(null);
      setLastAIResponseInternal(undefined);
      setErrorInternal('');
    });
  }, [trackStateUpdate]);

  const clearError = useCallback(() => {
    trackStateUpdate(() => {
      setErrorInternal('');
    });
  }, [trackStateUpdate]);

  // Performance Metrics
  const getStateMetrics = useCallback((): GameStateMetrics => {
    const averageUpdateTime = updateTimesRef.current.length > 0
      ? updateTimesRef.current.reduce((sum, time) => sum + time, 0) / updateTimesRef.current.length
      : 0;

    // Estimate memory usage (rough calculation)
    const gameStateSize = gameState ? JSON.stringify(gameState).length * 2 : 0; // UTF-16
    const aiResponseSize = lastAIResponse ? JSON.stringify(lastAIResponse).length * 2 : 0;
    const errorSize = error.length * 2;
    const memoryUsage = gameStateSize + aiResponseSize + errorSize;

    return {
      updateCount: updateCountRef.current,
      lastUpdateTime: lastUpdateTimeRef.current,
      averageUpdateTime,
      memoryUsage
    };
  }, [gameState, lastAIResponse, error]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear any pending timeouts or cleanup
      updateCountRef.current = 0;
      updateTimesRef.current = [];
      lastUpdateTimeRef.current = 0;
    };
  }, []);

  // Development logging
  useEffect(() => {
    if (enablePerformanceMonitoring && gameState) {
      console.log('🎮 useGameState: State updated', {
        step: gameState.step,
        status: gameState.gameStatus,
        updateCount: updateCountRef.current,
        lastUpdateTime: lastUpdateTimeRef.current
      });
    }
  }, [gameState, enablePerformanceMonitoring]);

  return {
    // State
    gameState,
    lastAIResponse,
    error,
    
    // State Setters
    setGameState,
    setLastAIResponse,
    setError,
    
    // Optimized State Operations
    updateGameState,
    batchUpdateGameState,
    resetGameState,
    clearError,
    
    // Performance Monitoring
    getStateMetrics
  };
}

export default useGameState;

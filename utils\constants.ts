/**
 * DEFEATER.AI Constants
 * Centralized configuration and magic numbers
 */

// API Configuration
export const API_CONFIG = {
  TIMEOUT_MS: 60000, // 60 seconds for local Ollama
  MAX_RETRIES: 3,
  RETRY_DELAY_MS: 1000,
} as const;

// Ollama Configuration
export const OLLAMA_CONFIG = {
  apiUrl: 'http://localhost:11434',
  model: 'gemma3-4b-gpu',
  defaultTemperature: 0.6,
  maxTokens: 2048,
} as const;

// Difficulty Configuration (MVP Core Mechanics)
export const DIFFICULTY_CONFIG = {
  easy: {
    maxSteps: 30,
    revealFrequency: 2,
    burnFrequency: 8,
    commonWordLimit: Infinity,
    wordCountRule: 'countdown' as const,
    initialWordCount: 10,
  },
  medium: {
    maxSteps: 25,
    revealFrequency: 3,
    burnFrequency: 6,
    commonWordLimit: 5,
    wordCountRule: 'shorter' as const,
    initialWordCount: 10,
  },
  hard: {
    maxSteps: 20,
    revealFrequency: 4,
    burnFrequency: 5,
    commonWordLimit: 0,
    wordCountRule: 'aggressive' as const,
    initialWordCount: 10,
  },
} as const;

// Game Balance Constants
export const GAME_BALANCE = {
  CONFIDENCE_THRESHOLDS: {
    HIGH: 0.7,
    MEDIUM: 0.5,
    LOW: 0.3,
  },
  WORD_SELECTION_PROBABILITY: {
    FROM_DEFINITION: 0.8,
    SEMANTIC_BRIDGE: 0.15,
    DOMAIN_SWITCH: 0.05,
  },
  PHASE_BOUNDARIES: {
    OPENING_END: 5,
    MIDDLE_END: 15,
    ENDGAME_START: 16,
  },
} as const;

// AI Temperature by Phase
export const AI_TEMPERATURE = {
  OPENING: 0.7,
  MIDDLE: 0.5,
  ENDGAME: 0.3,
} as const;

// UI Constants
export const UI_CONFIG = {
  ANIMATION_DURATION_MS: 500,
  SHAKE_DURATION_MS: 500,
  TYPING_DELAY_MS: 100,
  MIN_WORD_LENGTH: 1,
  MAX_WORD_LENGTH: 50,
} as const;

// Validation Constants
export const VALIDATION = {
  MIN_DEFINITION_LENGTH: 1, // Characters
  MAX_DEFINITION_LENGTH: 500, // Characters
  // Common words that get reuse allowances (from MVP spec)
  COMMON_WORDS: [
    'the', 'a', 'an', 'and', 'or', 'but', 'is', 'are', 'was', 'were',
    'to', 'of', 'in', 'on', 'at', 'by', 'for', 'with', 'from'
  ],
  // Game rule constants
  MAX_CONSECUTIVE_REJECTIONS: 3,
  MIN_TURNS_BEFORE_WIN: 3,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  AI_TIMEOUT: 'AI response timed out. Please try again.',
  INVALID_DEFINITION: 'Invalid definition. Please try again.',
  GAME_ERROR: 'Game error occurred. Please restart.',
  VALIDATION_ERROR: 'Definition validation failed.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  GAME_WON: 'Congratulations! You defeated the AI!',
  DEFINITION_ACCEPTED: 'Definition accepted.',
  GAME_STARTED: 'Game started successfully.',
} as const;

// Development Flags
export const DEV_FLAGS = {
  ENABLE_LOGGING: process.env.NODE_ENV === 'development',
  ENABLE_DEBUG_MODE: process.env.DEBUG === 'true',
  SKIP_AI_VALIDATION: process.env.SKIP_AI === 'true',
} as const;

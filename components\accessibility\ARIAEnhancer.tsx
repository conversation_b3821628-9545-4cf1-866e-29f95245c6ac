/**
 * ARIAEnhancer Component - Enhanced ARIA Descriptions
 * 
 * Provides comprehensive ARIA descriptions for complex game interactions
 * to improve screen reader experience and accessibility.
 * 
 * Features:
 * - Dynamic ARIA descriptions based on game state
 * - Context-aware labeling
 * - Progress announcements
 * - Error state descriptions
 * - Game status updates
 * 
 * WCAG 2.1 AA Compliance:
 * - 4.1.2 Name, Role, Value
 * - 4.1.3 Status Messages
 * - 3.3.1 Error Identification
 */

import React, { useEffect, useRef } from 'react';
import { GameState } from '@/types/game';

interface ARIAEnhancerProps {
  gameState?: GameState;
  currentInput?: string;
  isLoading?: boolean;
  error?: string;
  children: React.ReactNode;
}

interface ARIADescriptions {
  gameProgress: string;
  targetStatus: string;
  inputGuidance: string;
  errorContext: string;
  gameInstructions: string;
}

export const ARIAEnhancer: React.FC<ARIAEnhancerProps> = ({
  gameState,
  currentInput = '',
  isLoading = false,
  error = '',
  children
}) => {
  const announcementRef = useRef<HTMLDivElement>(null);
  const previousGameStateRef = useRef<GameState | undefined>();

  // Generate comprehensive ARIA descriptions
  const generateARIADescriptions = (): ARIADescriptions => {
    if (!gameState) {
      return {
        gameProgress: 'Game not started',
        targetStatus: 'No targets available',
        inputGuidance: 'Start a new game to begin',
        errorContext: '',
        gameInstructions: 'Use the New Game button to start playing DEFEATER.AI'
      };
    }

    const remainingTargets = gameState.targets.filter(
      target => !gameState.burnedTargets.includes(target)
    );
    
    const progressPercentage = Math.round((gameState.step / gameState.maxSteps) * 100);
    
    const gameProgress = `Step ${gameState.step} of ${gameState.maxSteps}, ${progressPercentage}% complete. ${
      gameState.gameStatus === 'waiting' ? 'Waiting for your definition' :
      gameState.gameStatus === 'won' ? 'Game won!' :
      gameState.gameStatus === 'lost' ? 'Game lost' : 'Game in progress'
    }.`;

    const targetStatus = `${remainingTargets.length} targets remaining: ${
      remainingTargets.map(target => {
        const revealed = target.split('').map((char, i) =>
          i === 0 || i === target.length - 1 || gameState.step >= (i + 1) * 3
            ? char : 'hidden letter'
        ).join(' ');
        return `${target} revealed as ${revealed}`;
      }).join(', ')
    }. ${gameState.burnedTargets.length} targets burned: ${gameState.burnedTargets.join(', ')}.`;

    const wordsLeft = gameState.definitions.length > 0
      ? gameState.definitions[gameState.definitions.length - 1].wordCount - 1
      : 15;

    const inputGuidance = `Define "${gameState.currentWord}" using ${wordsLeft} words or fewer. ${
      gameState.consecutiveRejections > 0 
        ? `Warning: ${gameState.consecutiveRejections} consecutive rejections. ${3 - gameState.consecutiveRejections} strikes remaining.`
        : ''
    }`;

    const errorContext = error ? `Error: ${error}. Please try a different definition.` : '';

    const gameInstructions = `DEFEATER.AI game: Define words to reach target words. Each definition must be shorter than the previous. The AI will try to predict and burn your targets. Current difficulty: ${gameState.difficulty}.`;

    return {
      gameProgress,
      targetStatus,
      inputGuidance,
      errorContext,
      gameInstructions
    };
  };

  const descriptions = generateARIADescriptions();

  // Announce important game state changes
  useEffect(() => {
    if (!gameState || !previousGameStateRef.current) {
      previousGameStateRef.current = gameState;
      return;
    }

    const prev = previousGameStateRef.current;
    const current = gameState;

    let announcement = '';

    // New word to define
    if (prev.currentWord !== current.currentWord) {
      announcement = `New word to define: ${current.currentWord}`;
    }

    // Target burned
    if (current.burnedTargets.length > prev.burnedTargets.length) {
      const newBurned = current.burnedTargets.filter(t => !prev.burnedTargets.includes(t));
      announcement = `Target burned: ${newBurned.join(', ')}. ${current.targets.length - current.burnedTargets.length} targets remaining.`;
    }

    // Game status change
    if (prev.gameStatus !== current.gameStatus) {
      if (current.gameStatus === 'won') {
        announcement = `Congratulations! You won the game in ${current.step} steps!`;
      } else if (current.gameStatus === 'lost') {
        announcement = `Game over. You reached the maximum steps or strikes.`;
      }
    }

    // Consecutive rejections warning
    if (current.consecutiveRejections > prev.consecutiveRejections && current.consecutiveRejections >= 2) {
      announcement = `Warning: ${current.consecutiveRejections} consecutive rejections. ${3 - current.consecutiveRejections} strikes remaining before game over.`;
    }

    if (announcement && announcementRef.current) {
      announceToScreenReader(announcement, 'assertive');
    }

    previousGameStateRef.current = current;
  }, [gameState]);

  // Announce loading states
  useEffect(() => {
    if (isLoading) {
      announceToScreenReader('Processing your definition, please wait...', 'polite');
    }
  }, [isLoading]);

  // Announce errors
  useEffect(() => {
    if (error) {
      announceToScreenReader(`Error: ${error}`, 'assertive');
    }
  }, [error]);

  const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
    }, 1000);
  };

  return (
    <div className="aria-enhancer">
      {/* Hidden descriptions for screen readers */}
      <div className="sr-only">
        <div id="game-instructions" aria-label="Game instructions">
          {descriptions.gameInstructions}
        </div>
        <div id="game-progress" aria-label="Game progress">
          {descriptions.gameProgress}
        </div>
        <div id="target-status" aria-label="Target status">
          {descriptions.targetStatus}
        </div>
        <div id="input-guidance" aria-label="Input guidance">
          {descriptions.inputGuidance}
        </div>
        {descriptions.errorContext && (
          <div id="error-context" aria-label="Error context">
            {descriptions.errorContext}
          </div>
        )}
      </div>

      {/* Live region for announcements */}
      <div
        ref={announcementRef}
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      />

      {/* Enhanced children with ARIA attributes */}
      <div
        role="application"
        aria-label="DEFEATER.AI Game"
        aria-describedby="game-instructions game-progress target-status input-guidance"
      >
        {children}
      </div>

      <style jsx>{`
        .aria-enhancer {
          position: relative;
        }

        .sr-only {
          position: absolute;
          width: 1px;
          height: 1px;
          padding: 0;
          margin: -1px;
          overflow: hidden;
          clip: rect(0, 0, 0, 0);
          white-space: nowrap;
          border: 0;
        }
      `}</style>
    </div>
  );
};

// Hook for enhanced ARIA labeling
export const useARIALabels = (gameState?: GameState) => {
  const getInputLabel = () => {
    if (!gameState) return 'Definition input';
    
    const wordsLeft = gameState.definitions.length > 0
      ? gameState.definitions[gameState.definitions.length - 1].wordCount - 1
      : 15;
    
    return `Define "${gameState.currentWord}" using ${wordsLeft} words or fewer`;
  };

  const getSubmitLabel = () => {
    if (!gameState) return 'Submit definition';
    return `Submit definition for "${gameState.currentWord}"`;
  };

  const getTargetLabel = (target: string, index: number) => {
    if (!gameState) return `Target ${index + 1}: ${target}`;
    
    const isBurned = gameState.burnedTargets.includes(target);
    const revealed = target.split('').map((char, i) =>
      i === 0 || i === target.length - 1 || gameState.step >= (i + 1) * 3
        ? char : 'hidden'
    ).join(' ');
    
    return `Target ${index + 1}: ${target}, ${isBurned ? 'burned' : 'active'}, revealed as ${revealed}`;
  };

  const getProgressLabel = () => {
    if (!gameState) return 'Game not started';
    
    const percentage = Math.round((gameState.step / gameState.maxSteps) * 100);
    return `Game progress: ${gameState.step} of ${gameState.maxSteps} steps, ${percentage}% complete`;
  };

  return {
    getInputLabel,
    getSubmitLabel,
    getTargetLabel,
    getProgressLabel
  };
};

export default ARIAEnhancer;

# 🎯 DEFEATER.AI - Developer Start Guide

> **"The Dark Souls of puzzle games"** - Where confidence goes to die, and reasoning is reborn.

Welcome to the DEFEATER.AI codebase! This guide will get you oriented and productive quickly.

---

## 🎮 **What is DEFEATER.AI?**

DEFEATER.AI is a psychological warfare puzzle game where humans battle an AI opponent through word definitions. Players must navigate from one concept to another through increasingly constrained definitions while the AI strategically burns target words and manipulates the game flow.

### **Core Game Loop**
1. **Player** defines a word given by the AI
2. **AI** chooses the next word from the player's definition
3. **Constraints** tighten (fewer words allowed each turn)
4. **Goal**: Include a target word in your definition
5. **AI Goal**: Burn all targets before player reaches them

---

## 🏗️ **Codebase Architecture**

### **Technology Stack**
- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS with custom design system
- **AI Integration**: Ollama (local) with Gemma3-4B-GPU model
- **State Management**: React hooks and context
- **Testing**: Jest, React Testing Library
- **Accessibility**: WCAG AA compliant

### **Key Systems**

#### **1. Game Engine** (`/utils/gameLogic.ts`, `/utils/deepseek.ts`)
- Turn-based definition validation
- AI strategic decision making
- Win/loss condition detection
- Progressive target revelation

#### **2. AI Game Master** (`/utils/deepseek.ts`, `/utils/aiTrashTalk.ts`)
- Ollama/Gemma3 integration for game decisions
- Psychological warfare personality
- Strategic word selection and target burning
- Real-time trash talk generation

#### **3. Spatial Layout System** (`/components/layout/`)
- Open, breathing room design philosophy
- No containers or boxed feelings
- Responsive spatial relationships
- Performance-optimized animations

#### **4. Chat System** (`/components/chat/`)
- Real-time AI psychological warfare
- Direct Ollama integration
- Context-aware responses
- Two-way player interaction

---

## 📁 **Directory Structure**

```
/components
├── /consolidated     # Main UI components (new system)
├── /game            # Game-specific components
├── /layout          # Spatial design system
├── /chat            # AI chat interface
├── /accessibility   # A11y components
├── /dev             # Development tools
└── [legacy]         # Old components (being phased out)

/utils
├── deepseek.ts      # AI integration & game master
├── gameLogic.ts     # Core game mechanics
├── aiTrashTalk.ts   # Psychological warfare system
├── wordPool.ts      # Random word generation
└── performance.ts   # Optimization utilities

/pages
├── /api
│   ├── game.ts      # Main game API endpoint
│   └── chat.ts      # AI chat API endpoint
└── index.tsx        # Main game interface

/docs
├── START_HERE.md           # This file
├── MVP_CORE_MECHANICS.md   # Game rules and mechanics
├── TECHNICAL_ARCHITECTURE.md # System design
└── GAME_MASTER_SYSTEM.md   # AI behavior documentation
```

---

## 🚀 **Quick Start**

### **1. Environment Setup**
```bash
# Install dependencies
npm install

# Set up Ollama (local AI)
# Download and install Ollama from https://ollama.ai
ollama pull gemma3:4b
ollama create gemma3-4b-gpu -f Modelfile.gemma3-gpu

# Start development server
npm run dev
```

### **2. Key Environment Variables**
```env
# .env.local
NEXT_PUBLIC_OLLAMA_URL=http://localhost:11434
NODE_ENV=development
```

### **3. First Steps**
1. **Start the game** at `http://localhost:3000`
2. **Open DevTools** to see AI decision logging
3. **Try the chat system** for AI psychological warfare
4. **Review game mechanics** in `/docs/MVP_CORE_MECHANICS.md`

---

## 🎯 **Key Concepts**

### **The "Secret Sauce" - AI Game Master**
The AI isn't just validating definitions—it's actively trying to defeat the player through:
- **Strategic word selection** from player definitions
- **Psychological manipulation** through trash talk
- **Target burning** at optimal moments
- **Semantic trap setting** to corner players

### **Spatial Design Philosophy**
- **No containers**: Avoid boxed, cramped feelings
- **Breathing room**: Open, spacious layouts
- **Confident typography**: Bold, imposing text
- **Minimal friction**: Streamlined user experience

### **Performance First**
- **GPU acceleration** for AI models
- **Component optimization** for low-end devices
- **Bundle size management**
- **Accessibility without performance cost**

---

## 🔧 **Development Workflow**

### **Making Changes**
1. **Game Mechanics**: Edit `/utils/gameLogic.ts` and `/utils/deepseek.ts`
2. **UI Components**: Use `/components/consolidated/` for new components
3. **AI Behavior**: Modify prompts in `/utils/deepseek.ts`
4. **Styling**: Follow spatial design principles in `/components/layout/`

### **Testing**
```bash
# Run all tests
npm test

# Accessibility testing
npm run test:a11y

# Performance testing
npm run test:performance
```

### **Debugging AI Decisions**
- Enable dev panel in-game for AI decision insights
- Check browser console for detailed AI reasoning
- Use `/api/debug-chat` endpoint for chat debugging

---

## 📚 **Essential Reading**

1. **[MVP Core Mechanics](./MVP_CORE_MECHANICS.md)** - Game rules and win conditions
2. **[Technical Architecture](./TECHNICAL_ARCHITECTURE.md)** - System design details
3. **[Game Master System](./GAME_MASTER_SYSTEM.md)** - AI behavior and prompts
4. **[API Reference](./API_REFERENCE.md)** - Endpoint documentation

---

## 🆘 **Getting Help**

### **Common Issues**
- **Ollama not responding**: Check if service is running on port 11434
- **AI making poor decisions**: Review prompts in `/utils/deepseek.ts`
- **Performance issues**: Check GPU utilization and component rendering
- **Layout problems**: Verify spatial design principles

### **Debugging Tools**
- **Dev Panel**: In-game debugging interface
- **Performance Monitor**: Real-time performance metrics
- **Chat Debug**: AI response logging
- **Accessibility Audit**: WCAG compliance checking

---

## 🎯 **Current Status & Next Steps**

### **✅ Completed**
- Core game mechanics with AI integration
- Real-time chat system with psychological warfare
- Spatial layout system implementation
- Performance optimization and accessibility compliance

### **🚧 In Progress**
- Code cleanup and organization (see `COMPREHENSIVE_CLEANUP_PLAN.md`)
- Documentation updates and standardization
- Component consolidation and dead code removal

### **🔮 Future Plans**
- Multi-language support
- Advanced AI personalities
- Tournament and ranking systems
- Mobile optimization

---

*Ready to defeat some humans? Let's build something that makes people quit 10 times but come back 11.* 🤖⚔️

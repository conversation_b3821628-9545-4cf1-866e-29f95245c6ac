/**
 * FeedbackContext - Global Feedback State Management (v2.0 Spatial Design)
 *
 * 🎯 CENTRALIZED FEEDBACK ORCHESTRATION
 * 
 * Features:
 * - Global feedback state management across components
 * - Smart feedback deduplication and priority handling
 * - Performance tracking for adaptive feedback
 * - User preference learning and adaptation
 * - Cross-component feedback coordination
 * 
 * Responsibilities:
 * - Track user interaction patterns
 * - Manage feedback display preferences
 * - Coordinate between different feedback sources
 * - Learn from user dismissal patterns
 * - Optimize feedback timing and relevance
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { GameState, PlayerProfile } from '@/types/game';

interface FeedbackPreferences {
  enableValidationFeedback: boolean;
  enableStrategicFeedback: boolean;
  enableEncouragementFeedback: boolean;
  enableEducationalFeedback: boolean;
  feedbackFrequency: 'minimal' | 'normal' | 'verbose';
  autoHideDelay: number; // seconds
  maxSimultaneousFeedback: number;
}

interface FeedbackMetrics {
  totalShown: number;
  totalDismissed: number;
  averageViewTime: number;
  dismissalPatterns: Record<string, number>;
  effectivenessByType: Record<string, number>;
}

interface FeedbackState {
  preferences: FeedbackPreferences;
  metrics: FeedbackMetrics;
  activeFeedback: string[];
  dismissedFeedback: string[];
  recentInteractions: Array<{
    type: string;
    timestamp: number;
    context: string;
  }>;
  learningData: {
    playerSkillLevel: 'beginner' | 'intermediate' | 'advanced';
    preferredFeedbackTypes: string[];
    optimalTiming: Record<string, number>;
  };
}

type FeedbackAction =
  | { type: 'SHOW_FEEDBACK'; payload: { id: string; category: string } }
  | { type: 'DISMISS_FEEDBACK'; payload: { id: string; viewTime: number } }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<FeedbackPreferences> }
  | { type: 'RECORD_INTERACTION'; payload: { type: string; context: string } }
  | { type: 'UPDATE_SKILL_ASSESSMENT'; payload: { gameState: GameState } }
  | { type: 'RESET_SESSION'; payload: {} };

interface FeedbackContextType {
  state: FeedbackState;
  dispatch: React.Dispatch<FeedbackAction>;
  shouldShowFeedback: (feedbackId: string, category: string, context: any) => boolean;
  recordFeedbackInteraction: (feedbackId: string, action: 'shown' | 'dismissed' | 'acted_upon') => void;
  updatePlayerSkillAssessment: (gameState: GameState) => void;
  getOptimalFeedbackTiming: (feedbackType: string) => number;
}

const defaultPreferences: FeedbackPreferences = {
  enableValidationFeedback: true,
  enableStrategicFeedback: true,
  enableEncouragementFeedback: true,
  enableEducationalFeedback: true,
  feedbackFrequency: 'normal',
  autoHideDelay: 8,
  maxSimultaneousFeedback: 2
};

const defaultMetrics: FeedbackMetrics = {
  totalShown: 0,
  totalDismissed: 0,
  averageViewTime: 0,
  dismissalPatterns: {},
  effectivenessByType: {}
};

const initialState: FeedbackState = {
  preferences: defaultPreferences,
  metrics: defaultMetrics,
  activeFeedback: [],
  dismissedFeedback: [],
  recentInteractions: [],
  learningData: {
    playerSkillLevel: 'beginner',
    preferredFeedbackTypes: [],
    optimalTiming: {}
  }
};

const FeedbackContext = createContext<FeedbackContextType | undefined>(undefined);

function feedbackReducer(state: FeedbackState, action: FeedbackAction): FeedbackState {
  switch (action.type) {
    case 'SHOW_FEEDBACK':
      return {
        ...state,
        activeFeedback: [...state.activeFeedback, action.payload.id],
        metrics: {
          ...state.metrics,
          totalShown: state.metrics.totalShown + 1
        }
      };

    case 'DISMISS_FEEDBACK':
      const { id, viewTime } = action.payload;
      return {
        ...state,
        activeFeedback: state.activeFeedback.filter(feedbackId => feedbackId !== id),
        dismissedFeedback: [...state.dismissedFeedback, id],
        metrics: {
          ...state.metrics,
          totalDismissed: state.metrics.totalDismissed + 1,
          averageViewTime: (state.metrics.averageViewTime + viewTime) / 2,
          dismissalPatterns: {
            ...state.metrics.dismissalPatterns,
            [id]: (state.metrics.dismissalPatterns[id] || 0) + 1
          }
        }
      };

    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: {
          ...state.preferences,
          ...action.payload
        }
      };

    case 'RECORD_INTERACTION':
      return {
        ...state,
        recentInteractions: [
          ...state.recentInteractions.slice(-19), // Keep last 20
          {
            type: action.payload.type,
            timestamp: Date.now(),
            context: action.payload.context
          }
        ]
      };

    case 'UPDATE_SKILL_ASSESSMENT':
      const gameState = action.payload.gameState;
      const newSkillLevel = assessPlayerSkill(gameState, state.metrics);
      return {
        ...state,
        learningData: {
          ...state.learningData,
          playerSkillLevel: newSkillLevel
        }
      };

    case 'RESET_SESSION':
      return {
        ...state,
        activeFeedback: [],
        dismissedFeedback: [],
        recentInteractions: []
      };

    default:
      return state;
  }
}

// Assess player skill based on game performance
function assessPlayerSkill(gameState: GameState, metrics: FeedbackMetrics): 'beginner' | 'intermediate' | 'advanced' {
  const efficiency = gameState.definitions.length > 0 
    ? gameState.definitions.reduce((sum, def) => sum + def.wordCount, 0) / gameState.definitions.length
    : 10;
  
  const targetRate = gameState.targets?.length > 0 
    ? (gameState.completedTargets?.length || 0) / gameState.targets.length 
    : 0;

  const feedbackDependency = metrics.totalShown > 0 
    ? metrics.totalDismissed / metrics.totalShown 
    : 0;

  // Advanced: Efficient, high target rate, dismisses feedback quickly
  if (efficiency <= 4 && targetRate >= 0.6 && feedbackDependency >= 0.7) {
    return 'advanced';
  }
  
  // Intermediate: Moderate efficiency, some targets, moderate feedback usage
  if (efficiency <= 7 && targetRate >= 0.3 && feedbackDependency >= 0.4) {
    return 'intermediate';
  }
  
  // Beginner: Needs guidance
  return 'beginner';
}

export const FeedbackProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(feedbackReducer, initialState);

  // Load preferences from localStorage
  useEffect(() => {
    const savedPreferences = localStorage.getItem('defeater-ai-feedback-preferences');
    if (savedPreferences) {
      try {
        const preferences = JSON.parse(savedPreferences);
        dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
      } catch (error) {
        console.warn('Failed to load feedback preferences:', error);
      }
    }
  }, []);

  // Save preferences to localStorage
  useEffect(() => {
    localStorage.setItem('defeater-ai-feedback-preferences', JSON.stringify(state.preferences));
  }, [state.preferences]);

  // Smart feedback filtering logic
  const shouldShowFeedback = (feedbackId: string, category: string, context: any): boolean => {
    // Check if already dismissed recently
    if (state.dismissedFeedback.includes(feedbackId)) {
      return false;
    }

    // Check if too many active feedback items
    if (state.activeFeedback.length >= state.preferences.maxSimultaneousFeedback) {
      return false;
    }

    // Check category preferences
    switch (category) {
      case 'validation':
        return state.preferences.enableValidationFeedback;
      case 'strategic':
        return state.preferences.enableStrategicFeedback;
      case 'encouragement':
        return state.preferences.enableEncouragementFeedback;
      case 'educational':
        return state.preferences.enableEducationalFeedback;
      default:
        return true;
    }
  };

  // Record feedback interactions for learning
  const recordFeedbackInteraction = (feedbackId: string, action: 'shown' | 'dismissed' | 'acted_upon') => {
    dispatch({
      type: 'RECORD_INTERACTION',
      payload: {
        type: `feedback_${action}`,
        context: feedbackId
      }
    });

    if (action === 'shown') {
      dispatch({
        type: 'SHOW_FEEDBACK',
        payload: { id: feedbackId, category: 'unknown' }
      });
    } else if (action === 'dismissed') {
      dispatch({
        type: 'DISMISS_FEEDBACK',
        payload: { id: feedbackId, viewTime: 5 } // Default view time
      });
    }
  };

  // Update skill assessment
  const updatePlayerSkillAssessment = (gameState: GameState) => {
    dispatch({
      type: 'UPDATE_SKILL_ASSESSMENT',
      payload: { gameState }
    });
  };

  // Get optimal timing for feedback type
  const getOptimalFeedbackTiming = (feedbackType: string): number => {
    const baseDelay = state.preferences.autoHideDelay;
    const skillMultiplier = {
      'beginner': 1.5,
      'intermediate': 1.0,
      'advanced': 0.7
    }[state.learningData.playerSkillLevel];

    return baseDelay * skillMultiplier;
  };

  const contextValue: FeedbackContextType = {
    state,
    dispatch,
    shouldShowFeedback,
    recordFeedbackInteraction,
    updatePlayerSkillAssessment,
    getOptimalFeedbackTiming
  };

  return (
    <FeedbackContext.Provider value={contextValue}>
      {children}
    </FeedbackContext.Provider>
  );
};

export const useFeedback = (): FeedbackContextType => {
  const context = useContext(FeedbackContext);
  if (context === undefined) {
    throw new Error('useFeedback must be used within a FeedbackProvider');
  }
  return context;
};

export default FeedbackContext;

/**
 * Disclosure Rules - Smart Information Revelation Logic (v2.0 Spatial Design)
 *
 * 🎯 CONTEXTUAL INFORMATION RULES
 * 
 * Defines when and how information should be progressively disclosed
 * based on game state, user experience, and contextual relevance.
 * 
 * Rule Categories:
 * - Onboarding: First-time user guidance
 * - Assistance: Help when struggling
 * - Celebration: Success acknowledgments
 * - Warning: Urgent notifications
 * - Information: Contextual insights
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React from 'react';
import { Small, Secondary, Status } from '@/components/ui/Typography';

export interface DisclosureRule {
  id: string;
  condition: (context: any) => boolean;
  priority: number;
  content: React.ReactNode;
  dismissible?: boolean;
  autoHide?: number;
  category: 'tip' | 'warning' | 'success' | 'info' | 'error';
}

export const createDisclosureRules = (): DisclosureRule[] => [
  // === ONBOARDING RULES ===
  {
    id: 'welcome-first-game',
    condition: (ctx) => ctx.gameState.isFirstGame && ctx.gameState.step === 1,
    priority: 100,
    category: 'tip',
    dismissible: true,
    content: (
      <div>
        <Secondary>Welcome to DEFEATER.AI!</Secondary>
        <Small>Define words to reach your targets. Be strategic with your word count!</Small>
      </div>
    )
  },

  {
    id: 'first-definition-tip',
    condition: (ctx) => ctx.gameState.step === 1 && ctx.uiState.isInputFocused && ctx.userState.gamesPlayed < 3,
    priority: 90,
    category: 'tip',
    dismissible: true,
    content: (
      <div>
        <Secondary>PRO TIP</Secondary>
        <Small>Keep definitions concise but complete. Every word counts toward your budget!</Small>
      </div>
    )
  },

  {
    id: 'target-explanation',
    condition: (ctx) => ctx.gameState.step === 2 && ctx.userState.gamesPlayed < 2,
    priority: 85,
    category: 'info',
    dismissible: true,
    content: (
      <div>
        <Secondary>ABOUT TARGETS</Secondary>
        <Small>Target words are revealed progressively. Try to steer your definitions toward them!</Small>
      </div>
    )
  },

  // === ASSISTANCE RULES ===
  {
    id: 'validation-help',
    condition: (ctx) => ctx.gameState.consecutiveFailures >= 2 && ctx.uiState.hasValidationErrors,
    priority: 80,
    category: 'warning',
    dismissible: true,
    content: (
      <div>
        <Secondary>NEED HELP?</Secondary>
        <Small>Check the side panel for detailed validation feedback and tips.</Small>
      </div>
    )
  },

  {
    id: 'word-budget-warning',
    condition: (ctx) => {
      const wordsLeft = ctx.gameState.wordsLeft || 0;
      const maxWords = ctx.gameState.maxWords || 100;
      const percentage = (wordsLeft / maxWords) * 100;
      return percentage <= 25 && percentage > 10;
    },
    priority: 75,
    category: 'warning',
    dismissible: false,
    content: (
      <div>
        <Secondary>WORD BUDGET LOW</Secondary>
        <Small>You're running low on words. Make each definition count!</Small>
      </div>
    )
  },

  {
    id: 'word-budget-critical',
    condition: (ctx) => {
      const wordsLeft = ctx.gameState.wordsLeft || 0;
      const maxWords = ctx.gameState.maxWords || 100;
      const percentage = (wordsLeft / maxWords) * 100;
      return percentage <= 10;
    },
    priority: 95,
    category: 'error',
    dismissible: false,
    content: (
      <div>
        <Secondary>CRITICAL: WORD BUDGET</Secondary>
        <Small>Very few words remaining! Be extremely concise.</Small>
      </div>
    )
  },

  {
    id: 'steps-running-out',
    condition: (ctx) => {
      const remaining = ctx.gameState.maxSteps - ctx.gameState.step;
      return remaining <= 5 && remaining > 2;
    },
    priority: 70,
    category: 'warning',
    dismissible: false,
    content: (
      <div>
        <Secondary>TIME RUNNING OUT</Secondary>
        <Small>Only a few steps left. Focus on your targets!</Small>
      </div>
    )
  },

  {
    id: 'steps-critical',
    condition: (ctx) => {
      const remaining = ctx.gameState.maxSteps - ctx.gameState.step;
      return remaining <= 2;
    },
    priority: 98,
    category: 'error',
    dismissible: false,
    content: (
      <div>
        <Secondary>FINAL STEPS</Secondary>
        <Small>Last chance! Make these definitions count toward your targets.</Small>
      </div>
    )
  },

  // === SUCCESS RULES ===
  {
    id: 'first-target-success',
    condition: (ctx) => ctx.gameState.recentSuccess && ctx.gameState.targetsCompleted === 1,
    priority: 85,
    category: 'success',
    dismissible: true,
    autoHide: 4,
    content: (
      <div>
        <Secondary>🎉 First Target Hit!</Secondary>
        <Small>Excellent! You're getting the hang of this. Keep going!</Small>
      </div>
    )
  },

  {
    id: 'multiple-targets-success',
    condition: (ctx) => ctx.gameState.recentSuccess && ctx.gameState.targetsCompleted >= 2,
    priority: 88,
    category: 'success',
    dismissible: true,
    autoHide: 3,
    content: (
      <div>
        <Secondary>🔥 On Fire!</Secondary>
        <Small>Multiple targets down! You're mastering the game.</Small>
      </div>
    )
  },

  {
    id: 'efficient-play',
    condition: (ctx) => {
      const efficiency = ctx.gameState.targetsCompleted / Math.max(ctx.gameState.step, 1);
      return efficiency >= 0.5 && ctx.gameState.step >= 5;
    },
    priority: 60,
    category: 'success',
    dismissible: true,
    autoHide: 5,
    content: (
      <div>
        <Secondary>⚡ Efficient Play</Secondary>
        <Small>Great target-to-step ratio! You're playing strategically.</Small>
      </div>
    )
  },

  // === INFORMATION RULES ===
  {
    id: 'mid-game-stats',
    condition: (ctx) => ctx.gameState.step === 10 && !ctx.userState.showHints,
    priority: 40,
    category: 'info',
    dismissible: true,
    content: (
      <div>
        <Secondary>📊 Mid-Game Check</Secondary>
        <Small>Check the Stats panel to see your progress and performance metrics.</Small>
      </div>
    )
  },

  {
    id: 'history-reminder',
    condition: (ctx) => ctx.gameState.step >= 8 && ctx.gameState.consecutiveFailures >= 1,
    priority: 50,
    category: 'tip',
    dismissible: true,
    content: (
      <div>
        <Secondary>📝 Review History</Secondary>
        <Small>Check your definition history to see what worked and what didn't.</Small>
      </div>
    )
  },

  {
    id: 'ai-learning-tip',
    condition: (ctx) => ctx.gameState.step >= 6 && ctx.userState.gamesPlayed < 5,
    priority: 45,
    category: 'tip',
    dismissible: true,
    content: (
      <div>
        <Secondary>🤖 AI Insight</Secondary>
        <Small>The AI learns from your definitions. Try to be unpredictable!</Small>
      </div>
    )
  },

  // === LOADING STATE RULES ===
  {
    id: 'ai-thinking',
    condition: (ctx) => ctx.uiState.isLoading,
    priority: 30,
    category: 'info',
    dismissible: false,
    autoHide: 10,
    content: (
      <div>
        <Secondary>🧠 AI Processing</Secondary>
        <Small>The AI is analyzing your definition and choosing the next challenge...</Small>
      </div>
    )
  },

  // === ADVANCED TIPS ===
  {
    id: 'advanced-strategy',
    condition: (ctx) => ctx.userState.gamesPlayed >= 5 && ctx.gameState.step >= 15,
    priority: 35,
    category: 'tip',
    dismissible: true,
    content: (
      <div>
        <Secondary>🎯 Advanced Strategy</Secondary>
        <Small>Try using definitions that could apply to multiple targets simultaneously.</Small>
      </div>
    )
  },

  {
    id: 'paranoia-mode',
    condition: (ctx) => ctx.gameState.step >= 12 && ctx.gameState.targetsCompleted === 0,
    priority: 55,
    category: 'tip',
    dismissible: true,
    content: (
      <div>
        <Secondary>🕵️ Paranoia Mode</Secondary>
        <Small>The AI is getting suspicious. Try more indirect approaches to your targets.</Small>
      </div>
    )
  }
];

// Helper function to evaluate rules with context
export const evaluateDisclosureRules = (
  rules: DisclosureRule[],
  context: any,
  dismissedTips: string[] = []
): DisclosureRule[] => {
  return rules
    .filter(rule => {
      // Skip dismissed tips
      if (dismissedTips.includes(rule.id)) {
        return false;
      }

      // Evaluate condition
      try {
        return rule.condition(context);
      } catch (error) {
        console.warn(`Error evaluating disclosure rule ${rule.id}:`, error);
        return false;
      }
    })
    .sort((a, b) => b.priority - a.priority);
};

// Context builder helper
export const buildDisclosureContext = (
  gameState: any,
  userState: any,
  uiState: any
) => ({
  gameState: {
    step: gameState.step || 0,
    maxSteps: gameState.maxSteps || 25,
    isFirstGame: gameState.isFirstGame || false,
    consecutiveFailures: gameState.consecutiveFailures || 0,
    recentSuccess: gameState.recentSuccess || false,
    targetsCompleted: gameState.completedTargets?.length || 0,
    totalTargets: gameState.targets?.length || 0,
    wordsLeft: gameState.wordsLeft || 0,
    maxWords: gameState.maxWords || 100
  },
  userState: {
    gamesPlayed: userState.gamesPlayed || 0,
    showHints: userState.showHints || false,
    dismissedTips: userState.dismissedTips || []
  },
  uiState: {
    isInputFocused: uiState.isInputFocused || false,
    hasValidationErrors: uiState.hasValidationErrors || false,
    isLoading: uiState.isLoading || false
  }
});

export default createDisclosureRules;

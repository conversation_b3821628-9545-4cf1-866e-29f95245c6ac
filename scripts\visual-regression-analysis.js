#!/usr/bin/env node

/**
 * Visual Regression Analysis Script
 * 
 * Analyzes differences between main app and open-design-test page
 * to identify visual inconsistencies and layout differences
 */

const fs = require('fs');
const path = require('path');

class VisualRegressionAnalyzer {
  constructor() {
    this.results = {
      layoutDifferences: [],
      componentDifferences: [],
      stylingDifferences: [],
      responsiveDifferences: [],
      performanceDifferences: [],
      summary: {}
    };
  }

  async analyzeAll() {
    console.log('🔍 Starting Visual Regression Analysis...\n');
    
    await this.analyzeLayoutStructure();
    await this.analyzeComponentUsage();
    await this.analyzeStylingConsistency();
    await this.analyzeResponsiveBehavior();
    await this.analyzePerformanceImpact();
    
    this.generateSummary();
    this.printResults();
    
    return this.results;
  }

  async analyzeLayoutStructure() {
    console.log('🏗️  Analyzing Layout Structure...');
    
    const mainAppFile = 'pages/index.tsx';
    const testPageFile = 'pages/open-design-test.tsx';
    
    if (!fs.existsSync(mainAppFile) || !fs.existsSync(testPageFile)) {
      this.results.layoutDifferences.push({
        issue: 'Missing comparison files',
        description: 'Cannot compare layout structure - files not found'
      });
      return;
    }
    
    const mainAppContent = fs.readFileSync(mainAppFile, 'utf8');
    const testPageContent = fs.readFileSync(testPageFile, 'utf8');
    
    // Check for GameLayout usage
    const mainAppUsesGameLayout = mainAppContent.includes('GameLayout');
    const testPageUsesGameLayout = testPageContent.includes('GameLayout');
    
    if (mainAppUsesGameLayout && testPageUsesGameLayout) {
      this.results.layoutDifferences.push({
        type: 'layout-consistency',
        status: 'good',
        description: 'Both pages use GameLayout component'
      });
    } else {
      this.results.layoutDifferences.push({
        type: 'layout-inconsistency',
        status: 'warning',
        description: `Layout inconsistency: Main app uses GameLayout: ${mainAppUsesGameLayout}, Test page: ${testPageUsesGameLayout}`
      });
    }
    
    // Check for GameFocus usage
    const mainAppUsesGameFocus = mainAppContent.includes('GameFocus') || 
                                this.checkGameBoardForGameFocus();
    const testPageUsesGameFocus = testPageContent.includes('GameFocus');
    
    if (mainAppUsesGameFocus && testPageUsesGameFocus) {
      this.results.layoutDifferences.push({
        type: 'focus-consistency',
        status: 'good',
        description: 'Both pages use GameFocus for content organization'
      });
    } else {
      this.results.layoutDifferences.push({
        type: 'focus-inconsistency',
        status: 'warning',
        description: `Focus inconsistency: Main app uses GameFocus: ${mainAppUsesGameFocus}, Test page: ${testPageUsesGameFocus}`
      });
    }
    
    console.log(`   ✅ Layout structure analysis complete`);
  }

  checkGameBoardForGameFocus() {
    const gameBoardFile = 'components/GameBoard.tsx';
    if (!fs.existsSync(gameBoardFile)) return false;
    
    const content = fs.readFileSync(gameBoardFile, 'utf8');
    return content.includes('GameFocus');
  }

  async analyzeComponentUsage() {
    console.log('🧩 Analyzing Component Usage...');
    
    const testPageFile = 'pages/open-design-test.tsx';
    const gameBoardFile = 'components/GameBoard.tsx';
    
    if (!fs.existsSync(testPageFile) || !fs.existsSync(gameBoardFile)) {
      this.results.componentDifferences.push({
        issue: 'Missing component files',
        description: 'Cannot compare component usage - files not found'
      });
      return;
    }
    
    const testPageContent = fs.readFileSync(testPageFile, 'utf8');
    const gameBoardContent = fs.readFileSync(gameBoardFile, 'utf8');
    
    const keyComponents = [
      'CurrentChallenge',
      'DefinitionInput',
      'TargetRevelationStrip',
      'CollapsibleSidePanel',
      'GameStatsPanel',
      'DefinitionHistoryPanel'
    ];
    
    keyComponents.forEach(component => {
      const testPageHasComponent = testPageContent.includes(component);
      const mainAppHasComponent = gameBoardContent.includes(component);
      
      if (testPageHasComponent && mainAppHasComponent) {
        this.results.componentDifferences.push({
          component,
          status: 'consistent',
          description: `${component} is used in both pages`
        });
      } else if (testPageHasComponent && !mainAppHasComponent) {
        this.results.componentDifferences.push({
          component,
          status: 'missing-in-main',
          description: `${component} is used in test page but missing in main app`
        });
      } else if (!testPageHasComponent && mainAppHasComponent) {
        this.results.componentDifferences.push({
          component,
          status: 'extra-in-main',
          description: `${component} is used in main app but not in test page`
        });
      } else {
        this.results.componentDifferences.push({
          component,
          status: 'missing-in-both',
          description: `${component} is not used in either page`
        });
      }
    });
    
    console.log(`   ✅ Component usage analysis complete`);
  }

  async analyzeStylingConsistency() {
    console.log('🎨 Analyzing Styling Consistency...');
    
    const designSystemFile = 'styles/design-system.css';
    const globalStylesFile = 'styles/globals.css';
    
    if (!fs.existsSync(designSystemFile)) {
      this.results.stylingDifferences.push({
        issue: 'Missing design system',
        description: 'Design system file not found'
      });
      return;
    }
    
    const designSystemContent = fs.readFileSync(designSystemFile, 'utf8');
    
    // Check for key design system variables
    const keyVariables = [
      '--text-5xl',
      '--text-4xl',
      '--text-3xl',
      '--space-4',
      '--space-6',
      '--space-8',
      '--color-primary',
      '--color-accent',
      '--glass-subtle',
      '--glass-medium',
      '--radius-lg',
      '--shadow-lg',
      '--z-modal',
      '--z-notification'
    ];
    
    keyVariables.forEach(variable => {
      if (designSystemContent.includes(variable)) {
        this.results.stylingDifferences.push({
          variable,
          status: 'defined',
          description: `${variable} is properly defined in design system`
        });
      } else {
        this.results.stylingDifferences.push({
          variable,
          status: 'missing',
          description: `${variable} is missing from design system`
        });
      }
    });
    
    // Check for responsive breakpoints
    const breakpoints = ['--bp-xs', '--bp-sm', '--bp-md', '--bp-lg', '--bp-xl', '--bp-2xl'];
    breakpoints.forEach(breakpoint => {
      if (designSystemContent.includes(breakpoint)) {
        this.results.stylingDifferences.push({
          breakpoint,
          status: 'defined',
          description: `${breakpoint} is properly defined`
        });
      } else {
        this.results.stylingDifferences.push({
          breakpoint,
          status: 'missing',
          description: `${breakpoint} is missing from design system`
        });
      }
    });
    
    console.log(`   ✅ Styling consistency analysis complete`);
  }

  async analyzeResponsiveBehavior() {
    console.log('📱 Analyzing Responsive Behavior...');
    
    const files = this.getAllFiles(['tsx', 'css', 'module.css']);
    const responsivePatterns = [];
    
    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for responsive patterns
      const mediaQueries = content.match(/@media[^{]+{/g) || [];
      const responsiveClasses = content.match(/\b(sm:|md:|lg:|xl:|2xl:)\w+/g) || [];
      const breakpointUsage = content.match(/var\(--bp-[^)]+\)/g) || [];
      
      if (mediaQueries.length > 0 || responsiveClasses.length > 0 || breakpointUsage.length > 0) {
        responsivePatterns.push({
          file: path.relative(process.cwd(), file),
          mediaQueries: mediaQueries.length,
          responsiveClasses: responsiveClasses.length,
          breakpointUsage: breakpointUsage.length
        });
      }
    });
    
    this.results.responsiveDifferences = responsivePatterns;
    
    console.log(`   ✅ Responsive behavior analysis complete`);
  }

  async analyzePerformanceImpact() {
    console.log('⚡ Analyzing Performance Impact...');
    
    const mainAppFile = 'pages/index.tsx';
    const testPageFile = 'pages/open-design-test.tsx';
    const gameBoardFile = 'components/GameBoard.tsx';
    
    const performanceMetrics = {
      mainApp: this.analyzeFileComplexity(mainAppFile),
      testPage: this.analyzeFileComplexity(testPageFile),
      gameBoard: this.analyzeFileComplexity(gameBoardFile)
    };
    
    this.results.performanceDifferences = performanceMetrics;
    
    console.log(`   ✅ Performance impact analysis complete`);
  }

  analyzeFileComplexity(filePath) {
    if (!fs.existsSync(filePath)) {
      return { error: 'File not found' };
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    return {
      lineCount: lines.length,
      componentImports: (content.match(/import.*from.*components/g) || []).length,
      hookUsage: (content.match(/use[A-Z]\w+/g) || []).length,
      stateVariables: (content.match(/useState|useRef|useMemo|useCallback/g) || []).length,
      effectHooks: (content.match(/useEffect/g) || []).length,
      jsxElements: (content.match(/<[A-Z]\w+/g) || []).length
    };
  }

  getAllFiles(extensions) {
    const files = [];
    
    const searchDirs = ['components', 'pages', 'styles'];
    
    const walkDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walkDir(fullPath);
        } else if (stat.isFile()) {
          const ext = path.extname(item).slice(1);
          if (extensions.includes(ext) || 
              (item.endsWith('.module.css') && extensions.includes('module.css'))) {
            files.push(fullPath);
          }
        }
      }
    };
    
    searchDirs.forEach(walkDir);
    return files;
  }

  generateSummary() {
    const layoutIssues = this.results.layoutDifferences.filter(d => d.status === 'warning').length;
    const componentIssues = this.results.componentDifferences.filter(d => 
      d.status === 'missing-in-main' || d.status === 'missing-in-both'
    ).length;
    const stylingIssues = this.results.stylingDifferences.filter(d => d.status === 'missing').length;
    
    this.results.summary = {
      totalIssues: layoutIssues + componentIssues + stylingIssues,
      layoutConsistency: this.results.layoutDifferences.filter(d => d.status === 'good').length,
      componentConsistency: this.results.componentDifferences.filter(d => d.status === 'consistent').length,
      stylingConsistency: this.results.stylingDifferences.filter(d => d.status === 'defined').length,
      responsiveFiles: this.results.responsiveDifferences.length
    };
  }

  printResults() {
    console.log('\n📋 VISUAL REGRESSION ANALYSIS RESULTS\n');
    
    console.log('📊 Summary:');
    console.log(`   Total Issues: ${this.results.summary.totalIssues}`);
    console.log(`   Layout Consistency: ${this.results.summary.layoutConsistency} items`);
    console.log(`   Component Consistency: ${this.results.summary.componentConsistency} components`);
    console.log(`   Styling Consistency: ${this.results.summary.stylingConsistency} variables`);
    console.log(`   Responsive Files: ${this.results.summary.responsiveFiles} files`);
    
    if (this.results.layoutDifferences.some(d => d.status === 'warning')) {
      console.log('\n⚠️  Layout Differences:');
      this.results.layoutDifferences
        .filter(d => d.status === 'warning')
        .forEach(diff => {
          console.log(`   ${diff.type}: ${diff.description}`);
        });
    }
    
    if (this.results.componentDifferences.some(d => d.status !== 'consistent')) {
      console.log('\n🧩 Component Differences:');
      this.results.componentDifferences
        .filter(d => d.status !== 'consistent')
        .forEach(diff => {
          console.log(`   ${diff.component}: ${diff.description}`);
        });
    }
    
    if (this.results.stylingDifferences.some(d => d.status === 'missing')) {
      console.log('\n🎨 Missing Style Variables:');
      this.results.stylingDifferences
        .filter(d => d.status === 'missing')
        .forEach(diff => {
          console.log(`   ${diff.variable || diff.breakpoint}: ${diff.description}`);
        });
    }
    
    if (this.results.summary.totalIssues === 0) {
      console.log('\n✅ Visual regression analysis passed! Main app matches test page design.');
    } else {
      console.log(`\n⚠️  Found ${this.results.summary.totalIssues} visual differences that need attention.`);
    }
    
    console.log('\n📈 Performance Comparison:');
    if (this.results.performanceDifferences.mainApp && this.results.performanceDifferences.testPage) {
      const mainApp = this.results.performanceDifferences.mainApp;
      const testPage = this.results.performanceDifferences.testPage;
      
      console.log(`   Main App: ${mainApp.lineCount} lines, ${mainApp.jsxElements} JSX elements`);
      console.log(`   Test Page: ${testPage.lineCount} lines, ${testPage.jsxElements} JSX elements`);
      
      if (this.results.performanceDifferences.gameBoard) {
        const gameBoard = this.results.performanceDifferences.gameBoard;
        console.log(`   GameBoard: ${gameBoard.lineCount} lines, ${gameBoard.jsxElements} JSX elements`);
      }
    }
  }
}

// Run analysis if called directly
if (require.main === module) {
  const analyzer = new VisualRegressionAnalyzer();
  analyzer.analyzeAll().then(results => {
    process.exit(results.summary.totalIssues > 0 ? 1 : 0);
  }).catch(error => {
    console.error('❌ Analysis failed:', error);
    process.exit(1);
  });
}

module.exports = VisualRegressionAnalyzer;

/**
 * CommonWordTracker Component - Simplified Integration (v2.0 Spatial Design)
 *
 * 🎯 STREAMLINED COMMON WORD MONITORING
 * 
 * Simplified from original CommonWordTracker:
 * - Cleaner, more focused interface
 * - Better integration with spatial design
 * - Imposing typography and clear status indicators
 * - Smart show/hide based on usage
 * - Consolidated view options
 * 
 * Features:
 * - Real-time common word usage tracking
 * - Difficulty-based limit visualization
 * - Warning states for approaching limits
 * - Compact and expanded view modes
 * - Clear exhaustion indicators
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React from 'react';
import { GameState } from '@/types/game';
import { Secondary, Small, Status } from '@/components/ui/Typography';
import { getCommonWordsUsageSummary, isCommonWord } from '@/utils/gameLogic';
import { VALIDATION } from '@/utils/constants';

interface CommonWordTrackerProps {
  gameState: GameState;
  viewMode?: 'compact' | 'expanded' | 'auto';
  showOnlyUsed?: boolean;
  className?: string;
}

export const CommonWordTracker: React.FC<CommonWordTrackerProps> = ({
  gameState,
  viewMode = 'auto',
  showOnlyUsed = false,
  className = ''
}) => {
  const commonWordsUsage = getCommonWordsUsageSummary(
    gameState.commonWordsUsage, 
    gameState.difficulty
  );

  // Determine view mode
  const shouldShowCompact = viewMode === 'compact' || 
    (viewMode === 'auto' && commonWordsUsage.length <= 3);

  // Filter words based on showOnlyUsed
  const displayWords = showOnlyUsed 
    ? commonWordsUsage.filter(usage => usage.used > 0)
    : commonWordsUsage;

  // Don't show if no words to display
  if (displayWords.length === 0) {
    return null;
  }

  // Get difficulty info
  const getDifficultyInfo = () => {
    switch (gameState.difficulty) {
      case 'easy':
        return { label: 'EASY', limit: 'Unlimited', color: 'var(--color-success)' };
      case 'medium':
        return { label: 'MEDIUM', limit: '5 max each', color: 'var(--color-warning)' };
      case 'hard':
        return { label: 'HARD', limit: 'No reuse', color: 'var(--color-error)' };
      default:
        return { label: 'UNKNOWN', limit: 'Unknown', color: 'var(--text-muted)' };
    }
  };

  const difficultyInfo = getDifficultyInfo();

  const trackerClasses = [
    'common-word-tracker',
    shouldShowCompact ? 'common-word-tracker--compact' : 'common-word-tracker--expanded',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={trackerClasses}>
      {/* Header */}
      <div className="tracker-header">
        <Secondary>COMMON WORDS</Secondary>
        <div className="difficulty-info">
          <Small style={{ color: difficultyInfo.color }}>
            {difficultyInfo.label}: {difficultyInfo.limit}
          </Small>
        </div>
      </div>

      {/* Words Display */}
      {shouldShowCompact ? (
        <CompactView words={displayWords} />
      ) : (
        <ExpandedView words={displayWords} />
      )}

      <style jsx>{`
        /* === CORE TRACKER LAYOUT === */
        .common-word-tracker {
          background: var(--glass-medium);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-lg);
          padding: var(--space-4);
          backdrop-filter: blur(8px);
        }

        .common-word-tracker--compact {
          padding: var(--space-3);
        }

        /* === TRACKER HEADER === */
        .tracker-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--space-3);
          padding-bottom: var(--space-2);
          border-bottom: 1px solid var(--glass-border);
        }

        .tracker-header h3 {
          margin: 0;
        }

        .difficulty-info {
          text-align: right;
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .tracker-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--space-1);
          }

          .difficulty-info {
            text-align: left;
          }
        }
      `}</style>
    </div>
  );
};

// Compact View Component
interface CompactViewProps {
  words: Array<{
    word: string;
    used: number;
    limit: number;
    remaining: number;
  }>;
}

const CompactView: React.FC<CompactViewProps> = ({ words }) => {
  return (
    <div className="compact-view">
      {words.map(({ word, used, limit, remaining }) => {
        const getStatusClass = () => {
          if (remaining === 0) return 'word-chip--exhausted';
          if (remaining <= 1 && remaining > 0) return 'word-chip--warning';
          return 'word-chip--safe';
        };

        return (
          <div key={word} className={`word-chip ${getStatusClass()}`}>
            <span className="word-text">{word}</span>
            <span className="word-usage">
              {used}/{limit === -1 ? '∞' : limit}
            </span>
          </div>
        );
      })}

      <style jsx>{`
        .compact-view {
          display: flex;
          flex-wrap: wrap;
          gap: var(--space-2);
        }

        .word-chip {
          display: flex;
          align-items: center;
          gap: var(--space-1);
          padding: var(--space-1) var(--space-2);
          border-radius: var(--radius-base);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          transition: all var(--transition-base);
        }

        .word-chip--safe {
          background: var(--color-success-20);
          color: var(--color-success);
          border: 1px solid var(--color-success-50);
        }

        .word-chip--warning {
          background: var(--color-warning-20);
          color: var(--color-warning);
          border: 1px solid var(--color-warning-50);
        }

        .word-chip--exhausted {
          background: var(--color-error-20);
          color: var(--color-error);
          border: 1px solid var(--color-error-50);
          opacity: 0.7;
        }

        .word-text {
          font-weight: var(--font-semibold);
        }

        .word-usage {
          font-size: var(--text-xs);
          opacity: 0.8;
        }
      `}</style>
    </div>
  );
};

// Expanded View Component
interface ExpandedViewProps {
  words: Array<{
    word: string;
    used: number;
    limit: number;
    remaining: number;
  }>;
}

const ExpandedView: React.FC<ExpandedViewProps> = ({ words }) => {
  return (
    <div className="expanded-view">
      {words.map(({ word, used, limit, remaining }) => {
        const getStatus = () => {
          if (remaining === 0) return { text: 'EXHAUSTED', status: 'error' as const };
          if (remaining <= 1 && remaining > 0) return { text: 'WARNING', status: 'warning' as const };
          return { text: 'AVAILABLE', status: 'success' as const };
        };

        const status = getStatus();

        return (
          <div key={word} className="word-row">
            <div className="word-info">
              <span className="word-name">{word}</span>
              <span className="word-stats">
                Used: {used} | Limit: {limit === -1 ? 'Unlimited' : limit} | Remaining: {remaining === -1 ? '∞' : remaining}
              </span>
            </div>
            <Status status={status.status}>
              {status.text}
            </Status>
          </div>
        );
      })}

      <style jsx>{`
        .expanded-view {
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
        }

        .word-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--space-2);
          background: var(--glass-subtle);
          border-radius: var(--radius-base);
          border: 1px solid var(--glass-border);
        }

        .word-info {
          display: flex;
          flex-direction: column;
          gap: var(--space-1);
        }

        .word-name {
          font-size: var(--text-base);
          font-weight: var(--font-semibold);
          color: var(--text-primary);
        }

        .word-stats {
          font-size: var(--text-sm);
          color: var(--text-muted);
        }

        @media (max-width: 767px) {
          .word-row {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--space-2);
          }
        }
      `}</style>
    </div>
  );
};

export default CommonWordTracker;

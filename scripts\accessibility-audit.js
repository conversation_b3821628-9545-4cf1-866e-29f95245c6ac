/**
 * Accessibility Audit Script
 * 
 * Generates comprehensive accessibility audit reports
 * for DEFEATER.AI application using automated tools.
 */

const puppeteer = require('puppeteer');
const axeCore = require('axe-core');
const fs = require('fs').promises;
const path = require('path');

class AccessibilityAuditor {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      timestamp: new Date().toISOString(),
      url: '',
      wcagLevel: 'AA',
      violations: [],
      passes: [],
      incomplete: [],
      inapplicable: [],
      summary: {
        violationCount: 0,
        passCount: 0,
        incompleteCount: 0,
        score: 0
      },
      recommendations: []
    };
  }

  async initialize() {
    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    this.page = await this.browser.newPage();
    
    // Set viewport for testing
    await this.page.setViewport({ width: 1200, height: 800 });
    
    // Inject axe-core
    await this.page.addScriptTag({
      path: require.resolve('axe-core/axe.min.js')
    });
  }

  async auditPage(url) {
    console.log(`🔍 Auditing: ${url}`);
    
    try {
      await this.page.goto(url, { waitUntil: 'networkidle0' });
      this.results.url = url;

      // Run axe accessibility tests
      const axeResults = await this.page.evaluate(async () => {
        return await axe.run({
          tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
          rules: {
            'color-contrast': { enabled: true },
            'keyboard-navigation': { enabled: true },
            'focus-management': { enabled: true },
            'aria-labels': { enabled: true },
            'semantic-markup': { enabled: true }
          }
        });
      });

      this.processAxeResults(axeResults);
      
      // Additional custom tests
      await this.runCustomTests();
      
      // Generate recommendations
      this.generateRecommendations();
      
      console.log(`✅ Audit completed for: ${url}`);
      return this.results;
      
    } catch (error) {
      console.error(`❌ Audit failed for ${url}:`, error);
      throw error;
    }
  }

  processAxeResults(axeResults) {
    this.results.violations = axeResults.violations.map(violation => ({
      id: violation.id,
      impact: violation.impact,
      description: violation.description,
      help: violation.help,
      helpUrl: violation.helpUrl,
      tags: violation.tags,
      nodes: violation.nodes.map(node => ({
        target: node.target,
        html: node.html,
        failureSummary: node.failureSummary
      }))
    }));

    this.results.passes = axeResults.passes.map(pass => ({
      id: pass.id,
      description: pass.description,
      help: pass.help,
      tags: pass.tags
    }));

    this.results.incomplete = axeResults.incomplete.map(incomplete => ({
      id: incomplete.id,
      description: incomplete.description,
      help: incomplete.help,
      tags: incomplete.tags
    }));

    this.results.inapplicable = axeResults.inapplicable.map(inapplicable => ({
      id: inapplicable.id,
      description: inapplicable.description
    }));

    // Calculate summary
    this.results.summary = {
      violationCount: this.results.violations.length,
      passCount: this.results.passes.length,
      incompleteCount: this.results.incomplete.length,
      score: this.calculateAccessibilityScore()
    };
  }

  calculateAccessibilityScore() {
    const totalTests = this.results.violations.length + this.results.passes.length;
    if (totalTests === 0) return 100;
    
    const passRate = (this.results.passes.length / totalTests) * 100;
    
    // Deduct points for violations based on impact
    let deductions = 0;
    this.results.violations.forEach(violation => {
      switch (violation.impact) {
        case 'critical':
          deductions += 20;
          break;
        case 'serious':
          deductions += 10;
          break;
        case 'moderate':
          deductions += 5;
          break;
        case 'minor':
          deductions += 2;
          break;
      }
    });

    return Math.max(0, Math.round(passRate - deductions));
  }

  async runCustomTests() {
    console.log('🧪 Running custom accessibility tests...');

    // Test keyboard navigation
    await this.testKeyboardNavigation();
    
    // Test screen reader compatibility
    await this.testScreenReaderCompatibility();
    
    // Test color contrast
    await this.testColorContrast();
    
    // Test responsive design
    await this.testResponsiveAccessibility();
    
    // Test performance impact
    await this.testPerformanceImpact();
  }

  async testKeyboardNavigation() {
    try {
      // Test Tab navigation
      const focusableElements = await this.page.evaluate(() => {
        const elements = document.querySelectorAll(
          'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
        );
        return Array.from(elements).map(el => ({
          tagName: el.tagName,
          id: el.id,
          className: el.className,
          tabIndex: el.tabIndex
        }));
      });

      if (focusableElements.length === 0) {
        this.results.violations.push({
          id: 'keyboard-navigation',
          impact: 'serious',
          description: 'No focusable elements found',
          help: 'Ensure interactive elements are keyboard accessible'
        });
      }

      // Test skip links
      const skipLinks = await this.page.evaluate(() => {
        const skipLinks = document.querySelectorAll('[href^="#"]');
        return Array.from(skipLinks).map(link => ({
          text: link.textContent,
          href: link.getAttribute('href')
        }));
      });

      if (skipLinks.length === 0) {
        this.results.violations.push({
          id: 'skip-links',
          impact: 'moderate',
          description: 'No skip links found',
          help: 'Add skip links for keyboard navigation'
        });
      }

    } catch (error) {
      console.error('Keyboard navigation test failed:', error);
    }
  }

  async testScreenReaderCompatibility() {
    try {
      // Test ARIA landmarks
      const landmarks = await this.page.evaluate(() => {
        const landmarks = document.querySelectorAll('[role="main"], [role="navigation"], [role="complementary"], [role="banner"], [role="contentinfo"]');
        return landmarks.length;
      });

      if (landmarks === 0) {
        this.results.violations.push({
          id: 'aria-landmarks',
          impact: 'moderate',
          description: 'No ARIA landmarks found',
          help: 'Add ARIA landmarks for screen reader navigation'
        });
      }

      // Test live regions
      const liveRegions = await this.page.evaluate(() => {
        const liveRegions = document.querySelectorAll('[aria-live]');
        return liveRegions.length;
      });

      if (liveRegions === 0) {
        this.results.violations.push({
          id: 'live-regions',
          impact: 'minor',
          description: 'No live regions found',
          help: 'Add live regions for dynamic content updates'
        });
      }

    } catch (error) {
      console.error('Screen reader compatibility test failed:', error);
    }
  }

  async testColorContrast() {
    try {
      // This would typically use a color contrast analyzer
      // For now, we'll check if high contrast mode is supported
      const highContrastSupport = await this.page.evaluate(() => {
        return window.matchMedia('(prefers-contrast: high)').matches;
      });

      this.results.passes.push({
        id: 'high-contrast-support',
        description: 'High contrast mode detection available',
        help: 'Application can detect high contrast preferences'
      });

    } catch (error) {
      console.error('Color contrast test failed:', error);
    }
  }

  async testResponsiveAccessibility() {
    try {
      // Test mobile viewport
      await this.page.setViewport({ width: 375, height: 667 });
      
      const mobileAccessible = await this.page.evaluate(() => {
        const buttons = document.querySelectorAll('button');
        return Array.from(buttons).every(button => {
          const rect = button.getBoundingClientRect();
          return rect.width >= 44 && rect.height >= 44; // WCAG minimum touch target
        });
      });

      if (!mobileAccessible) {
        this.results.violations.push({
          id: 'touch-targets',
          impact: 'moderate',
          description: 'Touch targets too small on mobile',
          help: 'Ensure touch targets are at least 44x44 pixels'
        });
      }

      // Reset viewport
      await this.page.setViewport({ width: 1200, height: 800 });

    } catch (error) {
      console.error('Responsive accessibility test failed:', error);
    }
  }

  async testPerformanceImpact() {
    try {
      const performanceMetrics = await this.page.evaluate(() => {
        return {
          domElements: document.querySelectorAll('*').length,
          ariaElements: document.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby]').length,
          focusableElements: document.querySelectorAll('a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])').length
        };
      });

      if (performanceMetrics.ariaElements / performanceMetrics.domElements > 0.5) {
        this.results.violations.push({
          id: 'aria-overuse',
          impact: 'minor',
          description: 'Excessive ARIA attributes may impact performance',
          help: 'Review ARIA usage for optimization opportunities'
        });
      }

    } catch (error) {
      console.error('Performance impact test failed:', error);
    }
  }

  generateRecommendations() {
    const recommendations = [];

    // Critical violations
    const criticalViolations = this.results.violations.filter(v => v.impact === 'critical');
    if (criticalViolations.length > 0) {
      recommendations.push({
        priority: 'high',
        category: 'Critical Issues',
        description: `Fix ${criticalViolations.length} critical accessibility violations immediately`,
        actions: criticalViolations.map(v => v.help)
      });
    }

    // Keyboard navigation
    const keyboardIssues = this.results.violations.filter(v => 
      v.id.includes('keyboard') || v.id.includes('focus') || v.id.includes('skip')
    );
    if (keyboardIssues.length > 0) {
      recommendations.push({
        priority: 'high',
        category: 'Keyboard Navigation',
        description: 'Improve keyboard accessibility',
        actions: ['Add skip links', 'Ensure all interactive elements are focusable', 'Implement proper focus management']
      });
    }

    // Screen reader support
    const screenReaderIssues = this.results.violations.filter(v => 
      v.id.includes('aria') || v.id.includes('label') || v.id.includes('landmark')
    );
    if (screenReaderIssues.length > 0) {
      recommendations.push({
        priority: 'medium',
        category: 'Screen Reader Support',
        description: 'Enhance screen reader compatibility',
        actions: ['Add ARIA landmarks', 'Improve ARIA labels', 'Add live regions for dynamic content']
      });
    }

    // Performance optimization
    if (this.results.summary.score < 90) {
      recommendations.push({
        priority: 'medium',
        category: 'Performance',
        description: 'Optimize accessibility features for better performance',
        actions: ['Review ARIA usage', 'Optimize focus management', 'Reduce accessibility overhead']
      });
    }

    this.results.recommendations = recommendations;
  }

  async generateReport() {
    const reportHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEFEATER.AI Accessibility Audit Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; }
        .score { font-size: 2em; font-weight: bold; color: ${this.results.summary.score >= 90 ? '#27ae60' : this.results.summary.score >= 70 ? '#f39c12' : '#e74c3c'}; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .violation { background: #ffebee; border-left: 4px solid #f44336; padding: 10px; margin: 10px 0; }
        .pass { background: #e8f5e8; border-left: 4px solid #4caf50; padding: 10px; margin: 10px 0; }
        .recommendation { background: #fff3e0; border-left: 4px solid #ff9800; padding: 10px; margin: 10px 0; }
        .impact-critical { color: #d32f2f; font-weight: bold; }
        .impact-serious { color: #f57c00; font-weight: bold; }
        .impact-moderate { color: #1976d2; }
        .impact-minor { color: #388e3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 DEFEATER.AI Accessibility Audit Report</h1>
        <p>Generated: ${this.results.timestamp}</p>
        <p>URL: ${this.results.url}</p>
        <p>WCAG Level: ${this.results.wcagLevel}</p>
        <div class="score">Accessibility Score: ${this.results.summary.score}%</div>
    </div>

    <div class="section">
        <h2>📊 Summary</h2>
        <ul>
            <li>Violations: ${this.results.summary.violationCount}</li>
            <li>Passes: ${this.results.summary.passCount}</li>
            <li>Incomplete: ${this.results.summary.incompleteCount}</li>
        </ul>
    </div>

    <div class="section">
        <h2>❌ Violations</h2>
        ${this.results.violations.map(violation => `
            <div class="violation">
                <h3 class="impact-${violation.impact}">${violation.id} (${violation.impact})</h3>
                <p><strong>Description:</strong> ${violation.description}</p>
                <p><strong>Help:</strong> ${violation.help}</p>
                <p><strong>Learn more:</strong> <a href="${violation.helpUrl}" target="_blank">${violation.helpUrl}</a></p>
            </div>
        `).join('')}
    </div>

    <div class="section">
        <h2>✅ Passes</h2>
        ${this.results.passes.slice(0, 10).map(pass => `
            <div class="pass">
                <h3>${pass.id}</h3>
                <p>${pass.description}</p>
            </div>
        `).join('')}
        ${this.results.passes.length > 10 ? `<p>... and ${this.results.passes.length - 10} more</p>` : ''}
    </div>

    <div class="section">
        <h2>💡 Recommendations</h2>
        ${this.results.recommendations.map(rec => `
            <div class="recommendation">
                <h3>${rec.category} (${rec.priority} priority)</h3>
                <p>${rec.description}</p>
                <ul>
                    ${rec.actions.map(action => `<li>${action}</li>`).join('')}
                </ul>
            </div>
        `).join('')}
    </div>
</body>
</html>
    `;

    const reportPath = path.join(__dirname, '..', 'reports', 'accessibility-audit.html');
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    await fs.writeFile(reportPath, reportHtml);
    
    // Also save JSON report
    const jsonPath = path.join(__dirname, '..', 'reports', 'accessibility-audit.json');
    await fs.writeFile(jsonPath, JSON.stringify(this.results, null, 2));

    console.log(`📄 Report saved to: ${reportPath}`);
    console.log(`📄 JSON data saved to: ${jsonPath}`);
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// Main execution
async function runAudit() {
  const auditor = new AccessibilityAuditor();
  
  try {
    await auditor.initialize();
    
    // Audit main pages
    const urls = [
      'http://localhost:3000',
      'http://localhost:3000/game',
      // Add more URLs as needed
    ];

    for (const url of urls) {
      await auditor.auditPage(url);
    }
    
    await auditor.generateReport();
    
  } catch (error) {
    console.error('❌ Audit failed:', error);
    process.exit(1);
  } finally {
    await auditor.cleanup();
  }
}

// Run if called directly
if (require.main === module) {
  runAudit();
}

module.exports = AccessibilityAuditor;

// Game State Types
export interface GameState {
  gameId: string;
  currentWord: string | null;
  targets: string[];
  burnedTargets: string[];
  definitions: Definition[];
  usedWords: string[];
  aiChallengeWords: string[]; // Track words AI has given as challenges
  step: number;
  maxSteps: number;
  gameStatus: GameStatus;
  playerConfidence?: number;
  difficulty: DifficultyLevel;

  // New fields for MVP core mechanics
  consecutiveRejections: number;
  rejectionCount?: number; // For strike system (0-3)
  completedTargets?: string[]; // Successfully reached targets
  commonWordsUsage: Record<string, number>; // Track common word usage
  lastDefinitionLength?: number; // For word count validation
  rejectionHistory: RejectionRecord[]; // Track rejection details for debugging
}

export interface Definition {
  id: string;
  word: string;
  definition: string;
  wordCount: number;
  timestamp: number;
  isValid: boolean;
}

export type GameStatus = 
  | 'waiting'     // Waiting for player input
  | 'processing'  // AI is thinking
  | 'won'         // Player reached a target
  | 'lost'        // Player failed (max steps or impossible)
  | 'error';      // Something went wrong

export type DifficultyLevel = 'easy' | 'medium' | 'hard';

// API Types
export interface GameRequest {
  gameState: GameState;
  playerDefinition?: string;
  action: GameAction;
  difficulty?: DifficultyLevel;
}

export interface GameResponse {
  success: boolean;
  gameState: GameState;
  aiResponse?: AIResponse;
  error?: string;
}

export interface AIResponse {
  accept: boolean;
  reason?: string;
  nextWord?: string;
  burnedTarget?: string;
  gameResult?: 'continue' | 'player_wins' | 'ai_wins';
  resultReason?: string;
  internalAnalysis: {
    playerConfidence: number;
    remainingPaths: string[];
    recommendedBurn: string | null;
    difficultyAdjustment: number;
  };
  // Debug information for developer panel
  debugInfo?: {
    aiRawResponse: string;
    winDetectionMethod: 'ai' | 'code' | 'hybrid' | 'fallback' | 'deterministic';
    winCheckResults: {
      directWin: boolean;
      semanticWin: boolean;
      aiDecision: string; // Free-form debug text
    };
    gameStateAnalysis: {
      remainingTargets: string[];
      burnedTargets: string[];
      currentWord: string;
      step: number;
      isImpossible: boolean;
    };
    timestamp: number;
  };
}

export type GameAction = 
  | 'start'       // Start new game
  | 'submit'      // Submit definition
  | 'reset'       // Reset current game
  | 'hint';       // Get hint (future feature)

// UI State Types
export interface UIState {
  isLoading: boolean;
  showRules: boolean;
  showHistory: boolean;
  animationState: AnimationState;
  inputValue: string;
  wordCount: number;
}

export type AnimationState =
  | 'idle'
  | 'typing'
  | 'thinking'
  | 'success'
  | 'failure'
  | 'shake';

// Player Profile for Feedback System
export interface PlayerProfile {
  id: string;
  gamesPlayed: number;
  averageWordsPerDefinition: number;
  targetCompletionRate?: number;
  preferredDifficulty: DifficultyLevel;
  skillLevel: 'beginner' | 'intermediate' | 'advanced';
  lastPlayedAt: Date;
  totalPlayTime: number;
  achievements: string[];
}

// Game Rules Constants
export const GAME_RULES = {
  MAX_STEPS: 25,
  MIN_TARGETS: 3,
  MAX_TARGETS: 3,
  FIRST_BURN_AT: 5,
  SECOND_BURN_AT: 10,
  SINGLE_WORD_PHASE: 15,
} as const;

// Validation Types
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  wordCount: number;
  usedWords: string[];
  maxAllowedWords?: number; // For better error messages
}

// New validation result for MVP mechanics
export interface TurnValidationResult {
  valid: boolean;
  reason?: string;
  maxAllowed?: number;
  details?: string;
}

// Rejection tracking for three-strike system
export interface RejectionRecord {
  turn: number;
  reason: string;
  definition: string;
  timestamp: number;
  violationType: 'length' | 'reuse' | 'circular' | 'invalid';
}

export interface ValidationError {
  type: 'length' | 'reuse' | 'circular' | 'invalid';
  message: string;
  word?: string;
}

// Enhanced Error Types for Better Error Handling
export interface GameError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: number;
  recoverable: boolean;
}

export type GameErrorCode =
  | 'NETWORK_ERROR'
  | 'AI_TIMEOUT'
  | 'VALIDATION_ERROR'
  | 'GAME_STATE_ERROR'
  | 'INPUT_SANITIZATION_ERROR'
  | 'RATE_LIMIT_ERROR'
  | 'UNKNOWN_ERROR';

// API Error Response Type
export interface APIErrorResponse {
  success: false;
  error: string;
  errorCode?: GameErrorCode;
  gameState?: GameState;
  retryable?: boolean;
}

// DeepSeek API Types
export interface DeepSeekRequest {
  model: string;
  messages: DeepSeekMessage[];
  temperature: number;
  max_tokens: number;
  top_p: number;
  frequency_penalty: number;
  presence_penalty: number;
}

export interface DeepSeekMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface DeepSeekResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: DeepSeekMessage;
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

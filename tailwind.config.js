/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // DEFEATER.AI Dark Theme Color Palette - High Contrast & Sophisticated
        defeater: {
          // Dark backgrounds with depth
          'dark-900': '#0A0A0F',     // Deep space black - main background
          'dark-800': '#12121A',     // Slightly lighter - cards/panels
          'dark-700': '#1A1A26',     // Interactive elements background
          'dark-600': '#252533',     // Hover states
          'dark-500': '#32323F',     // Borders and dividers

          // Accent colors - vibrant and high contrast
          'neon-cyan': '#00F5FF',    // Electric cyan - primary actions
          'neon-purple': '#8B5CF6',  // Vibrant purple - secondary actions
          'neon-green': '#00FF88',   // Success states
          'neon-orange': '#FF8C00',  // Warning/attention
          'neon-red': '#FF3366',     // Error/danger

          // Text colors - optimized for readability
          'text-primary': '#FFFFFF',    // Pure white - headings
          'text-secondary': '#E2E8F0',  // Light gray - body text
          'text-muted': '#94A3B8',      // Muted gray - secondary info
          'text-accent': '#00F5FF',     // Cyan - links and highlights

          // Glass morphism and effects
          'glass-light': 'rgba(255, 255, 255, 0.05)',
          'glass-medium': 'rgba(255, 255, 255, 0.10)',
          'glass-heavy': 'rgba(255, 255, 255, 0.15)',

          // Gradients
          'gradient-start': '#8B5CF6',
          'gradient-end': '#00F5FF',
        },

        // Semantic mappings for easy use
        background: '#0A0A0F',
        surface: '#12121A',
        primary: '#00F5FF',
        secondary: '#8B5CF6',
        success: '#00FF88',
        warning: '#FF8C00',
        error: '#FF3366',
        muted: '#94A3B8',
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      animation: {
        'typewriter': 'typewriter 2s steps(40) 1s 1 normal both',
        'blink': 'blink 1s infinite',
        'shake': 'shake 0.5s ease-in-out',
        'float': 'float 3s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        typewriter: {
          'from': { width: '0' },
          'to': { width: '100%' }
        },
        blink: {
          'from, to': { 'border-color': 'transparent' },
          '50%': { 'border-color': '#7B68EE' }
        },
        shake: {
          '0%, 100%': { transform: 'translateX(0)' },
          '10%, 30%, 50%, 70%, 90%': { transform: 'translateX(-2px)' },
          '20%, 40%, 60%, 80%': { transform: 'translateX(2px)' }
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' }
        }
      },
      boxShadow: {
        // Dark theme shadows - deeper and more dramatic
        'dark-soft': '0 4px 20px -2px rgba(0, 0, 0, 0.4), 0 8px 25px -5px rgba(0, 0, 0, 0.3)',
        'dark-medium': '0 8px 30px -3px rgba(0, 0, 0, 0.5), 0 12px 40px -8px rgba(0, 0, 0, 0.4)',
        'dark-heavy': '0 12px 40px -4px rgba(0, 0, 0, 0.6), 0 20px 60px -12px rgba(0, 0, 0, 0.5)',

        // Neon glows for interactive elements
        'glow-cyan': '0 0 20px rgba(0, 245, 255, 0.4), 0 0 40px rgba(0, 245, 255, 0.2)',
        'glow-purple': '0 0 20px rgba(139, 92, 246, 0.4), 0 0 40px rgba(139, 92, 246, 0.2)',
        'glow-green': '0 0 20px rgba(0, 255, 136, 0.4), 0 0 40px rgba(0, 255, 136, 0.2)',
        'glow-orange': '0 0 20px rgba(255, 140, 0, 0.4), 0 0 40px rgba(255, 140, 0, 0.2)',
        'glow-red': '0 0 20px rgba(255, 51, 102, 0.4), 0 0 40px rgba(255, 51, 102, 0.2)',

        // Subtle inner shadows for depth
        'inset-dark': 'inset 0 2px 4px rgba(0, 0, 0, 0.3)',
        'inset-glow': 'inset 0 1px 2px rgba(255, 255, 255, 0.1)',
      },
      backdropBlur: {
        xs: '2px',
      }
    },
  },
  plugins: [],
}

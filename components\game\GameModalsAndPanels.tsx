/**
 * GameModalsAndPanels Component - Extracted from GameBoard.tsx
 *
 * Handles all modals and panels including:
 * - Collapsible side panel with stats/history/rules tabs
 * - Game rules modal
 * - Developer panel (development only)
 * - Post-game analysis modal
 * - Performance dashboard (development only)
 * - Floating chat dialog
 *
 * This extraction reduces GameBoard.tsx complexity and creates a focused
 * component responsible for all overlay UI elements.
 */

import React from 'react';
import { GameState, DifficultyLevel } from '@/types/game';
import { calculateMaxWords } from '@/utils/gameLogic';

// Panel and Modal Components
import CollapsibleSidePanel, { SidePanelTab } from '@/components/layout/CollapsibleSidePanel';
import GameStatsPanel from '@/components/panels/GameStatsPanel';
import DefinitionHistoryPanel from '@/components/panels/DefinitionHistoryPanel';
import GameRules from '@/components/GameRules';
import DevPanel from '@/components/DevPanel';
import PostGameAnalysis from '@/components/PostGameAnalysis';
import { PerformanceDashboard } from '@/components/dev/PerformanceDashboard';
import FloatingChatDialog from '@/components/chat/FloatingChatDialog';

interface GameModalsAndPanelsProps {
  // Game State
  gameState: GameState | null;
  isGameOver: boolean;
  lastAIResponse: any;

  // UI State
  showSidePanel: boolean;
  sidePanelTab: SidePanelTab;
  showRules: boolean;
  showPostGameAnalysis: boolean;
  wordCount: number;

  // Development State
  showDevPanel: boolean;
  showPerformanceDashboard: boolean;
  isDevelopment: boolean;

  // Event Handlers
  onToggleSidePanel: () => void;
  onUpdateUIState: (updates: any) => void;
  onSetShowDevPanel: (show: boolean) => void;
  onSetShowPerformanceDashboard: (show: boolean) => void;
  onSetShowPostGameAnalysis: (show: boolean) => void;
  onStartNewGame: () => void;
}

export const GameModalsAndPanels: React.FC<GameModalsAndPanelsProps> = ({
  gameState,
  isGameOver,
  lastAIResponse,
  showSidePanel,
  sidePanelTab,
  showRules,
  showPostGameAnalysis,
  wordCount,
  showDevPanel,
  showPerformanceDashboard,
  isDevelopment,
  onToggleSidePanel,
  onUpdateUIState,
  onSetShowDevPanel,
  onSetShowPerformanceDashboard,
  onSetShowPostGameAnalysis,
  onStartNewGame
}) => {
  // Helper function to calculate words left
  const calculateWordsLeft = (): number => {
    if (!gameState) {
      return 15;
    }
    const maxWords = calculateMaxWords(
      gameState.step,
      gameState.difficulty,
      gameState.lastDefinitionLength
    );
    return Math.max(1, maxWords);
  };

  return (
    <>
      {/* Floating Side Panel */}
      {gameState && (
        <CollapsibleSidePanel
          isOpen={showSidePanel}
          onToggle={onToggleSidePanel}
          defaultTab={sidePanelTab}
          tabs={[
            {
              id: 'stats',
              label: 'Stats',
              icon: '📊',
              content: (
                <GameStatsPanel
                  gameState={{
                    step: gameState.step,
                    maxSteps: gameState.maxSteps,
                    targets: gameState.targets,
                    completedTargets: gameState.completedTargets || [],
                    burnedTargets: gameState.burnedTargets,
                    definitions: gameState.definitions,
                    difficulty: gameState.difficulty,
                    wordsLeft: calculateWordsLeft(),
                    maxWords: calculateWordsLeft()
                  }}
                  currentDefinitionLength={wordCount}
                  showDetailed={true}
                />
              )
            },
            {
              id: 'history',
              label: 'History',
              icon: '📜',
              content: (
                <DefinitionHistoryPanel
                  definitions={gameState.definitions.map(def => ({
                    id: def.id || `def-${def.word}-${def.timestamp}`,
                    step: gameState.definitions.indexOf(def) + 1,
                    word: def.word,
                    definition: def.definition,
                    wordCount: def.wordCount,
                    isValid: def.isValid,
                    timestamp: new Date(def.timestamp),
                    validationIssues: def.isValid ? undefined : ['Definition was rejected']
                  }))}
                  isVisible={true}
                  showValidation={true}
                />
              )
            },
            {
              id: 'rules',
              label: 'Rules',
              icon: '📖',
              content: (
                <div className="p-4">
                  <h3 className="text-lg font-bold mb-4">Game Rules</h3>
                  <p className="text-sm text-muted">
                    Define words to reach the targets. Each definition must be shorter than the previous one.
                    The AI will try to predict and burn your targets!
                  </p>
                </div>
              )
            }
          ]}
        />
      )}

      {/* Game Rules Modal */}
      <GameRules
        isOpen={showRules}
        onClose={() => onUpdateUIState({ showRules: false })}
      />

      {/* Developer Panel */}
      {isDevelopment && gameState && (
        <DevPanel
          gameState={gameState}
          lastAIResponse={lastAIResponse}
          isVisible={showDevPanel}
          onToggle={() => onSetShowDevPanel(!showDevPanel)}
        />
      )}

      {/* Post-Game Analysis Modal */}
      {showPostGameAnalysis && isGameOver && gameState && (
        <PostGameAnalysis
          gameState={gameState}
          onNewGame={() => {
            onSetShowPostGameAnalysis(false);
            onStartNewGame();
          }}
          onClose={() => onSetShowPostGameAnalysis(false)}
        />
      )}

      {/* Performance Dashboard (Development Only) */}
      {isDevelopment && (
        <PerformanceDashboard
          isVisible={showPerformanceDashboard}
          onToggle={() => onSetShowPerformanceDashboard(!showPerformanceDashboard)}
        />
      )}

      {/* Floating Chat Dialog - AI Trash Talk */}
      <FloatingChatDialog
        position="bottom-right"
        maxMessages={5}
      />
    </>
  );
};

export default GameModalsAndPanels;
/**
 * GameProgress Component - Unified Progress & Statistics (v2.0 Spatial Design)
 *
 * 🎯 COMPREHENSIVE GAME PROGRESS SYSTEM
 * 
 * Consolidates:
 * - GameStats (performance metrics and analysis)
 * - GameStatus (progress tracking and phase detection)
 * - Progress visualization and risk assessment
 * - Strategic recommendations and insights
 * 
 * Features:
 * - Real-time progress tracking with visual indicators
 * - Performance analysis and efficiency metrics
 * - Phase-based status messages and warnings
 * - Target completion tracking
 * - Strategic insights and recommendations
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React from 'react';
import { GameState, AnimationState } from '@/types/game';
import { Primary, Secondary, Small, Status, Imposing } from '@/components/ui/Typography';
import { 
  getRemainingTargets, 
  getCurrentPhase, 
  getRevealedTargets,
  calculatePlayerConfidence 
} from '@/utils/gameLogic';

interface GameProgressProps {
  gameState: GameState;
  animationState?: AnimationState;
  showDetailed?: boolean;
  compact?: boolean;
  className?: string;
}

interface GameAnalysis {
  efficiency: number;
  strategy: string;
  targetProgress: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  recommendations: string[];
}

export const GameProgress: React.FC<GameProgressProps> = ({
  gameState,
  animationState = 'idle',
  showDetailed = false,
  compact = false,
  className = ''
}) => {
  const remainingTargets = getRemainingTargets(gameState);
  const revealedTargets = getRevealedTargets(gameState);
  const phase = getCurrentPhase(gameState);
  const progressPercentage = Math.round((gameState.step / gameState.maxSteps) * 100);
  const analysis = analyzeGamePerformance(gameState);

  // Status determination
  const isEndgame = phase === 'endgame';
  const isAtRisk = progressPercentage > 80 && remainingTargets.length > 1;
  const isCritical = gameState.step >= gameState.maxSteps - 3;

  const progressClasses = [
    'game-progress',
    compact ? 'game-progress--compact' : '',
    isAtRisk ? 'game-progress--at-risk' : '',
    isCritical ? 'game-progress--critical' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={progressClasses}>
      {/* Progress Header */}
      <div className="progress-header">
        <Primary as="h3">GAME PROGRESS</Primary>
        <div className="progress-stats">
          <Imposing>{gameState.step}</Imposing>
          <span className="progress-separator">/</span>
          <span className="progress-total">{gameState.maxSteps}</span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="progress-bar-container">
        <div className="progress-bar">
          <div 
            className={`progress-fill ${getProgressBarClass(progressPercentage, isAtRisk, isCritical)}`}
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        <Small className="progress-percentage">{progressPercentage}% complete</Small>
      </div>

      {/* Status Message */}
      <div className="status-message">
        <StatusMessage 
          gameState={gameState}
          animationState={animationState}
          phase={phase}
        />
      </div>

      {/* Core Metrics */}
      <div className="metrics-grid">
        <div className="metric-item">
          <Secondary>Targets</Secondary>
          <div className="metric-value">
            <span className="metric-number">{gameState.completedTargets?.length || 0}</span>
            <span className="metric-total">/{gameState.targets?.length || 0}</span>
          </div>
        </div>

        <div className="metric-item">
          <Secondary>Efficiency</Secondary>
          <div className="metric-value">
            <span className={`metric-number ${getEfficiencyClass(analysis.efficiency)}`}>
              {analysis.efficiency}%
            </span>
          </div>
        </div>

        <div className="metric-item">
          <Secondary>Risk Level</Secondary>
          <div className="metric-value">
            <span className={`metric-risk metric-risk--${analysis.riskLevel.toLowerCase()}`}>
              {analysis.riskLevel}
            </span>
          </div>
        </div>
      </div>

      {/* Detailed Analysis */}
      {showDetailed && !compact && (
        <div className="detailed-analysis">
          <div className="analysis-section">
            <Secondary>Strategy Assessment</Secondary>
            <Small>{analysis.strategy}</Small>
          </div>

          {analysis.recommendations.length > 0 && (
            <div className="analysis-section">
              <Secondary>Recommendations</Secondary>
              <div className="recommendations">
                {analysis.recommendations.map((rec, index) => (
                  <Status key={index} status="info">
                    {rec}
                  </Status>
                ))}
              </div>
            </div>
          )}

          {/* Target Progress */}
          <div className="analysis-section">
            <Secondary>Target Progress</Secondary>
            <div className="target-breakdown">
              <div className="target-stat">
                <Small>Revealed: {revealedTargets.length}</Small>
              </div>
              <div className="target-stat">
                <Small>Remaining: {remainingTargets.length}</Small>
              </div>
              <div className="target-stat">
                <Small>Completed: {gameState.completedTargets?.length || 0}</Small>
              </div>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        /* === CORE PROGRESS LAYOUT === */
        .game-progress {
          display: flex;
          flex-direction: column;
          gap: var(--space-4);
          background: var(--glass-medium);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-lg);
          padding: var(--space-6);
          backdrop-filter: blur(12px);
          transition: all var(--transition-base);
        }

        .game-progress--compact {
          padding: var(--space-4);
          gap: var(--space-3);
        }

        .game-progress--at-risk {
          border-color: var(--color-warning-50);
          background: var(--color-warning-10);
        }

        .game-progress--critical {
          border-color: var(--color-error-50);
          background: var(--color-error-10);
        }

        /* === PROGRESS HEADER === */
        .progress-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .progress-header h3 {
          margin: 0;
        }

        .progress-stats {
          display: flex;
          align-items: baseline;
          gap: var(--space-1);
        }

        .progress-separator {
          font-size: var(--text-lg);
          color: var(--text-muted);
        }

        .progress-total {
          font-size: var(--text-lg);
          font-weight: var(--font-semibold);
          color: var(--text-secondary);
        }

        /* === PROGRESS BAR === */
        .progress-bar-container {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
        }

        .progress-bar {
          height: 8px;
          background: var(--bg-tertiary);
          border-radius: var(--radius-full);
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          transition: width var(--transition-base);
          border-radius: var(--radius-full);
        }

        .progress-fill--normal {
          background: linear-gradient(90deg, var(--accent-cyan) 0%, var(--accent-purple) 100%);
        }

        .progress-fill--warning {
          background: linear-gradient(90deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
        }

        .progress-fill--critical {
          background: linear-gradient(90deg, var(--color-error) 0%, var(--color-error-dark) 100%);
        }

        .progress-percentage {
          text-align: center;
          opacity: 0.8;
        }

        /* === STATUS MESSAGE === */
        .status-message {
          text-align: center;
          padding: var(--space-3);
          background: var(--glass-subtle);
          border-radius: var(--radius-base);
        }

        /* === METRICS GRID === */
        .metrics-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: var(--space-4);
        }

        .metric-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--space-1);
          text-align: center;
        }

        .metric-value {
          display: flex;
          align-items: baseline;
          gap: var(--space-1);
        }

        .metric-number {
          font-size: var(--text-xl);
          font-weight: var(--font-bold);
          color: var(--text-primary);
        }

        .metric-total {
          font-size: var(--text-base);
          color: var(--text-muted);
        }

        .metric-risk {
          font-size: var(--text-base);
          font-weight: var(--font-bold);
          padding: var(--space-1) var(--space-2);
          border-radius: var(--radius-base);
        }

        .metric-risk--low {
          color: var(--color-success);
          background: var(--color-success-20);
        }

        .metric-risk--medium {
          color: var(--color-warning);
          background: var(--color-warning-20);
        }

        .metric-risk--high {
          color: var(--color-error);
          background: var(--color-error-20);
        }

        /* === EFFICIENCY CLASSES === */
        .metric-number--excellent {
          color: var(--color-success);
        }

        .metric-number--good {
          color: var(--accent-cyan);
        }

        .metric-number--poor {
          color: var(--color-warning);
        }

        .metric-number--critical {
          color: var(--color-error);
        }

        /* === DETAILED ANALYSIS === */
        .detailed-analysis {
          display: flex;
          flex-direction: column;
          gap: var(--space-4);
          padding-top: var(--space-4);
          border-top: 1px solid var(--glass-border);
        }

        .analysis-section {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
        }

        .recommendations {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
        }

        .target-breakdown {
          display: flex;
          justify-content: space-around;
          gap: var(--space-2);
        }

        .target-stat {
          text-align: center;
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .progress-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--space-2);
          }

          .metrics-grid {
            grid-template-columns: 1fr;
            gap: var(--space-3);
          }

          .target-breakdown {
            flex-direction: column;
            gap: var(--space-2);
          }
        }
      `}</style>
    </div>
  );
};

// Helper function to analyze game performance
function analyzeGamePerformance(gameState: GameState): GameAnalysis {
  const totalWords = gameState.definitions.reduce((sum, def) => sum + def.wordCount, 0);
  const avgWordsPerTurn = gameState.definitions.length > 0 ? totalWords / gameState.definitions.length : 0;
  const efficiency = Math.round((1 / Math.max(avgWordsPerTurn, 1)) * 100);
  
  const targetProgress = gameState.targets?.length > 0 
    ? ((gameState.completedTargets?.length || 0) / gameState.targets.length) * 100 
    : 0;

  let riskLevel: 'Low' | 'Medium' | 'High' = 'Low';
  if (gameState.step > gameState.maxSteps * 0.8) riskLevel = 'High';
  else if (gameState.step > gameState.maxSteps * 0.6) riskLevel = 'Medium';

  const recommendations: string[] = [];
  if (efficiency < 50) recommendations.push('Try shorter, more precise definitions');
  if (targetProgress < 20 && gameState.step > 10) recommendations.push('Focus more on revealed targets');
  if (riskLevel === 'High') recommendations.push('Time is running out - prioritize targets');

  return {
    efficiency,
    strategy: efficiency > 70 ? 'Efficient word usage' : efficiency > 50 ? 'Moderate efficiency' : 'Needs optimization',
    targetProgress,
    riskLevel,
    recommendations
  };
}

// Helper function for progress bar styling
function getProgressBarClass(percentage: number, isAtRisk: boolean, isCritical: boolean): string {
  if (isCritical) return 'progress-fill--critical';
  if (isAtRisk) return 'progress-fill--warning';
  return 'progress-fill--normal';
}

// Helper function for efficiency styling
function getEfficiencyClass(efficiency: number): string {
  if (efficiency >= 80) return 'metric-number--excellent';
  if (efficiency >= 60) return 'metric-number--good';
  if (efficiency >= 40) return 'metric-number--poor';
  return 'metric-number--critical';
}

// Status Message Component
interface StatusMessageProps {
  gameState: GameState;
  animationState: AnimationState;
  phase: string;
}

const StatusMessage: React.FC<StatusMessageProps> = ({ 
  gameState, 
  animationState, 
  phase 
}) => {
  const getStatusMessage = (): { text: string; status: 'info' | 'warning' | 'error' | 'success' } => {
    if (animationState === 'thinking') {
      return {
        text: 'AI ANALYZING DEFINITION...',
        status: 'info'
      };
    }

    if (gameState.gameStatus === 'won') {
      return {
        text: 'VICTORY: YOU DEFEATED THE DEFEATER!',
        status: 'success'
      };
    }

    if (gameState.gameStatus === 'lost') {
      return {
        text: 'DEFEAT: THE AI HAS BESTED YOU',
        status: 'error'
      };
    }

    // Phase-based messages
    switch (phase) {
      case 'opening':
        return {
          text: 'OPENING PHASE: AI IS BEING REASONABLE',
          status: 'info'
        };
      case 'middle':
        return {
          text: 'MIDDLE GAME: SEMANTIC PRESSURE INCREASING',
          status: 'warning'
        };
      case 'endgame':
        return {
          text: 'ENDGAME: NO MERCY - PURE REASONING WARFARE',
          status: 'error'
        };
      default:
        return {
          text: 'READY FOR YOUR NEXT MOVE',
          status: 'info'
        };
    }
  };

  const { text, status } = getStatusMessage();

  return (
    <Status status={status}>
      {text}
    </Status>
  );
};

export default GameProgress;

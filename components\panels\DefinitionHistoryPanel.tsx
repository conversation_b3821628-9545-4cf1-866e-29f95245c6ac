/**
 * DefinitionHistoryPanel Component - Enhanced Definition History (v2.0 Spatial Design)
 *
 * 🎯 COMPREHENSIVE DEFINITION HISTORY
 * 
 * Features:
 * - Chronological list of all definitions with context
 * - Visual indicators for success/failure states
 * - Word count and validation feedback
 * - AI response summaries and reasoning
 * - Expandable details for each definition
 * 
 * Replaces:
 * - DefinitionHistory component (legacy)
 * - ValidationFeedback component (legacy)
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React, { useState } from 'react';
import { Primary, Secondary, Small, Body, Status, Mono } from '@/components/ui/Typography';

interface DefinitionEntry {
  id: string;
  step: number;
  word: string;
  definition: string;
  wordCount: number;
  isValid: boolean;
  aiResponse?: {
    feedback: string;
    reasoning: string;
    nextWord: string;
  };
  timestamp: Date;
  validationIssues?: string[];
}

interface DefinitionHistoryPanelProps {
  definitions: DefinitionEntry[];
  isVisible?: boolean;
  maxEntries?: number;
  showValidation?: boolean;
  className?: string;
}

export const DefinitionHistoryPanel: React.FC<DefinitionHistoryPanelProps> = ({
  definitions,
  isVisible = true,
  maxEntries = 50,
  showValidation = true,
  className = ''
}) => {
  const [expandedEntries, setExpandedEntries] = useState<Set<string>>(new Set());

  // Sort definitions by step (most recent first)
  const sortedDefinitions = [...definitions]
    .sort((a, b) => b.step - a.step)
    .slice(0, maxEntries);

  const toggleExpanded = (entryId: string) => {
    const newExpanded = new Set(expandedEntries);
    if (newExpanded.has(entryId)) {
      newExpanded.delete(entryId);
    } else {
      newExpanded.add(entryId);
    }
    setExpandedEntries(newExpanded);
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (!isVisible || definitions.length === 0) {
    return (
      <div className={`definition-history-panel ${className}`}>
        <div className="empty-state">
          <Secondary>No definitions yet</Secondary>
          <Small>Your definition history will appear here as you play</Small>
        </div>
      </div>
    );
  }

  return (
    <div className={`definition-history-panel ${className}`}>
      {/* Panel Header */}
      <div className="panel-header">
        <Primary as="h3">Definition History</Primary>
        <Small>{definitions.length} definition{definitions.length !== 1 ? 's' : ''}</Small>
      </div>

      {/* History List */}
      <div className="history-list">
        {sortedDefinitions.map((entry) => {
          const isExpanded = expandedEntries.has(entry.id);
          
          return (
            <article key={entry.id} className="history-entry">
              {/* Entry Header */}
              <header className="entry-header">
                <div className="entry-meta">
                  <span className="step-number">Step {entry.step}</span>
                  <span className="entry-time">{formatTimestamp(entry.timestamp)}</span>
                </div>
                
                <div className="entry-status">
                  {entry.isValid ? (
                    <Status status="success">✓ Valid</Status>
                  ) : (
                    <Status status="error">✗ Invalid</Status>
                  )}
                </div>
              </header>

              {/* Word & Definition */}
              <div className="entry-content">
                <div className="word-definition">
                  <div className="target-word">
                    <Mono>"{entry.word}"</Mono>
                  </div>
                  
                  <div className="definition-text">
                    <Body>"{entry.definition}"</Body>
                  </div>
                  
                  <div className="word-count">
                    <Small>
                      {entry.wordCount} word{entry.wordCount !== 1 ? 's' : ''}
                    </Small>
                  </div>
                </div>

                {/* Validation Issues */}
                {!entry.isValid && entry.validationIssues && showValidation && (
                  <div className="validation-issues">
                    {entry.validationIssues.map((issue, index) => (
                      <Status key={index} status="error">
                        {issue}
                      </Status>
                    ))}
                  </div>
                )}

                {/* AI Response Summary */}
                {entry.aiResponse && (
                  <div className="ai-response-summary">
                    <Small className="ai-label">AI Response:</Small>
                    <Body className="ai-feedback">
                      {entry.aiResponse.feedback}
                    </Body>
                  </div>
                )}
              </div>

              {/* Expand/Collapse Button */}
              {entry.aiResponse?.reasoning && (
                <button
                  className="expand-button"
                  onClick={() => toggleExpanded(entry.id)}
                  aria-expanded={isExpanded}
                  aria-label={isExpanded ? 'Hide details' : 'Show details'}
                >
                  <span className="expand-icon">
                    {isExpanded ? '▼' : '▶'}
                  </span>
                  <Small>{isExpanded ? 'Hide' : 'Show'} AI reasoning</Small>
                </button>
              )}

              {/* Expanded Details */}
              {isExpanded && entry.aiResponse && (
                <div className="entry-details">
                  <div className="ai-reasoning">
                    <Secondary as="h4">AI Reasoning</Secondary>
                    <Body>{entry.aiResponse.reasoning}</Body>
                  </div>
                  
                  {entry.aiResponse.nextWord && (
                    <div className="next-word">
                      <Secondary as="h4">Next Challenge</Secondary>
                      <Mono>"{entry.aiResponse.nextWord}"</Mono>
                    </div>
                  )}
                </div>
              )}
            </article>
          );
        })}
      </div>

      <style jsx>{`
        /* === CORE PANEL LAYOUT === */
        .definition-history-panel {
          display: flex;
          flex-direction: column;
          gap: var(--space-4);
          height: 100%;
        }

        .panel-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-bottom: var(--space-3);
          border-bottom: 1px solid var(--glass-border);
        }

        .panel-header h3 {
          margin: 0;
          font-size: var(--text-base);
        }

        /* === EMPTY STATE === */
        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: var(--space-2);
          padding: var(--space-8);
          text-align: center;
          opacity: 0.7;
        }

        /* === HISTORY LIST === */
        .history-list {
          display: flex;
          flex-direction: column;
          gap: var(--space-4);
          overflow-y: auto;
          flex: 1;
        }

        /* === HISTORY ENTRY === */
        .history-entry {
          background: var(--glass-subtle);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-lg);
          padding: var(--space-4);
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
          transition: all var(--transition-base);
        }

        .history-entry:hover {
          background: var(--glass-medium);
          border-color: var(--glass-border-hover);
        }

        /* === ENTRY HEADER === */
        .entry-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .entry-meta {
          display: flex;
          align-items: center;
          gap: var(--space-3);
        }

        .step-number {
          font-size: var(--text-sm);
          font-weight: var(--font-semibold);
          color: var(--accent-cyan);
        }

        .entry-time {
          font-size: var(--text-xs);
          color: var(--text-muted);
          font-family: var(--font-mono);
        }

        /* === ENTRY CONTENT === */
        .entry-content {
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
        }

        .word-definition {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
        }

        .target-word {
          font-size: var(--text-base);
          font-weight: var(--font-semibold);
          color: var(--accent-purple);
        }

        .definition-text {
          padding: var(--space-2) var(--space-3);
          background: var(--bg-secondary);
          border-radius: var(--radius-base);
          border-left: 3px solid var(--glass-border);
        }

        .word-count {
          text-align: right;
          opacity: 0.7;
        }

        /* === VALIDATION ISSUES === */
        .validation-issues {
          display: flex;
          flex-direction: column;
          gap: var(--space-1);
          padding: var(--space-2);
          background: var(--color-error-10);
          border-radius: var(--radius-base);
          border-left: 3px solid var(--color-error);
        }

        /* === AI RESPONSE === */
        .ai-response-summary {
          padding: var(--space-3);
          background: var(--glass-medium);
          border-radius: var(--radius-base);
          border-left: 3px solid var(--accent-cyan);
        }

        .ai-label {
          color: var(--accent-cyan);
          font-weight: var(--font-semibold);
          margin-bottom: var(--space-1);
          display: block;
        }

        .ai-feedback {
          color: var(--text-secondary);
        }

        /* === EXPAND BUTTON === */
        .expand-button {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          padding: var(--space-2);
          background: none;
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-base);
          color: var(--text-muted);
          cursor: pointer;
          transition: all var(--transition-base);
          align-self: flex-start;
        }

        .expand-button:hover {
          background: var(--glass-subtle);
          color: var(--text-secondary);
          border-color: var(--accent-cyan-50);
        }

        .expand-icon {
          font-size: var(--text-xs);
          transition: transform var(--transition-base);
        }

        /* === EXPANDED DETAILS === */
        .entry-details {
          display: flex;
          flex-direction: column;
          gap: var(--space-4);
          padding: var(--space-3);
          background: var(--bg-secondary);
          border-radius: var(--radius-base);
          border: 1px solid var(--glass-border);
        }

        .ai-reasoning h4,
        .next-word h4 {
          margin: 0 0 var(--space-2) 0;
          font-size: var(--text-sm);
          color: var(--accent-cyan);
        }

        .next-word {
          padding: var(--space-2);
          background: var(--glass-subtle);
          border-radius: var(--radius-base);
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .entry-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--space-2);
          }

          .entry-meta {
            width: 100%;
            justify-content: space-between;
          }

          .history-entry {
            padding: var(--space-3);
          }
        }

        /* === ACCESSIBILITY === */
        .expand-button:focus {
          outline: 2px solid var(--accent-cyan);
          outline-offset: 2px;
        }

        @media (prefers-reduced-motion: reduce) {
          .history-entry,
          .expand-button,
          .expand-icon {
            transition: none;
          }
        }
      `}</style>
    </div>
  );
};

export default DefinitionHistoryPanel;

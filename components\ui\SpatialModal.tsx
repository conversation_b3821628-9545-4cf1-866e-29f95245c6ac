/**
 * SpatialModal Component - Spatial Design Modal System (v2.0)
 *
 * 🎯 ACCESSIBLE MODAL WITH SPATIAL DESIGN PATTERNS
 * 
 * Features:
 * - Spatial design system integration
 * - Full accessibility compliance (ARIA, keyboard navigation)
 * - Smooth animations with backdrop blur
 * - Responsive design with mobile optimization
 * - Focus management and escape key handling
 * - Portal rendering for proper z-index stacking
 * 
 * Accessibility:
 * - Focus trap within modal
 * - Escape key to close
 * - ARIA labels and roles
 * - Screen reader announcements
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React, { useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Primary, Secondary } from '@/components/ui/Typography';

interface SpatialModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  variant?: 'default' | 'success' | 'warning' | 'error';
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  children: React.ReactNode;
  className?: string;
}

export const SpatialModal: React.FC<SpatialModalProps> = ({
  isOpen,
  onClose,
  title,
  subtitle,
  size = 'medium',
  variant = 'default',
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  children,
  className = ''
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);

  // Focus management
  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element
      previousFocusRef.current = document.activeElement as HTMLElement;
      
      // Focus the modal
      setTimeout(() => {
        modalRef.current?.focus();
      }, 100);
    } else {
      // Restore focus when modal closes
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    }
  }, [isOpen]);

  // Escape key handler
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && closeOnEscape) {
      onClose();
    }
  }, [onClose, closeOnEscape]);

  // Add/remove escape key listener
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    } else {
      document.removeEventListener('keydown', handleKeyDown);
      // Restore body scroll
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [isOpen, handleKeyDown]);

  // Handle overlay click
  const handleOverlayClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget && closeOnOverlayClick) {
      onClose();
    }
  };

  // Don't render if not open
  if (!isOpen) return null;

  // Generate CSS classes
  const modalClasses = [
    'spatial-modal',
    `spatial-modal--${size}`,
    `spatial-modal--${variant}`,
    className
  ].filter(Boolean).join(' ');

  const modalContent = (
    <div
      className="spatial-modal-overlay"
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby={title ? "modal-title" : undefined}
      aria-describedby={subtitle ? "modal-subtitle" : undefined}
    >
      <div
        ref={modalRef}
        className={modalClasses}
        tabIndex={-1}
        role="document"
      >
        {/* Modal Header */}
        {(title || subtitle || showCloseButton) && (
          <header className="spatial-modal-header">
            <div className="modal-title-section">
              {title && (
                <Primary as="h2" className="modal-title">
                  {title}
                </Primary>
              )}
              {subtitle && (
                <Secondary className="modal-subtitle">
                  {subtitle}
                </Secondary>
              )}
            </div>
            
            {showCloseButton && (
              <button
                onClick={onClose}
                className="modal-close-button"
                aria-label="Close modal"
                type="button"
              >
                ×
              </button>
            )}
          </header>
        )}

        {/* Modal Content */}
        <div className="spatial-modal-content">
          {children}
        </div>
      </div>

      <style jsx>{`
        /* === MODAL OVERLAY === */
        .spatial-modal-overlay {
          position: fixed;
          inset: 0;
          background: rgba(0, 0, 0, 0.6);
          backdrop-filter: blur(8px);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: var(--z-modal, 1000);
          padding: var(--space-4);
          animation: modal-overlay-fade-in 0.3s ease-out;
        }

        @keyframes modal-overlay-fade-in {
          from { 
            opacity: 0;
            backdrop-filter: blur(0px);
          }
          to { 
            opacity: 1;
            backdrop-filter: blur(8px);
          }
        }

        /* === MODAL CONTAINER === */
        .spatial-modal {
          background: var(--bg-primary);
          border-radius: var(--radius-lg);
          box-shadow: var(--shadow-xl);
          border: 1px solid var(--glass-border);
          backdrop-filter: blur(20px);
          display: flex;
          flex-direction: column;
          max-height: 90vh;
          overflow: hidden;
          animation: modal-scale-in 0.3s ease-out;
          outline: none;
        }

        @keyframes modal-scale-in {
          from { 
            opacity: 0;
            transform: scale(0.95) translateY(-20px);
          }
          to { 
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }

        /* === MODAL SIZES === */
        .spatial-modal--small {
          width: 100%;
          max-width: 400px;
        }

        .spatial-modal--medium {
          width: 100%;
          max-width: 600px;
        }

        .spatial-modal--large {
          width: 100%;
          max-width: 800px;
        }

        .spatial-modal--fullscreen {
          width: 95vw;
          height: 95vh;
          max-width: none;
          max-height: none;
        }

        /* === MODAL VARIANTS === */
        .spatial-modal--success .spatial-modal-header {
          background: linear-gradient(135deg, var(--success-50) 0%, var(--success-100) 100%);
          border-bottom-color: var(--success-200);
        }

        .spatial-modal--warning .spatial-modal-header {
          background: linear-gradient(135deg, var(--warning-50) 0%, var(--warning-100) 100%);
          border-bottom-color: var(--warning-200);
        }

        .spatial-modal--error .spatial-modal-header {
          background: linear-gradient(135deg, var(--error-50) 0%, var(--error-100) 100%);
          border-bottom-color: var(--error-200);
        }

        /* === MODAL HEADER === */
        .spatial-modal-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: var(--space-6);
          border-bottom: 1px solid var(--border-subtle);
          background: linear-gradient(135deg, var(--bg-subtle) 0%, var(--bg-muted) 100%);
          gap: var(--space-4);
        }

        .modal-title-section {
          display: flex;
          flex-direction: column;
          gap: var(--space-1);
          flex: 1;
          min-width: 0;
        }

        .modal-title {
          margin: 0;
          color: var(--text-primary);
        }

        .modal-subtitle {
          margin: 0;
          color: var(--text-secondary);
        }

        .modal-close-button {
          background: none;
          border: none;
          font-size: 24px;
          color: var(--text-tertiary);
          cursor: pointer;
          padding: var(--space-2);
          border-radius: var(--radius-md);
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all var(--transition-fast);
          flex-shrink: 0;
        }

        .modal-close-button:hover {
          background: var(--bg-hover);
          color: var(--text-primary);
          transform: scale(1.05);
        }

        .modal-close-button:focus {
          outline: 2px solid var(--focus-ring);
          outline-offset: 2px;
        }

        /* === MODAL CONTENT === */
        .spatial-modal-content {
          padding: var(--space-6);
          overflow-y: auto;
          flex: 1;
          min-height: 0;
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: calc(var(--bp-sm) - 1px)) {
          .spatial-modal-overlay {
            padding: var(--space-2);
          }

          .spatial-modal {
            max-height: 95vh;
          }

          .spatial-modal--large,
          .spatial-modal--medium {
            max-width: none;
            width: 100%;
          }

          .spatial-modal-header {
            padding: var(--space-4);
          }

          .spatial-modal-content {
            padding: var(--space-4);
          }

          .modal-title {
            font-size: 1.25rem;
          }
        }

        /* === ACCESSIBILITY === */
        @media (prefers-reduced-motion: reduce) {
          .spatial-modal-overlay,
          .spatial-modal {
            animation: none;
          }
          
          .modal-close-button {
            transition: none;
          }
        }
      `}</style>
    </div>
  );

  // Render modal in portal
  return typeof window !== 'undefined' 
    ? createPortal(modalContent, document.body)
    : null;
};

/**
 * SpatialModalActions Component - Modal Footer Actions
 */
interface SpatialModalActionsProps {
  children: React.ReactNode;
  align?: 'left' | 'center' | 'right' | 'space-between';
  className?: string;
}

export const SpatialModalActions: React.FC<SpatialModalActionsProps> = ({
  children,
  align = 'right',
  className = ''
}) => {
  const actionsClasses = [
    'spatial-modal-actions',
    `spatial-modal-actions--${align}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <footer className={actionsClasses}>
      {children}

      <style jsx>{`
        .spatial-modal-actions {
          display: flex;
          gap: var(--space-3);
          padding: var(--space-6);
          border-top: 1px solid var(--border-subtle);
          background: var(--bg-subtle);
        }

        .spatial-modal-actions--left {
          justify-content: flex-start;
        }

        .spatial-modal-actions--center {
          justify-content: center;
        }

        .spatial-modal-actions--right {
          justify-content: flex-end;
        }

        .spatial-modal-actions--space-between {
          justify-content: space-between;
        }

        @media (max-width: 640px) {
          .spatial-modal-actions {
            padding: var(--space-4);
            flex-direction: column;
          }

          .spatial-modal-actions--space-between {
            flex-direction: column-reverse;
          }
        }
      `}</style>
    </footer>
  );
};

export default SpatialModal;

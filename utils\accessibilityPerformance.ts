/**
 * Accessibility Performance Optimization
 * 
 * Optimizes performance for users with disabilities and low-end devices
 * while maintaining full accessibility compliance.
 * 
 * Features:
 * - Low-end device detection
 * - Reduced motion preferences
 * - High contrast mode optimization
 * - Screen reader performance optimization
 * - Memory management for assistive technologies
 * - Adaptive rendering based on capabilities
 */

export interface AccessibilityPreferences {
  prefersReducedMotion: boolean;
  prefersHighContrast: boolean;
  prefersReducedData: boolean;
  prefersReducedTransparency: boolean;
  forcedColors: boolean;
  screenReaderActive: boolean;
}

export interface DeviceCapabilities {
  isLowEnd: boolean;
  memoryGB: number;
  connectionType: string;
  hardwareConcurrency: number;
  devicePixelRatio: number;
  supportsWebGL: boolean;
  supportsIntersectionObserver: boolean;
}

export interface PerformanceConfig {
  enableAnimations: boolean;
  enableTransitions: boolean;
  enableBlur: boolean;
  enableShadows: boolean;
  enableGradients: boolean;
  maxConcurrentAnimations: number;
  debounceDelay: number;
  lazyLoadThreshold: number;
}

/**
 * Detect user accessibility preferences
 */
export function detectAccessibilityPreferences(): AccessibilityPreferences {
  if (typeof window === 'undefined') {
    return {
      prefersReducedMotion: false,
      prefersHighContrast: false,
      prefersReducedData: false,
      prefersReducedTransparency: false,
      forcedColors: false,
      screenReaderActive: false
    };
  }

  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
  const prefersReducedData = window.matchMedia('(prefers-reduced-data: reduce)').matches;
  const prefersReducedTransparency = window.matchMedia('(prefers-reduced-transparency: reduce)').matches;
  const forcedColors = window.matchMedia('(forced-colors: active)').matches;
  
  // Detect screen reader (heuristic approach)
  const screenReaderActive = detectScreenReader();

  return {
    prefersReducedMotion,
    prefersHighContrast,
    prefersReducedData,
    prefersReducedTransparency,
    forcedColors,
    screenReaderActive
  };
}

/**
 * Detect screen reader usage (heuristic)
 */
function detectScreenReader(): boolean {
  if (typeof window === 'undefined') {
      return false;
    }

  // Check for common screen reader indicators
  const indicators = [
    // NVDA, JAWS, VoiceOver indicators
    navigator.userAgent.includes('NVDA'),
    navigator.userAgent.includes('JAWS'),
    navigator.userAgent.includes('VoiceOver'),
    // Check for high contrast mode (often used with screen readers)
    window.matchMedia('(prefers-contrast: high)').matches,
    // Check for reduced motion (often used with screen readers)
    window.matchMedia('(prefers-reduced-motion: reduce)').matches,
    // Check for forced colors (Windows high contrast)
    window.matchMedia('(forced-colors: active)').matches
  ];

  return indicators.some(indicator => indicator);
}

/**
 * Detect device capabilities
 */
export function detectDeviceCapabilities(): DeviceCapabilities {
  if (typeof window === 'undefined') {
    return {
      isLowEnd: false,
      memoryGB: 4,
      connectionType: 'unknown',
      hardwareConcurrency: 4,
      devicePixelRatio: 1,
      supportsWebGL: false,
      supportsIntersectionObserver: false
    };
  }

  const nav = navigator as any;
  const memoryGB = nav.deviceMemory || 4;
  const hardwareConcurrency = nav.hardwareConcurrency || 4;
  const connectionType = nav.connection?.effectiveType || 'unknown';
  const devicePixelRatio = window.devicePixelRatio || 1;

  // Detect low-end device
  const isLowEnd = (
    memoryGB <= 2 ||
    hardwareConcurrency <= 2 ||
    connectionType === 'slow-2g' ||
    connectionType === '2g' ||
    devicePixelRatio < 1.5
  );

  // Feature detection
  const supportsWebGL = !!window.WebGLRenderingContext;
  const supportsIntersectionObserver = 'IntersectionObserver' in window;

  return {
    isLowEnd,
    memoryGB,
    connectionType,
    hardwareConcurrency,
    devicePixelRatio,
    supportsWebGL,
    supportsIntersectionObserver
  };
}

/**
 * Generate performance configuration based on capabilities and preferences
 */
export function generatePerformanceConfig(
  preferences: AccessibilityPreferences,
  capabilities: DeviceCapabilities
): PerformanceConfig {
  const baseConfig: PerformanceConfig = {
    enableAnimations: true,
    enableTransitions: true,
    enableBlur: true,
    enableShadows: true,
    enableGradients: true,
    maxConcurrentAnimations: 5,
    debounceDelay: 100,
    lazyLoadThreshold: 0.1
  };

  // Reduce performance for accessibility preferences
  if (preferences.prefersReducedMotion) {
    baseConfig.enableAnimations = false;
    baseConfig.enableTransitions = false;
    baseConfig.maxConcurrentAnimations = 0;
  }

  if (preferences.prefersReducedTransparency) {
    baseConfig.enableBlur = false;
  }

  if (preferences.prefersHighContrast || preferences.forcedColors) {
    baseConfig.enableShadows = false;
    baseConfig.enableGradients = false;
  }

  if (preferences.screenReaderActive) {
    baseConfig.debounceDelay = 200; // Longer delays for screen readers
    baseConfig.lazyLoadThreshold = 0.3; // More aggressive loading
  }

  // Reduce performance for low-end devices
  if (capabilities.isLowEnd) {
    baseConfig.enableBlur = false;
    baseConfig.enableShadows = false;
    baseConfig.maxConcurrentAnimations = Math.min(baseConfig.maxConcurrentAnimations, 2);
    baseConfig.debounceDelay = Math.max(baseConfig.debounceDelay, 200);
    baseConfig.lazyLoadThreshold = 0.5;
  }

  // Further reduce for very low-end devices
  if (capabilities.memoryGB <= 1 || capabilities.hardwareConcurrency <= 1) {
    baseConfig.enableAnimations = false;
    baseConfig.enableTransitions = false;
    baseConfig.enableGradients = false;
    baseConfig.maxConcurrentAnimations = 0;
    baseConfig.debounceDelay = 300;
  }

  return baseConfig;
}

/**
 * CSS class generator for performance optimization
 */
export function generatePerformanceClasses(config: PerformanceConfig): string[] {
  const classes: string[] = [];

  if (!config.enableAnimations) {
    classes.push('no-animations');
  }

  if (!config.enableTransitions) {
    classes.push('no-transitions');
  }

  if (!config.enableBlur) {
    classes.push('no-blur');
  }

  if (!config.enableShadows) {
    classes.push('no-shadows');
  }

  if (!config.enableGradients) {
    classes.push('no-gradients');
  }

  return classes;
}

/**
 * React hook for accessibility performance optimization
 */
export function useAccessibilityPerformance() {
  const [preferences, setPreferences] = React.useState<AccessibilityPreferences | null>(null);
  const [capabilities, setCapabilities] = React.useState<DeviceCapabilities | null>(null);
  const [config, setConfig] = React.useState<PerformanceConfig | null>(null);

  React.useEffect(() => {
    const detectedPreferences = detectAccessibilityPreferences();
    const detectedCapabilities = detectDeviceCapabilities();
    const generatedConfig = generatePerformanceConfig(detectedPreferences, detectedCapabilities);

    setPreferences(detectedPreferences);
    setCapabilities(detectedCapabilities);
    setConfig(generatedConfig);

    // Listen for preference changes
    const mediaQueries = [
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-contrast: high)'),
      window.matchMedia('(prefers-reduced-data: reduce)'),
      window.matchMedia('(prefers-reduced-transparency: reduce)'),
      window.matchMedia('(forced-colors: active)')
    ];

    const handleChange = () => {
      const newPreferences = detectAccessibilityPreferences();
      const newConfig = generatePerformanceConfig(newPreferences, detectedCapabilities);
      setPreferences(newPreferences);
      setConfig(newConfig);
    };

    mediaQueries.forEach(mq => mq.addEventListener('change', handleChange));

    return () => {
      mediaQueries.forEach(mq => mq.removeEventListener('change', handleChange));
    };
  }, []);

  return {
    preferences,
    capabilities,
    config,
    isLoading: !preferences || !capabilities || !config
  };
}

/**
 * Performance monitoring for accessibility
 */
export class AccessibilityPerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  private config: PerformanceConfig;

  constructor(config: PerformanceConfig) {
    this.config = config;
  }

  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
  }

  getAverageMetric(name: string): number {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) {
      return 0;
    }
    
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  shouldOptimize(name: string, threshold: number): boolean {
    return this.getAverageMetric(name) > threshold;
  }

  getReport(): Record<string, any> {
    const report: Record<string, any> = {
      config: this.config,
      metrics: {}
    };

    this.metrics.forEach((values, name) => {
      report.metrics[name] = {
        average: this.getAverageMetric(name),
        latest: values[values.length - 1] || 0,
        count: values.length
      };
    });

    return report;
  }
}

// Import React for the hook
import React from 'react';

#!/usr/bin/env node

/**
 * Accessibility Analysis Script
 * 
 * Analyzes codebase for WCAG 2.1 compliance, accessibility patterns,
 * and potential accessibility issues
 */

const fs = require('fs');
const path = require('path');

class AccessibilityAnalyzer {
  constructor() {
    this.results = {
      ariaUsage: [],
      semanticElements: [],
      focusManagement: [],
      colorContrast: [],
      keyboardNavigation: [],
      touchTargets: [],
      screenReaderSupport: [],
      wcagViolations: [],
      summary: {}
    };
  }

  async analyzeAll() {
    console.log('♿ Starting Accessibility Analysis...\n');
    
    await this.analyzeAriaUsage();
    await this.analyzeSemanticElements();
    await this.analyzeFocusManagement();
    await this.analyzeKeyboardNavigation();
    await this.analyzeTouchTargets();
    await this.analyzeScreenReaderSupport();
    await this.analyzeColorContrast();
    
    this.generateSummary();
    this.printResults();
    
    return this.results;
  }

  async analyzeAriaUsage() {
    console.log('🏷️  Analyzing ARIA Usage...');
    
    const files = this.getAllFiles(['tsx', 'ts']);
    const ariaPatterns = {
      ariaLabel: 0,
      ariaLabelledBy: 0,
      ariaDescribedBy: 0,
      ariaHidden: 0,
      ariaExpanded: 0,
      ariaSelected: 0,
      ariaChecked: 0,
      ariaDisabled: 0,
      role: 0,
      ariaLive: 0
    };
    
    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      // Count ARIA attributes
      ariaPatterns.ariaLabel += (content.match(/aria-label=/g) || []).length;
      ariaPatterns.ariaLabelledBy += (content.match(/aria-labelledby=/g) || []).length;
      ariaPatterns.ariaDescribedBy += (content.match(/aria-describedby=/g) || []).length;
      ariaPatterns.ariaHidden += (content.match(/aria-hidden=/g) || []).length;
      ariaPatterns.ariaExpanded += (content.match(/aria-expanded=/g) || []).length;
      ariaPatterns.ariaSelected += (content.match(/aria-selected=/g) || []).length;
      ariaPatterns.ariaChecked += (content.match(/aria-checked=/g) || []).length;
      ariaPatterns.ariaDisabled += (content.match(/aria-disabled=/g) || []).length;
      ariaPatterns.role += (content.match(/role=/g) || []).length;
      ariaPatterns.ariaLive += (content.match(/aria-live=/g) || []).length;
      
      // Check for missing alt text on images
      const imgMatches = content.match(/<img[^>]*>/g) || [];
      imgMatches.forEach(img => {
        if (!img.includes('alt=') && !img.includes('aria-hidden="true"')) {
          this.results.wcagViolations.push({
            file: path.relative(process.cwd(), file),
            issue: 'Image missing alt text',
            severity: 'high',
            wcagCriterion: '1.1.1 Non-text Content'
          });
        }
      });
    });
    
    this.results.ariaUsage = ariaPatterns;
    
    console.log(`   ✅ ARIA usage analysis complete`);
  }

  async analyzeSemanticElements() {
    console.log('🏗️  Analyzing Semantic Elements...');
    
    const files = this.getAllFiles(['tsx', 'ts']);
    const semanticElements = {
      main: 0,
      nav: 0,
      section: 0,
      article: 0,
      aside: 0,
      header: 0,
      footer: 0,
      h1: 0,
      h2: 0,
      h3: 0,
      h4: 0,
      h5: 0,
      h6: 0,
      button: 0,
      form: 0,
      label: 0
    };
    
    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      Object.keys(semanticElements).forEach(element => {
        const regex = new RegExp(`<${element}[^>]*>`, 'g');
        semanticElements[element] += (content.match(regex) || []).length;
      });
      
      // Check for heading hierarchy issues
      const headings = content.match(/<h[1-6][^>]*>/g) || [];
      if (headings.length > 0) {
        const levels = headings.map(h => parseInt(h.match(/h([1-6])/)[1]));
        
        for (let i = 1; i < levels.length; i++) {
          if (levels[i] - levels[i-1] > 1) {
            this.results.wcagViolations.push({
              file: path.relative(process.cwd(), file),
              issue: `Heading hierarchy skip: h${levels[i-1]} to h${levels[i]}`,
              severity: 'medium',
              wcagCriterion: '1.3.1 Info and Relationships'
            });
          }
        }
      }
    });
    
    this.results.semanticElements = semanticElements;
    
    console.log(`   ✅ Semantic elements analysis complete`);
  }

  async analyzeFocusManagement() {
    console.log('🎯 Analyzing Focus Management...');
    
    const files = this.getAllFiles(['tsx', 'ts']);
    const focusPatterns = {
      useRef: 0,
      focus: 0,
      blur: 0,
      tabIndex: 0,
      autoFocus: 0,
      focusTrap: 0,
      focusVisible: 0
    };
    
    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      focusPatterns.useRef += (content.match(/useRef/g) || []).length;
      focusPatterns.focus += (content.match(/\.focus\(\)/g) || []).length;
      focusPatterns.blur += (content.match(/\.blur\(\)/g) || []).length;
      focusPatterns.tabIndex += (content.match(/tabIndex=/g) || []).length;
      focusPatterns.autoFocus += (content.match(/autoFocus=/g) || []).length;
      focusPatterns.focusTrap += (content.match(/focus.*trap/gi) || []).length;
      focusPatterns.focusVisible += (content.match(/focus-visible/g) || []).length;
      
      // Check for missing focus management in modals
      if (content.includes('Modal') && !content.includes('focus')) {
        this.results.wcagViolations.push({
          file: path.relative(process.cwd(), file),
          issue: 'Modal component may be missing focus management',
          severity: 'medium',
          wcagCriterion: '2.4.3 Focus Order'
        });
      }
    });
    
    this.results.focusManagement = focusPatterns;
    
    console.log(`   ✅ Focus management analysis complete`);
  }

  async analyzeKeyboardNavigation() {
    console.log('⌨️  Analyzing Keyboard Navigation...');
    
    const files = this.getAllFiles(['tsx', 'ts']);
    const keyboardPatterns = {
      onKeyDown: 0,
      onKeyUp: 0,
      onKeyPress: 0,
      enterKey: 0,
      escapeKey: 0,
      arrowKeys: 0,
      tabKey: 0,
      spaceKey: 0
    };
    
    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      keyboardPatterns.onKeyDown += (content.match(/onKeyDown=/g) || []).length;
      keyboardPatterns.onKeyUp += (content.match(/onKeyUp=/g) || []).length;
      keyboardPatterns.onKeyPress += (content.match(/onKeyPress=/g) || []).length;
      keyboardPatterns.enterKey += (content.match(/Enter|key === 13/g) || []).length;
      keyboardPatterns.escapeKey += (content.match(/Escape|key === 27/g) || []).length;
      keyboardPatterns.arrowKeys += (content.match(/Arrow(Up|Down|Left|Right)/g) || []).length;
      keyboardPatterns.tabKey += (content.match(/Tab|key === 9/g) || []).length;
      keyboardPatterns.spaceKey += (content.match(/Space|key === 32/g) || []).length;
      
      // Check for interactive elements without keyboard support
      const interactiveElements = content.match(/<(div|span)[^>]*onClick/g) || [];
      interactiveElements.forEach(element => {
        if (!element.includes('onKeyDown') && !element.includes('role="button"')) {
          this.results.wcagViolations.push({
            file: path.relative(process.cwd(), file),
            issue: 'Interactive element missing keyboard support',
            severity: 'high',
            wcagCriterion: '2.1.1 Keyboard'
          });
        }
      });
    });
    
    this.results.keyboardNavigation = keyboardPatterns;
    
    console.log(`   ✅ Keyboard navigation analysis complete`);
  }

  async analyzeTouchTargets() {
    console.log('👆 Analyzing Touch Targets...');
    
    const cssFiles = this.getAllFiles(['css', 'module.css']);
    const touchTargets = {
      minHeight44px: 0,
      minWidth44px: 0,
      touchTargetVariables: 0,
      buttonSizing: 0
    };
    
    cssFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for WCAG compliant touch targets
      touchTargets.minHeight44px += (content.match(/min-height:\s*44px/g) || []).length;
      touchTargets.minWidth44px += (content.match(/min-width:\s*44px/g) || []).length;
      touchTargets.touchTargetVariables += (content.match(/--touch-target/g) || []).length;
      touchTargets.buttonSizing += (content.match(/button.*height.*44px/g) || []).length;
      
      // Check for potentially small touch targets
      const smallSizes = content.match(/(height|width):\s*(1[0-9]|2[0-9]|3[0-9])px/g) || [];
      if (smallSizes.length > 0) {
        this.results.wcagViolations.push({
          file: path.relative(process.cwd(), file),
          issue: `Potentially small touch targets found: ${smallSizes.length} instances`,
          severity: 'medium',
          wcagCriterion: '2.5.5 Target Size'
        });
      }
    });
    
    this.results.touchTargets = touchTargets;
    
    console.log(`   ✅ Touch targets analysis complete`);
  }

  async analyzeScreenReaderSupport() {
    console.log('🔊 Analyzing Screen Reader Support...');
    
    const files = this.getAllFiles(['tsx', 'ts']);
    const screenReaderPatterns = {
      srOnly: 0,
      visuallyHidden: 0,
      screenReaderText: 0,
      ariaLive: 0,
      ariaAtomic: 0,
      ariaRelevant: 0
    };
    
    files.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      screenReaderPatterns.srOnly += (content.match(/sr-only|screen-reader-only/g) || []).length;
      screenReaderPatterns.visuallyHidden += (content.match(/visually-hidden/g) || []).length;
      screenReaderPatterns.screenReaderText += (content.match(/screen.*reader.*text/gi) || []).length;
      screenReaderPatterns.ariaLive += (content.match(/aria-live=/g) || []).length;
      screenReaderPatterns.ariaAtomic += (content.match(/aria-atomic=/g) || []).length;
      screenReaderPatterns.ariaRelevant += (content.match(/aria-relevant=/g) || []).length;
      
      // Check for form inputs without labels
      const inputs = content.match(/<input[^>]*>/g) || [];
      inputs.forEach(input => {
        if (!input.includes('aria-label') && !input.includes('aria-labelledby') && 
            !input.includes('placeholder') && input.includes('type=')) {
          this.results.wcagViolations.push({
            file: path.relative(process.cwd(), file),
            issue: 'Input element missing accessible label',
            severity: 'high',
            wcagCriterion: '1.3.1 Info and Relationships'
          });
        }
      });
    });
    
    this.results.screenReaderSupport = screenReaderPatterns;
    
    console.log(`   ✅ Screen reader support analysis complete`);
  }

  async analyzeColorContrast() {
    console.log('🎨 Analyzing Color and Contrast...');
    
    const cssFiles = this.getAllFiles(['css', 'module.css']);
    const colorPatterns = {
      colorVariables: 0,
      contrastChecks: 0,
      focusIndicators: 0,
      highContrastSupport: 0
    };
    
    cssFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      colorPatterns.colorVariables += (content.match(/--.*color/g) || []).length;
      colorPatterns.contrastChecks += (content.match(/@media.*prefers-contrast/g) || []).length;
      colorPatterns.focusIndicators += (content.match(/:focus.*outline|:focus.*box-shadow/g) || []).length;
      colorPatterns.highContrastSupport += (content.match(/prefers-contrast.*high/g) || []).length;
      
      // Check for potential contrast issues
      const lowOpacityColors = content.match(/rgba?\([^)]*,\s*0\.[0-4]\)/g) || [];
      if (lowOpacityColors.length > 0) {
        this.results.wcagViolations.push({
          file: path.relative(process.cwd(), file),
          issue: `Low opacity colors may cause contrast issues: ${lowOpacityColors.length} instances`,
          severity: 'medium',
          wcagCriterion: '1.4.3 Contrast (Minimum)'
        });
      }
    });
    
    this.results.colorContrast = colorPatterns;
    
    console.log(`   ✅ Color and contrast analysis complete`);
  }

  getAllFiles(extensions) {
    const files = [];
    
    const searchDirs = ['components', 'pages', 'styles', 'hooks', 'contexts'];
    
    const walkDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walkDir(fullPath);
        } else if (stat.isFile()) {
          const ext = path.extname(item).slice(1);
          if (extensions.includes(ext) || 
              (item.endsWith('.module.css') && extensions.includes('module.css'))) {
            files.push(fullPath);
          }
        }
      }
    };
    
    searchDirs.forEach(walkDir);
    return files;
  }

  generateSummary() {
    const totalViolations = this.results.wcagViolations.length;
    const highSeverityViolations = this.results.wcagViolations.filter(v => v.severity === 'high').length;
    const mediumSeverityViolations = this.results.wcagViolations.filter(v => v.severity === 'medium').length;
    
    this.results.summary = {
      totalViolations,
      highSeverityViolations,
      mediumSeverityViolations,
      ariaAttributeCount: Object.values(this.results.ariaUsage).reduce((a, b) => a + b, 0),
      semanticElementCount: Object.values(this.results.semanticElements).reduce((a, b) => a + b, 0),
      focusManagementCount: Object.values(this.results.focusManagement).reduce((a, b) => a + b, 0),
      keyboardNavigationCount: Object.values(this.results.keyboardNavigation).reduce((a, b) => a + b, 0),
      touchTargetCount: Object.values(this.results.touchTargets).reduce((a, b) => a + b, 0),
      screenReaderSupportCount: Object.values(this.results.screenReaderSupport).reduce((a, b) => a + b, 0)
    };
  }

  printResults() {
    console.log('\n♿ ACCESSIBILITY ANALYSIS RESULTS\n');
    
    console.log('📊 Summary:');
    console.log(`   Total WCAG Violations: ${this.results.summary.totalViolations}`);
    console.log(`   High Severity: ${this.results.summary.highSeverityViolations}`);
    console.log(`   Medium Severity: ${this.results.summary.mediumSeverityViolations}`);
    console.log(`   ARIA Attributes: ${this.results.summary.ariaAttributeCount}`);
    console.log(`   Semantic Elements: ${this.results.summary.semanticElementCount}`);
    console.log(`   Focus Management: ${this.results.summary.focusManagementCount}`);
    console.log(`   Keyboard Navigation: ${this.results.summary.keyboardNavigationCount}`);
    console.log(`   Touch Target Support: ${this.results.summary.touchTargetCount}`);
    console.log(`   Screen Reader Support: ${this.results.summary.screenReaderSupportCount}`);
    
    if (this.results.wcagViolations.length > 0) {
      console.log('\n⚠️  WCAG Violations Found:');
      this.results.wcagViolations.forEach(violation => {
        console.log(`   ${violation.severity.toUpperCase()}: ${violation.issue}`);
        console.log(`      File: ${violation.file}`);
        console.log(`      WCAG: ${violation.wcagCriterion}`);
      });
    }
    
    console.log('\n🏷️  ARIA Usage:');
    Object.entries(this.results.ariaUsage).forEach(([attribute, count]) => {
      console.log(`   ${attribute}: ${count} usages`);
    });
    
    console.log('\n🏗️  Semantic Elements:');
    Object.entries(this.results.semanticElements).forEach(([element, count]) => {
      if (count > 0) {
        console.log(`   <${element}>: ${count} usages`);
      }
    });
    
    console.log('\n⌨️  Keyboard Navigation:');
    Object.entries(this.results.keyboardNavigation).forEach(([pattern, count]) => {
      if (count > 0) {
        console.log(`   ${pattern}: ${count} usages`);
      }
    });
    
    if (this.results.summary.totalViolations === 0) {
      console.log('\n✅ Excellent accessibility! No WCAG violations detected.');
    } else {
      console.log(`\n⚠️  Found ${this.results.summary.totalViolations} accessibility issues that need attention.`);
    }
  }
}

// Run analysis if called directly
if (require.main === module) {
  const analyzer = new AccessibilityAnalyzer();
  analyzer.analyzeAll().then(results => {
    process.exit(results.summary.highSeverityViolations > 0 ? 1 : 0);
  }).catch(error => {
    console.error('❌ Analysis failed:', error);
    process.exit(1);
  });
}

module.exports = AccessibilityAnalyzer;

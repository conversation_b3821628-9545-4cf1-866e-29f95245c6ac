import React, { useState, useEffect } from 'react';
import { performanceMonitor, PerformanceMetrics, GamePerformanceMetrics } from '@/utils/performance';

interface PerformanceDashboardProps {
  isVisible: boolean;
  onToggle: () => void;
}

export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  isVisible,
  onToggle
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [gameMetrics, setGameMetrics] = useState<GamePerformanceMetrics | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isVisible) {
      // Initial load
      updateMetrics();

      // Set up refresh interval
      const interval = setInterval(updateMetrics, 1000);
      setRefreshInterval(interval);

      return () => {
        if (interval) {
          clearInterval(interval);
        }
      };
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }
  }, [isVisible, refreshInterval]);

  const updateMetrics = () => {
    setMetrics(performanceMonitor.getMetrics());
    setGameMetrics(performanceMonitor.getGameMetrics());
  };

  const getScoreColor = (score: number): string => {
    if (score >= 90) {
      return 'text-green-400';
    }
    if (score >= 70) {
      return 'text-yellow-400';
    }
    if (score >= 50) {
      return 'text-orange-400';
    }
    return 'text-red-400';
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) {
      return '0 B';
    }
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number): string => {
    if (ms < 1000) {
      return `${Math.round(ms)}ms`;
    }
    return `${(ms / 1000).toFixed(2)}s`;
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm z-50 hover:bg-gray-700 transition-colors"
        title="Show Performance Dashboard"
      >
        📊 Perf
      </button>
    );
  }

  const performanceScore = performanceMonitor.getPerformanceScore();

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-xl z-50 w-80 max-h-96 overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">Performance Dashboard</h3>
        <button
          onClick={onToggle}
          className="text-gray-400 hover:text-white transition-colors"
          title="Hide Performance Dashboard"
        >
          ✕
        </button>
      </div>

      {/* Performance Score */}
      <div className="mb-4 p-3 bg-gray-800 rounded">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Performance Score</span>
          <span className={`text-lg font-bold ${getScoreColor(performanceScore)}`}>
            {performanceScore}/100
          </span>
        </div>
      </div>

      {/* Core Web Vitals */}
      {metrics && (
        <div className="mb-4">
          <h4 className="text-sm font-semibold mb-2 text-gray-300">Core Web Vitals</h4>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span>First Contentful Paint</span>
              <span className={metrics.firstContentfulPaint > 2500 ? 'text-red-400' : 'text-green-400'}>
                {formatTime(metrics.firstContentfulPaint)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Largest Contentful Paint</span>
              <span className={metrics.largestContentfulPaint > 4000 ? 'text-red-400' : 'text-green-400'}>
                {formatTime(metrics.largestContentfulPaint)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>First Input Delay</span>
              <span className={metrics.firstInputDelay > 300 ? 'text-red-400' : 'text-green-400'}>
                {formatTime(metrics.firstInputDelay)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Cumulative Layout Shift</span>
              <span className={metrics.cumulativeLayoutShift > 0.25 ? 'text-red-400' : 'text-green-400'}>
                {metrics.cumulativeLayoutShift.toFixed(3)}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Game Performance */}
      {gameMetrics && (
        <div className="mb-4">
          <h4 className="text-sm font-semibold mb-2 text-gray-300">Game Performance</h4>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span>API Response Time</span>
              <span className={gameMetrics.apiResponseTime > 5000 ? 'text-red-400' : 'text-green-400'}>
                {formatTime(gameMetrics.apiResponseTime)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Game State Size</span>
              <span className={gameMetrics.gameStateSize > 10000 ? 'text-yellow-400' : 'text-green-400'}>
                {formatBytes(gameMetrics.gameStateSize)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Render Time</span>
              <span className={gameMetrics.renderTime > 16 ? 'text-red-400' : 'text-green-400'}>
                {formatTime(gameMetrics.renderTime)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Animation FPS</span>
              <span className={gameMetrics.animationFrameRate < 30 ? 'text-red-400' : 'text-green-400'}>
                {Math.round(gameMetrics.animationFrameRate)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Component Renders</span>
              <span className={gameMetrics.componentRenderCount > 100 ? 'text-yellow-400' : 'text-green-400'}>
                {gameMetrics.componentRenderCount}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Memory Usage */}
      {metrics?.memoryUsage && (
        <div className="mb-4">
          <h4 className="text-sm font-semibold mb-2 text-gray-300">Memory Usage</h4>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span>Used Heap</span>
              <span>{formatBytes(metrics.memoryUsage.usedJSHeapSize)}</span>
            </div>
            <div className="flex justify-between">
              <span>Total Heap</span>
              <span>{formatBytes(metrics.memoryUsage.totalJSHeapSize)}</span>
            </div>
            <div className="flex justify-between">
              <span>Heap Limit</span>
              <span>{formatBytes(metrics.memoryUsage.jsHeapSizeLimit)}</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
              <div
                className={`h-2 rounded-full ${
                  (metrics.memoryUsage.usedJSHeapSize / metrics.memoryUsage.totalJSHeapSize) > 0.8
                    ? 'bg-red-400'
                    : 'bg-green-400'
                }`}
                style={{
                  width: `${(metrics.memoryUsage.usedJSHeapSize / metrics.memoryUsage.totalJSHeapSize) * 100}%`
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Memory Leak Warning */}
      {gameMetrics?.memoryLeaks && (
        <div className="mb-4 p-2 bg-red-900 border border-red-600 rounded">
          <div className="text-red-200 text-xs">
            ⚠️ Potential memory leak detected
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex gap-2">
        <button
          onClick={() => {
            if (typeof window !== 'undefined' && 'gc' in window) {
              (window as any).gc();
            }
          }}
          className="flex-1 bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs transition-colors"
          title="Force garbage collection (if available)"
        >
          🗑️ GC
        </button>
        <button
          onClick={updateMetrics}
          className="flex-1 bg-green-600 hover:bg-green-700 px-2 py-1 rounded text-xs transition-colors"
          title="Refresh metrics"
        >
          🔄 Refresh
        </button>
      </div>

      <style jsx>{`
        /* Custom scrollbar for dashboard */
        .overflow-y-auto::-webkit-scrollbar {
          width: 4px;
        }
        .overflow-y-auto::-webkit-scrollbar-track {
          background: #374151;
        }
        .overflow-y-auto::-webkit-scrollbar-thumb {
          background: #6b7280;
          border-radius: 2px;
        }
        .overflow-y-auto::-webkit-scrollbar-thumb:hover {
          background: #9ca3af;
        }
      `}</style>
    </div>
  );
};

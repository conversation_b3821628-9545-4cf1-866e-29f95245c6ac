/**
 * GameInput Component - Definition Input and Submission (v2.0)
 *
 * 🎯 COMPREHENSIVE INPUT MANAGEMENT COMPONENT
 * 
 * Features:
 * - Definition input with real-time validation
 * - Word count tracking and display
 * - Submit button with loading states
 * - Accessibility-first design with proper ARIA structure
 * - Performance optimized with debounced input
 * 
 * Input Features:
 * - Real-time word counting
 * - Input validation and feedback
 * - Keyboard shortcuts (Enter to submit)
 * - Loading states and animations
 * 
 * Performance Benefits:
 * - Memoized props to prevent unnecessary re-renders
 * - Debounced input handling
 * - Optimized event handlers
 * - Clean separation of concerns
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_5-6_COMPONENT_DECOMPOSITION.md
 */

import React, { memo } from 'react';
import DefinitionInput from '@/components/game/DefinitionInput';

interface GameInputProps {
  // Input State
  inputValue: string;
  wordCount: number;
  isValid: boolean;
  
  // UI State
  isLoading: boolean;
  animationState?: 'idle' | 'thinking' | 'shake' | 'success' | 'failure';
  
  // Game State
  currentWord: string;
  step: number;
  maxSteps: number;
  
  // Event Handlers
  onInputChange: (value: string) => void;
  onSubmit: (definition: string) => void;
  
  // Configuration
  placeholder?: string;
  maxLength?: number;
  showWordCount?: boolean;
  showProgress?: boolean;
  disabled?: boolean;
  
  // Layout
  className?: string;
}

const GameInput: React.FC<GameInputProps> = memo(({
  inputValue,
  wordCount,
  isValid,
  isLoading,
  animationState = 'idle',
  currentWord,
  step,
  maxSteps,
  onInputChange,
  onSubmit,
  placeholder = "Enter your definition...",
  maxLength = 500,
  showWordCount = true,
  showProgress = true,
  disabled = false,
  className = ''
}) => {
  const inputClasses = [
    'game-input',
    isLoading ? 'game-input--loading' : '',
    disabled ? 'game-input--disabled' : '',
    className
  ].filter(Boolean).join(' ');

  const handleSubmit = () => {
    if (!isLoading && !disabled && isValid && inputValue.trim()) {
      onSubmit(inputValue);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit();
    }
  };

  return (
    <section 
      className={inputClasses}
      role="form"
      aria-label="Definition input form"
    >
      {/* Input Instructions */}
      <div className="input-instructions">
        <p className="instruction-text">
          Define "{currentWord}" in your own words
        </p>
        {showProgress && (
          <p className="progress-text">
            Step {step} of {maxSteps}
          </p>
        )}
      </div>

      {/* Definition Input Component */}
      <div className="input-container">
        <DefinitionInput
          value={inputValue}
          onChange={onInputChange}
          onSubmit={handleSubmit}
          placeholder={placeholder}
          isLoading={isLoading}
          animationState={
            animationState === 'thinking' ? 'pulse' :
            animationState === 'failure' ? 'shake' :
            animationState === 'success' ? 'success' :
            'idle'
          }
        />
      </div>

      {/* Submit Button */}
      <div className="submit-container">
        <button
          onClick={handleSubmit}
          disabled={!isValid || isLoading || disabled}
          className={`btn-primary ${isLoading ? 'btn-loading' : ''}`}
          aria-label={isLoading ? 'Submitting definition...' : 'Submit definition'}
        >
          {isLoading ? (
            <>
              <span className="loading-spinner" aria-hidden="true"></span>
              Thinking...
            </>
          ) : (
            'Submit Definition'
          )}
        </button>
      </div>

      <style jsx>{`
        /* === CORE INPUT LAYOUT === */
        .game-input {
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: var(--space-8);
          padding: var(--space-4) 0;
          position: relative;
        }

        .game-input--loading {
          opacity: 0.9;
        }

        .game-input--disabled {
          opacity: 0.6;
          pointer-events: none;
        }

        /* === INPUT INSTRUCTIONS === */
        .input-instructions {
          text-align: center;
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
        }

        .instruction-text {
          font-size: var(--text-lg);
          font-weight: var(--font-medium);
          color: var(--text-primary);
          margin: 0;
        }

        .progress-text {
          font-size: var(--text-sm);
          color: var(--text-muted);
          margin: 0;
        }

        /* === INPUT CONTAINER === */
        .input-container {
          flex: 1;
          width: 100%;
        }

        /* === SUBMIT CONTAINER === */
        .submit-container {
          display: flex;
          justify-content: center;
          width: 100%;
        }

        /* === LOADING SPINNER === */
        .loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid transparent;
          border-top: 2px solid currentColor;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-right: var(--space-2);
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .game-input {
            gap: var(--space-6);
            padding: var(--space-6) var(--space-4);
          }

          .instruction-text {
            font-size: var(--text-base);
          }
        }

        @media (min-width: 1024px) {
          .game-input {
            gap: var(--space-8);
            padding: var(--space-8) var(--space-6);
          }

          .instruction-text {
            font-size: var(--text-xl);
          }
        }

        /* === ACCESSIBILITY === */
        @media (prefers-reduced-motion: reduce) {
          .loading-spinner {
            animation: none;
          }
        }
      `}</style>
    </section>
  );
});

GameInput.displayName = 'GameInput';

export default GameInput;

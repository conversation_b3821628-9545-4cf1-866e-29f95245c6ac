/**
 * CurrentChallenge Component - Hero Word Display (v2.0 Spatial Design)
 *
 * 🎯 CENTER-STAGE HERO ELEMENT
 * 
 * Features:
 * - Hero typography (3rem+) for maximum visual impact
 * - Gradient text effects with high contrast
 * - Animated word transitions and state changes
 * - Progressive revelation hints and context
 * - Accessibility-first with proper semantic markup
 * 
 * Visual Hierarchy:
 * - Primary: Current word (hero size)
 * - Secondary: Challenge instruction (large)
 * - Tertiary: Progress context (medium)
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React, { useEffect, useState } from 'react';
import { Hero, Primary, Secondary } from '@/components/ui/Typography';
import { useComponentAnimation } from '@/hooks/useGameAnimations';

interface CurrentChallengeProps {
  word: string;
  step: number;
  maxSteps: number;
  isLoading?: boolean;
  animationState?: 'idle' | 'entering' | 'success' | 'error';
  showProgress?: boolean;
  className?: string;
}

export const CurrentChallenge: React.FC<CurrentChallengeProps> = ({
  word,
  step,
  maxSteps,
  isLoading = false,
  animationState = 'idle',
  showProgress = true,
  className = ''
}) => {
  const [previousWord, setPreviousWord] = useState(word);
  const [isWordChanging, setIsWordChanging] = useState(false);

  const componentAnimation = useComponentAnimation('current-challenge');

  // Detect word changes and trigger animations
  useEffect(() => {
    if (previousWord !== word && previousWord !== '') {
      setIsWordChanging(true);
      componentAnimation.triggerComponentAnimation('update', {
        oldWord: previousWord,
        newWord: word
      });

      // Reset word changing state after animation
      const timeout = setTimeout(() => {
        setIsWordChanging(false);
        setPreviousWord(word);
      }, 400);

      return () => clearTimeout(timeout);
    } else {
      setPreviousWord(word);
    }
  }, [word, previousWord, componentAnimation]);

  const challengeClasses = [
    'current-challenge',
    `current-challenge--${animationState}`,
    isLoading ? 'current-challenge--loading' : '',
    isWordChanging ? 'current-challenge--word-changing' : '',
    className
  ].filter(Boolean).join(' ');

  const progressPercentage = Math.round((step / maxSteps) * 100);

  return (
    <section className={challengeClasses} role="banner" aria-label="Current challenge">
      {/* Challenge Instruction */}
      <div className="challenge-instruction">
        <Primary as="h2">Define this word:</Primary>
      </div>

      {/* Hero Word Display */}
      <div className="challenge-word-container">
        <Hero as="h1" className="challenge-word">
          "{word}"
        </Hero>
        
        {/* Loading overlay */}
        {isLoading && (
          <div className="challenge-loading" aria-label="Loading new word">
            <div className="loading-pulse"></div>
          </div>
        )}
      </div>

      {/* Progress Context */}
      {showProgress && (
        <div className="challenge-progress">
          <Secondary as="p">
            Step {step} of {maxSteps} • {progressPercentage}% complete
          </Secondary>
        </div>
      )}

      <style jsx>{`
        /* === CORE CHALLENGE LAYOUT === */
        .current-challenge {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          gap: var(--space-6);
          position: relative;
          padding: var(--space-8) var(--space-4);
        }

        /* === CHALLENGE INSTRUCTION === */
        .challenge-instruction {
          opacity: 0.9;
          margin-bottom: var(--space-2);
        }

        /* === HERO WORD CONTAINER === */
        .challenge-word-container {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 120px;
          width: 100%;
        }

        /* === HERO WORD STYLING === */
        .challenge-word {
          font-size: 3rem !important;
          font-weight: var(--font-bold) !important;
          line-height: var(--leading-tight) !important;
          background: linear-gradient(
            135deg,
            var(--accent-cyan) 0%,
            var(--accent-purple) 50%,
            var(--accent-pink) 100%
          ) !important;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent !important;
          background-clip: text !important;
          text-shadow: none !important;
          letter-spacing: -0.02em !important;
          margin: 0 !important;
          transition: all var(--transition-base);
          transform-origin: center;
        }

        /* === ANIMATION STATES === */
        .current-challenge--entering .challenge-word {
          animation: wordEnter 0.6s ease-out;
        }

        .current-challenge--success .challenge-word {
          animation: wordSuccess 0.8s ease-out;
        }

        .current-challenge--error .challenge-word {
          animation: wordError 0.5s ease-out;
        }

        .current-challenge--word-changing .challenge-word {
          animation: wordChange 0.4s ease-in-out;
        }

        /* === LOADING STATE === */
        .challenge-loading {
          position: absolute;
          inset: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: var(--bg-primary-80);
          backdrop-filter: blur(4px);
          border-radius: var(--radius-lg);
        }

        .loading-pulse {
          width: 60px;
          height: 60px;
          border: 3px solid var(--accent-cyan-30);
          border-top: 3px solid var(--accent-cyan);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        /* === PROGRESS CONTEXT === */
        .challenge-progress {
          opacity: 0.8;
          margin-top: var(--space-2);
        }

        /* === ANIMATIONS === */
        @keyframes wordEnter {
          0% {
            opacity: 0;
            transform: scale(0.8) translateY(20px);
          }
          100% {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }

        @keyframes wordSuccess {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.05);
          }
        }

        @keyframes wordError {
          0%, 100% {
            transform: translateX(0);
          }
          25% {
            transform: translateX(-10px);
          }
          75% {
            transform: translateX(10px);
          }
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes wordChange {
          0% {
            transform: scale(1) rotateY(0deg);
            opacity: 1;
          }
          50% {
            transform: scale(0.8) rotateY(90deg);
            opacity: 0.3;
          }
          100% {
            transform: scale(1) rotateY(0deg);
            opacity: 1;
          }
        }

        /* === RESPONSIVE HERO TYPOGRAPHY === */
        
        /* Mobile: 2.5rem */
        @media (max-width: 767px) {
          .challenge-word {
            font-size: 2.5rem !important;
          }
          
          .challenge-word-container {
            min-height: 100px;
          }
          
          .current-challenge {
            gap: var(--space-4);
            padding: var(--space-6) var(--space-3);
          }
        }

        /* Tablet: 3.5rem */
        @media (min-width: 768px) and (max-width: 1023px) {
          .challenge-word {
            font-size: 3.5rem !important;
          }
          
          .challenge-word-container {
            min-height: 140px;
          }
        }

        /* Desktop: 4rem */
        @media (min-width: 1024px) {
          .challenge-word {
            font-size: 4rem !important;
          }
          
          .challenge-word-container {
            min-height: 160px;
          }
          
          .current-challenge {
            gap: var(--space-8);
            padding: var(--space-12) var(--space-6);
          }
        }

        /* Large Desktop: 5rem */
        @media (min-width: var(--bp-2xl)) {
          .challenge-word {
            font-size: 5rem !important;
          }
          
          .challenge-word-container {
            min-height: 200px;
          }
        }

        /* === ACCESSIBILITY === */
        @media (prefers-reduced-motion: reduce) {
          .challenge-word,
          .loading-pulse {
            animation: none !important;
            transition: none !important;
          }
        }

        /* High contrast mode */
        @media (prefers-contrast: high) {
          .challenge-word {
            background: var(--text-primary) !important;
            -webkit-background-clip: unset !important;
            -webkit-text-fill-color: unset !important;
            background-clip: unset !important;
            color: var(--text-primary) !important;
          }
        }
      `}</style>
    </section>
  );
};

export default CurrentChallenge;

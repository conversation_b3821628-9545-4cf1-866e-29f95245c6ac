/**
 * Performance monitoring and optimization utilities for DEFEATER.AI
 */

export interface PerformanceMetrics {
  loadTime: number;
  domContentLoaded: number;
  firstPaint: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;
  memoryUsage?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

export interface GamePerformanceMetrics {
  apiResponseTime: number;
  gameStateSize: number;
  renderTime: number;
  animationFrameRate: number;
  memoryLeaks: boolean;
  componentRenderCount: number;
}

// Performance monitoring class
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number> = new Map();
  private observers: Map<string, PerformanceObserver> = new Map();
  private frameRateBuffer: number[] = [];
  private lastFrameTime = 0;

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObservers();
      this.startFrameRateMonitoring();
    }
  }

  private initializeObservers() {
    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.set('lcp', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.set('lcp', lcpObserver);

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          this.metrics.set('fid', entry.processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.set('fid', fidObserver);

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.metrics.set('cls', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.set('cls', clsObserver);
    }
  }

  private startFrameRateMonitoring() {
    const measureFrameRate = (timestamp: number) => {
      if (this.lastFrameTime) {
        const delta = timestamp - this.lastFrameTime;
        const fps = 1000 / delta;
        
        this.frameRateBuffer.push(fps);
        if (this.frameRateBuffer.length > 60) {
          this.frameRateBuffer.shift();
        }
      }
      this.lastFrameTime = timestamp;
      requestAnimationFrame(measureFrameRate);
    };
    requestAnimationFrame(measureFrameRate);
  }

  // Measure API response time
  measureApiCall<T>(apiCall: () => Promise<T>, label: string): Promise<T> {
    const startTime = performance.now();
    return apiCall().then(result => {
      const endTime = performance.now();
      this.metrics.set(`api_${label}`, endTime - startTime);
      return result;
    }).catch(error => {
      const endTime = performance.now();
      this.metrics.set(`api_${label}_error`, endTime - startTime);
      throw error;
    });
  }

  // Measure render time
  measureRender(componentName: string, renderFn: () => void): void {
    const startTime = performance.now();
    renderFn();
    const endTime = performance.now();
    this.metrics.set(`render_${componentName}`, endTime - startTime);
  }

  // Get current performance metrics
  getMetrics(): PerformanceMetrics {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paintEntries = performance.getEntriesByType('paint');
    
    return {
      loadTime: navigation ? Math.round(navigation.loadEventEnd - navigation.fetchStart) : 0,
      domContentLoaded: navigation ? Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart) : 0,
      firstPaint: paintEntries.find(entry => entry.name === 'first-paint')?.startTime || 0,
      firstContentfulPaint: paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0,
      largestContentfulPaint: this.metrics.get('lcp') || 0,
      cumulativeLayoutShift: this.metrics.get('cls') || 0,
      firstInputDelay: this.metrics.get('fid') || 0,
      memoryUsage: this.getMemoryUsage(),
    };
  }

  // Get game-specific performance metrics
  getGameMetrics(): GamePerformanceMetrics {
    const avgFrameRate = this.frameRateBuffer.length > 0 
      ? this.frameRateBuffer.reduce((sum, fps) => sum + fps, 0) / this.frameRateBuffer.length 
      : 60;

    return {
      apiResponseTime: this.metrics.get('api_game') || 0,
      gameStateSize: this.metrics.get('gameStateSize') || 0,
      renderTime: this.metrics.get('render_GameBoard') || 0,
      animationFrameRate: avgFrameRate,
      memoryLeaks: this.detectMemoryLeaks(),
      componentRenderCount: this.metrics.get('componentRenders') || 0,
    };
  }

  private getMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
      };
    }
    return undefined;
  }

  private detectMemoryLeaks(): boolean {
    const memory = this.getMemoryUsage();
    if (!memory) return false;
    
    // Simple heuristic: if used heap is > 80% of total, potential leak
    return (memory.usedJSHeapSize / memory.totalJSHeapSize) > 0.8;
  }

  // Track game state size
  trackGameStateSize(gameState: any): void {
    const size = JSON.stringify(gameState).length;
    this.metrics.set('gameStateSize', size);
  }

  // Track component renders
  trackComponentRender(componentName: string): void {
    const current = this.metrics.get(`render_count_${componentName}`) || 0;
    this.metrics.set(`render_count_${componentName}`, current + 1);
    
    const total = this.metrics.get('componentRenders') || 0;
    this.metrics.set('componentRenders', total + 1);
  }

  // Get performance score (0-100)
  getPerformanceScore(): number {
    const metrics = this.getMetrics();
    let score = 100;

    // Deduct points for poor metrics
    if (metrics.firstContentfulPaint > 2500) score -= 20;
    if (metrics.largestContentfulPaint > 4000) score -= 20;
    if (metrics.firstInputDelay > 300) score -= 20;
    if (metrics.cumulativeLayoutShift > 0.25) score -= 20;
    
    const gameMetrics = this.getGameMetrics();
    if (gameMetrics.animationFrameRate < 30) score -= 20;

    return Math.max(0, score);
  }

  // Cleanup observers
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }
}

// Utility functions
export const performanceMonitor = PerformanceMonitor.getInstance();

// React hook for performance monitoring
export function usePerformanceMonitoring(componentName: string) {
  const trackRender = () => {
    performanceMonitor.trackComponentRender(componentName);
  };

  const measureOperation = <T>(operation: () => T, operationName: string): T => {
    const startTime = performance.now();
    const result = operation();
    const endTime = performance.now();
    performanceMonitor.measureRender(`${componentName}_${operationName}`, () => {});
    return result;
  };

  return { trackRender, measureOperation };
}

// Debounce utility for performance
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

// Throttle utility for performance
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Memory optimization utilities
export function optimizeGameState(gameState: any): any {
  // Remove unnecessary fields for performance
  const optimized = { ...gameState };
  
  // Limit definition history to last 50 entries
  if (optimized.definitions && optimized.definitions.length > 50) {
    optimized.definitions = optimized.definitions.slice(-50);
  }
  
  // Limit rejection history to last 20 entries
  if (optimized.rejectionHistory && optimized.rejectionHistory.length > 20) {
    optimized.rejectionHistory = optimized.rejectionHistory.slice(-20);
  }
  
  return optimized;
}

// Check if device has performance constraints
export function isLowEndDevice(): boolean {
  if (typeof navigator === 'undefined') return false;
  
  // Check for hardware concurrency (CPU cores)
  const cores = navigator.hardwareConcurrency || 1;
  if (cores <= 2) return true;
  
  // Check for memory constraints
  const memory = (navigator as any).deviceMemory;
  if (memory && memory <= 2) return true;
  
  // Check for connection speed
  const connection = (navigator as any).connection;
  if (connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g')) {
    return true;
  }
  
  return false;
}

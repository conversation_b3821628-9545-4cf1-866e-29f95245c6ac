/**
 * DEFEATER.AI Design System
 * 
 * High-contrast, accessible design system for low-friction gaming experience.
 * Based on UX redesign requirements and MVP core mechanics analysis.
 */

/* ===== CSS CUSTOM PROPERTIES ===== */

:root {
  /* === COLOR PALETTE === */
  
  /* Text Colors - High Contrast for Accessibility */
  --text-primary: #ffffff;        /* Pure white for main content */
  --text-secondary: #e2e8f0;      /* Light gray for secondary content */
  --text-muted: #94a3b8;          /* Muted for less important text */
  --text-disabled: #64748b;       /* Disabled states */
  --text-inverse: #0f172a;        /* Dark text on light backgrounds */

  /* Semantic Colors - Meaningful Only */
  --color-success: #10b981;       /* Green for valid/wins */
  --color-error: #ef4444;         /* Red for errors/invalid */
  --color-warning: #f59e0b;       /* Amber for warnings */
  --color-info: #3b82f6;          /* Blue for information */

  /* Background Colors */
  --bg-primary: #0f172a;          /* Deep slate for main background */
  --bg-secondary: #1e293b;        /* Lighter slate for panels */
  --bg-tertiary: #334155;         /* Even lighter for inputs */
  --bg-glass-light: rgba(30, 41, 59, 0.8);   /* Glass effect light */
  --bg-glass-medium: rgba(51, 65, 85, 0.9);  /* Glass effect medium */

  /* Accent Colors - Strategic Use Only */
  --accent-purple: #8b5cf6;       /* Primary brand color */
  --accent-cyan: #06b6d4;         /* Secondary accent */
  --accent-pink: #ec4899;         /* Tertiary accent */
  --accent-green: #10b981;        /* Success accent */

  /* Component Color Aliases (for compatibility) */
  --color-primary: #06b6d4;       /* Maps to accent-cyan */
  --color-accent: #8b5cf6;        /* Maps to accent-purple */
  --color-primary-foreground: #ffffff;
  --color-primary-hover: #8b5cf6; /* Maps to accent-purple */
  --color-secondary: rgba(30, 41, 59, 0.8); /* Maps to bg-glass-light */
  --color-secondary-foreground: #e2e8f0; /* Maps to text-secondary */
  --color-secondary-hover: rgba(51, 65, 85, 0.9); /* Maps to bg-glass-medium */
  --color-foreground: #ffffff;    /* Maps to text-primary */
  --color-muted-foreground: #94a3b8; /* Maps to text-muted */
  --color-muted: #334155;         /* Maps to bg-tertiary */
  --color-border: #334155;        /* Maps to bg-tertiary */
  --color-border-hover: #06b6d4;  /* Maps to accent-cyan */
  --color-card: rgba(30, 41, 59, 0.8); /* Maps to bg-glass-light */
  --color-background-overlay: rgba(15, 23, 42, 0.8); /* Maps to bg-primary with opacity */
  --color-focus: #06b6d4;         /* Maps to accent-cyan */

  /* Gradient Colors for Aurora Effects */
  --gradient-primary: linear-gradient(135deg, 
    #0f172a 0%,     /* Deep slate */
    #1e1b4b 25%,    /* Deep purple */
    #312e81 50%,    /* Purple */
    #1e40af 75%,    /* Blue */
    #0f172a 100%    /* Back to deep slate */
  );

  /* Aurora Overlay Effects */
  --aurora-purple: radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.3) 0%, transparent 50%);
  --aurora-cyan: radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.2) 0%, transparent 50%);
  --aurora-pink: radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);

  /* === TYPOGRAPHY SCALE === */
  
  /* Font Families */
  --font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;

  /* Font Sizes - IMPOSING & COMMANDING Scale */
  --text-xs: 1rem;         /* 16px - NO tiny text allowed */
  --text-sm: 1.125rem;     /* 18px - Small but VISIBLE */
  --text-base: 1.25rem;    /* 20px - Base COMMANDING size */
  --text-lg: 1.5rem;       /* 24px - Large and IMPOSING */
  --text-xl: 1.875rem;     /* 30px - Extra large PRESENCE */
  --text-2xl: 2.25rem;     /* 36px - Primary BOLD headings */
  --text-3xl: 3rem;        /* 48px - Major COMMANDING headings */
  --text-4xl: 4rem;        /* 64px - Hero secondary POWER */
  --text-5xl: 5rem;        /* 80px - Hero TRANSFORMER dominance */
  --text-6xl: 6rem;        /* 96px - MAXIMUM intimidation */

  /* Font Weights - Bold & Confident */
  --font-light: 400;       /* No thin fonts - minimum normal */
  --font-normal: 500;      /* Medium as new normal */
  --font-medium: 600;      /* Semibold as medium */
  --font-semibold: 700;    /* Bold as semibold */
  --font-bold: 800;        /* Extrabold as bold */
  --font-extrabold: 900;   /* Black as extrabold */

  /* Line Heights - Spacious & Confident */
  --leading-none: 1;
  --leading-tight: 1.1;     /* Tighter for hero text */
  --leading-snug: 1.3;      /* Snug but readable */
  --leading-normal: 1.4;    /* Comfortable reading */
  --leading-relaxed: 1.6;   /* Spacious for body text */
  --leading-loose: 1.8;     /* Very spacious */

  /* Text Shadows - IMPOSING PRESENCE */
  --text-shadow-subtle: 0 1px 2px rgba(0, 0, 0, 0.3);
  --text-shadow-medium: 0 2px 4px rgba(0, 0, 0, 0.4);
  --text-shadow-strong: 0 3px 6px rgba(0, 0, 0, 0.5);
  --text-shadow-hero: 0 4px 8px rgba(0, 0, 0, 0.6), 0 0 20px rgba(6, 182, 212, 0.2);
  --text-shadow-commanding: 0 6px 12px rgba(0, 0, 0, 0.7), 0 0 30px rgba(6, 182, 212, 0.3);

  /* === SPACING SCALE === */
  
  --space-0: 0;
  --space-1: 0.25rem;      /* 4px */
  --space-2: 0.5rem;       /* 8px */
  --space-3: 0.75rem;      /* 12px */
  --space-4: 1rem;         /* 16px */
  --space-5: 1.25rem;      /* 20px */
  --space-6: 1.5rem;       /* 24px */
  --space-8: 2rem;         /* 32px */
  --space-10: 2.5rem;      /* 40px */
  --space-12: 3rem;        /* 48px */
  --space-16: 4rem;        /* 64px */
  --space-20: 5rem;        /* 80px */
  --space-24: 6rem;        /* 96px */

  /* === SPATIAL DESIGN DIMENSIONS === */

  /* Content Flow Widths - No containers, just content limits */
  --content-narrow: 400px;      /* For focused inputs */
  --content-medium: 600px;      /* For main content */
  --content-wide: 800px;        /* For full layouts */
  --content-max: 1200px;        /* Maximum readable width */

  /* Spatial Relationships */
  --section-spacing: 6rem;      /* Between major sections */
  --group-spacing: 3rem;        /* Between content groups */
  --element-spacing: 1.5rem;    /* Between related elements */
  --tight-spacing: 0.75rem;     /* For closely related items */

  /* Interactive Element Sizes */
  --input-min-height: 120px;
  --input-height: 120px;        /* Alias for compatibility */
  --button-min-height: 56px;
  --button-height: 56px;        /* Alias for compatibility */
  --touch-target: 44px;         /* WCAG minimum touch target */
  --touch-target-large: 48px;   /* Enhanced touch target */
  --touch-target-xl: 56px;      /* Extra large touch target */
  --touch-target-mobile: 48px;  /* Mobile-optimized touch target */

  /* === BORDER RADIUS === */
  
  --radius-sm: 0.125rem;    /* 2px */
  --radius-base: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;    /* 6px */
  --radius-lg: 0.5rem;      /* 8px */
  --radius-xl: 0.75rem;     /* 12px */
  --radius-2xl: 1rem;       /* 16px */
  --radius-3xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;

  /* === SPATIAL EFFECTS === */

  /* Subtle depth without containers */
  --depth-subtle: 0 2px 8px rgba(0, 0, 0, 0.1);
  --depth-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --depth-strong: 0 8px 32px rgba(0, 0, 0, 0.2);

  /* Shadow Effects (aliases for depth) */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-dark-soft: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-dark-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);

  /* Glow Effects for Floating Elements */
  --glow-subtle: 0 0 20px rgba(255, 255, 255, 0.05);
  --glow-purple: 0 0 20px rgba(139, 92, 246, 0.3);
  --glow-cyan: 0 0 20px rgba(6, 182, 212, 0.3);
  --glow-pink: 0 0 20px rgba(236, 72, 153, 0.3);
  --glow-green: 0 0 20px rgba(16, 185, 129, 0.3);

  /* Glass Effects for Minimal UI */
  --glass-subtle: rgba(255, 255, 255, 0.03);
  --glass-medium: rgba(255, 255, 255, 0.05);
  --glass-strong: rgba(255, 255, 255, 0.08);
  --glass-heavy: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.1);

  /* === TRANSITIONS === */
  
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slower: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Easing Functions */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* === Z-INDEX SCALE === */
  
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-modal: 1000;
  --z-popover: 1010;
  --z-tooltip: 1020;
  --z-notification: 1030;

  /* === RESPONSIVE BREAKPOINTS === */

  /* Standard Breakpoint Scale - Mobile First */
  --bp-xs: 480px;        /* Extra small mobile (iPhone SE) */
  --bp-sm: 640px;        /* Small mobile/large mobile */
  --bp-md: 768px;        /* Tablet portrait */
  --bp-lg: 1024px;       /* Tablet landscape/small desktop */
  --bp-xl: 1280px;       /* Desktop */
  --bp-2xl: 1440px;      /* Large desktop */
  --bp-3xl: 1536px;      /* Extra large desktop */

  /* Legacy Support (for gradual migration) */
  --breakpoint-mobile: 767px;     /* DEPRECATED: Use --bp-md instead */
  --breakpoint-tablet: 1023px;    /* DEPRECATED: Use --bp-lg instead */
  --breakpoint-desktop: 1024px;   /* DEPRECATED: Use --bp-lg instead */
}

/* ===== RESPONSIVE BREAKPOINT SYSTEM ===== */

/* Mobile First Approach - IMPOSING Scaling */

/* Small Mobile (640px+) */
@media (min-width: var(--bp-sm)) {
  :root {
    --text-5xl: 5.5rem;     /* 88px - IMPOSING hero text on tablets */
    --text-4xl: 4.5rem;     /* 72px - Scale up secondary hero */
    --text-3xl: 3.5rem;     /* 56px - Scale up major headings */
    --text-2xl: 2.75rem;    /* 44px - Stronger primary headings */
  }
}

/* Tablet Portrait (768px+) */
@media (min-width: var(--bp-md)) {
  :root {
    --side-panel-width: var(--side-panel-width-tablet);
  }
}

/* Tablet Landscape/Desktop (1024px+) */
@media (min-width: var(--bp-lg)) {
  :root {
    --side-panel-width: 350px;
    --text-5xl: 6rem;       /* 96px - COMMANDING impact on desktop */
    --text-4xl: 5rem;       /* 80px - Large secondary hero */
    --text-3xl: 4rem;       /* 64px - Bold major headings */
    --text-2xl: 3rem;       /* 48px - Strong primary headings */
    --text-xl: 2.25rem;     /* 36px - Extra large presence */
  }
}

/* Large Desktop (1440px+) */
@media (min-width: var(--bp-2xl)) {
  :root {
    --text-5xl: 7rem;       /* 112px - MAXIMUM intimidation on large screens */
    --text-4xl: 5.5rem;     /* 88px - Scale up secondary */
    --text-3xl: 4.5rem;     /* 72px - Scale up major */
  }
}

/* === MOBILE PERFORMANCE OPTIMIZATIONS === */

/* Mobile-specific optimizations for better performance */
@media (max-width: calc(var(--bp-md) - 1px)) {
  :root {
    /* Simplified shadows for mobile performance */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.1);

    /* Reduced blur for mobile performance */
    --glass-subtle: rgba(255, 255, 255, 0.05);
    --glass-medium: rgba(255, 255, 255, 0.08);
    --glass-strong: rgba(255, 255, 255, 0.12);
  }
}

/* Extra small mobile optimizations */
@media (max-width: calc(var(--bp-xs) - 1px)) {
  :root {
    /* Further simplified effects for very small screens */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.05);

    /* Minimal glass effects */
    --glass-subtle: rgba(255, 255, 255, 0.03);
    --glass-medium: rgba(255, 255, 255, 0.05);
    --glass-strong: rgba(255, 255, 255, 0.08);
  }
}

/* ===== ACCESSIBILITY FEATURES ===== */

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0ms;
    --transition-base: 0ms;
    --transition-slow: 0ms;
    --transition-slower: 0ms;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --bg-primary: #000000;
    --bg-secondary: #1a1a1a;
  }
}

/* Dark mode is default, but support light mode if needed */
@media (prefers-color-scheme: light) {
  :root {
    /* Light mode overrides would go here if needed */
    /* Currently DEFEATER.AI is dark-theme only */
  }
}

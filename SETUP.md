# DEFEATER.AI Development Setup Guide

## 🚀 **Quick Start**

### **Prerequisites**
- **Node.js** v18+ ([Download](https://nodejs.org/))
- **Ollama** ([Install Guide](https://ollama.ai/))
- **Git** ([Download](https://git-scm.com/))

### **Installation**
```bash
# 1. Clone repository
git clone https://github.com/Aladin147/DefeaterAI.git
cd DefeaterAI

# 2. Install dependencies
npm install

# 3. Set up Ollama AI models
ollama pull gemma3:4b
ollama create gemma3-4b-gpu -f Modelfile.gemma3-gpu

# 4. Start development server
npm run dev

# 5. Open browser
open http://localhost:3000
```

---

## 🔧 **Detailed Setup**

### **1. Environment Setup**

#### **Node.js & npm**

```bash
# Check versions
node --version  # Should be v18+
npm --version   # Should be v8+

# If outdated, install latest LTS from nodejs.org
```

#### **Ollama Installation**

```bash
# macOS/Linux
curl -fsSL https://ollama.ai/install.sh | sh

# Windows
# Download from https://ollama.ai/download

# Verify installation
ollama --version
```

### **2. AI Model Configuration**

#### **Download Base Model**

```bash
# Pull the base model (this may take a few minutes)
ollama pull gemma3:4b

# Verify model is available
ollama list
```

#### **Create GPU-Optimized Model**

```bash
# Create custom GPU model using our Modelfile
ollama create gemma3-4b-gpu -f Modelfile.gemma3-gpu

# Test the model
ollama run gemma3-4b-gpu "Hello, are you working?"
```

### **3. Project Dependencies**

```bash
# Install all dependencies
npm install

# Verify installation
npm list --depth=0
```

### **4. Development Server**

```bash
# Start with hot reload
npm run dev

# Alternative: Start with verbose logging
NODE_ENV=development npm run dev

# Check server is running
curl http://localhost:3000/api/game -X POST -H "Content-Type: application/json" -d '{"action":"start"}'
```

---

## ⚙️ **Configuration**

### **Environment Variables**

Create `.env.local` for custom configuration:

```bash
# Optional: Custom Ollama URL
OLLAMA_API_URL=http://localhost:11434

# Optional: Different model
OLLAMA_MODEL=gemma3-4b-gpu

# Optional: Enable debug logging
DEBUG=true
```

### **Core Configuration Files**

#### **`utils/constants.ts`** - Main configuration

```typescript
export const OLLAMA_CONFIG = {
  apiUrl: 'http://localhost:11434',
  model: 'gemma3-4b-gpu',           // GPU-optimized model
  defaultTemperature: 0.6,          // AI creativity level
  maxTokens: 2048,                  // Response length limit
} as const;
```

---

## 🐛 **Troubleshooting**

### **Common Issues**

#### **Ollama Not Responding**

```bash
# Check if Ollama is running
ps aux | grep ollama

# Start Ollama service
ollama serve

# Test connection
curl http://localhost:11434/api/tags
```

#### **Model Not Found**

```bash
# List available models
ollama list

# Re-create GPU model if missing
ollama create gemma3-4b-gpu -f Modelfile.gemma3-gpu

# Test model directly
ollama run gemma3-4b-gpu "Test message"
```

#### **Performance Issues**

```bash
# Check GPU usage (if available)
nvidia-smi  # For NVIDIA GPUs

# Monitor Ollama logs
ollama logs

# Increase timeout in constants.ts
export const API_CONFIG = {
  TIMEOUT_MS: 120000,  // Increase to 2 minutes
}
```

---

## 🧪 **Development Workflow**

### **Code Quality**

```bash
# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix

# Build verification
npm run build
```

### **Developer Tools**

- **DevPanel**: Press `🔧 DEV` button (development mode only)
- **Browser Console**: Detailed logging and error information
- **Network Tab**: API request/response inspection

---

## 📚 **Additional Resources**

- **[Technical Architecture](./docs/TECHNICAL_ARCHITECTURE.md)** - System design overview
- **[API Reference](./docs/API_REFERENCE.md)** - Complete API documentation
- **[Game Rules](./docs/DEFEATER_Master_Document.md)** - Original game design
- **[Roadmap](./docs/ROADMAP.md)** - Development roadmap

---

*Setup guide version: 1.0.0 | Last updated: 2025-01-16*

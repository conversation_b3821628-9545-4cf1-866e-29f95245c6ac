{"header": {"title": "DEFEATER.AI", "subtitle": "The Dark Souls of puzzle games", "navigation": "Main navigation", "menu": "<PERSON><PERSON>", "logo": "DEFEATER.AI logo"}, "footer": {"copyright": "© 2024 DEFEATER.AI. All rights reserved.", "privacy": "Privacy Policy", "terms": "Terms of Service", "contact": "Contact Us", "support": "Support", "feedback": "<PERSON><PERSON><PERSON>"}, "sidebar": {"title": "Game Information", "stats": "Statistics", "history": "History", "rules": "Rules", "settings": "Settings", "help": "Help", "toggle": "Toggle sidebar", "expand": "Expand sidebar", "collapse": "Collapse sidebar"}, "modal": {"close": "Close modal", "confirm": "Confirm action", "cancel": "Cancel action", "save": "Save changes", "discard": "Discard changes", "title": "Modal dialog", "overlay": "Modal overlay"}, "tabs": {"stats": "Stats", "history": "History", "rules": "Rules", "settings": "Settings", "help": "Help", "about": "About", "current": "Current tab", "switch": "Switch to {{tab}} tab"}, "forms": {"label": "Form field", "required": "Required field", "optional": "Optional field", "placeholder": "Enter value", "submit": "Submit form", "reset": "Reset form", "clear": "Clear form", "save": "Save form", "cancel": "Cancel form", "validation": "Validation error", "success": "Form submitted successfully", "error": "Form submission failed"}, "buttons": {"primary": "Primary action", "secondary": "Secondary action", "danger": "Dangerous action", "warning": "Warning action", "info": "Information action", "success": "Success action", "disabled": "Disabled action", "loading": "Loading action", "icon": "Icon button", "text": "Text button", "link": "Link button"}, "inputs": {"text": "Text input", "password": "Password input", "email": "Email input", "number": "Number input", "search": "Search input", "textarea": "Text area", "select": "Select dropdown", "checkbox": "Checkbox", "radio": "Radio button", "file": "File input", "date": "Date input", "time": "Time input", "range": "Range slider"}, "feedback": {"success": "Success message", "error": "Error message", "warning": "Warning message", "info": "Information message", "loading": "Loading message", "empty": "No data available", "notFound": "Content not found", "unauthorized": "Access denied", "forbidden": "Action forbidden", "timeout": "Request timed out"}, "loading": {"default": "Loading...", "game": "Loading game...", "data": "Loading data...", "content": "Loading content...", "page": "Loading page...", "component": "Loading component...", "slow": "This is taking longer than usual...", "error": "Loading failed", "retry": "Retry loading", "cancel": "Cancel loading"}, "pagination": {"first": "First page", "previous": "Previous page", "next": "Next page", "last": "Last page", "page": "Page {{number}}", "of": "of {{total}}", "showing": "Showing {{start}} to {{end}} of {{total}} items", "perPage": "Items per page", "goTo": "Go to page"}, "search": {"placeholder": "Search...", "submit": "Search", "clear": "Clear search", "results": "Search results", "noResults": "No results found", "suggestions": "Search suggestions", "recent": "Recent searches", "popular": "Popular searches", "filter": "Filter results", "sort": "Sort results"}, "filters": {"title": "Filters", "apply": "Apply filters", "clear": "Clear filters", "reset": "Reset filters", "active": "Active filters", "category": "Category", "date": "Date", "status": "Status", "type": "Type", "priority": "Priority", "tag": "Tag"}, "sorting": {"title": "Sort by", "ascending": "Ascending", "descending": "Descending", "name": "Name", "date": "Date", "size": "Size", "type": "Type", "relevance": "Relevance", "popularity": "Popularity", "rating": "Rating"}, "tooltips": {"help": "Help information", "info": "Additional information", "warning": "Warning message", "error": "Error details", "shortcut": "Keyboard shortcut", "feature": "Feature description", "status": "Current status", "action": "Action description"}, "notifications": {"title": "Notifications", "new": "New notification", "unread": "Unread notifications", "mark": "<PERSON> as read", "clear": "Clear notifications", "settings": "Notification settings", "empty": "No notifications", "error": "Notification error", "success": "Notification sent"}, "progress": {"loading": "Loading progress", "uploading": "Upload progress", "downloading": "Download progress", "processing": "Processing progress", "complete": "Progress complete", "failed": "Progress failed", "cancelled": "Progress cancelled", "percentage": "{{percent}}% complete"}, "media": {"play": "Play media", "pause": "Pause media", "stop": "Stop media", "mute": "Mute audio", "unmute": "Unmute audio", "volume": "Volume control", "fullscreen": "Enter fullscreen", "exitFullscreen": "Exit fullscreen", "captions": "Toggle captions", "settings": "Media settings"}, "calendar": {"today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "thisWeek": "This week", "nextWeek": "Next week", "thisMonth": "This month", "nextMonth": "Next month", "selectDate": "Select date", "selectTime": "Select time", "dateFormat": "Date format", "timeFormat": "Time format"}, "file": {"upload": "Upload file", "download": "Download file", "delete": "Delete file", "rename": "Rename file", "copy": "Copy file", "move": "Move file", "share": "Share file", "preview": "Preview file", "properties": "File properties", "size": "File size", "type": "File type", "modified": "Last modified"}}
import React from 'react';

interface GameRulesProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function GameRules({ isOpen, onClose }: GameRulesProps) {
  if (!isOpen) {
      return null;
    }

  return (
    <div className="fixed inset-0 bg-defeater-dark-900/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="glass-heavy rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-defeater-neon-cyan/30 shadow-dark-heavy">
        <div className="p-8">
          <div className="flex justify-between items-center" style={{ marginBottom: 'var(--space-8)' }}>
            <h2 className="text-3xl font-bold text-gradient">How to Play DEFEATER.AI</h2>
            <button
              onClick={onClose}
              className="text-defeater-text-muted hover:text-defeater-neon-red text-2xl font-bold transition-colors hover:scale-110"
            >
              ×
            </button>
          </div>

          <div className="space-y-8 text-defeater-text-secondary">
            <section>
              <h3 className="text-xl font-semibold text-defeater-neon-cyan mb-4">🎯 The Goal</h3>
              <p className="leading-relaxed">
                Navigate from your starting word to one of the AI's target words through a chain of definitions.
                Each definition must be shorter than the previous one, and you cannot reuse any words.
              </p>
            </section>

            <section>
              <h3 className="text-xl font-semibold text-defeater-neon-purple mb-4">📏 The Rules</h3>
              <ul className="space-y-3 list-disc list-inside">
                <li><strong className="text-defeater-text-primary">Shorter Definitions:</strong> Each definition must have fewer words than the previous</li>
                <li><strong className="text-defeater-text-primary">No Word Reuse:</strong> Once a word is used, it's gone forever</li>
                <li><strong className="text-defeater-text-primary">Semantic Validity:</strong> Definitions must actually define the word (no rambling!)</li>
                <li><strong className="text-defeater-text-primary">No Circular Logic:</strong> Can't use the word in its own definition</li>
                <li><strong className="text-defeater-text-primary">Reach a Target:</strong> Eventually define one of the AI's target words</li>
              </ul>
            </section>

            <section>
              <h3 className="text-xl font-semibold text-defeater-neon-green mb-4">🧠 Strategy Tips</h3>
              <ul className="space-y-3 list-disc list-inside">
                <li><strong className="text-defeater-text-primary">Ration Your Words:</strong> Start with longer definitions - you'll need shorter ones later</li>
                <li><strong className="text-defeater-text-primary">Plan Ahead:</strong> Think about how to reach the targets before committing words</li>
                <li><strong className="text-defeater-text-primary">Watch the AI:</strong> It will try to lead you away from easy paths</li>
                <li><strong className="text-defeater-text-primary">Target Burning:</strong> The AI can eliminate targets if you get too close</li>
              </ul>
            </section>

            <section>
              <h3 className="text-xl font-semibold text-defeater-neon-red mb-4">⚔️ The AI's Strategy</h3>
              <div className="bg-defeater-neon-red/10 p-5 rounded-lg border border-defeater-neon-red/30">
                <p className="text-defeater-neon-red leading-relaxed">
                  <strong>Warning:</strong> The AI is designed to be ruthlessly fair but merciless.
                  It will exploit your word choices, burn your easiest paths, and force impossible-seeming
                  leaps. Every challenge is technically solvable, but the AI will make you work for it.
                </p>
              </div>
            </section>

            <section>
              <h3 className="text-xl font-semibold text-defeater-neon-orange mb-4">🎮 Game Phases</h3>
              <div className="space-y-4">
                <div className="bg-defeater-neon-green/10 p-4 rounded-lg border border-defeater-neon-green/30">
                  <strong className="text-defeater-neon-green">Opening (Steps 1-5):</strong>
                  <span className="text-defeater-text-secondary"> AI is reasonable, building your confidence</span>
                </div>
                <div className="bg-defeater-neon-orange/10 p-4 rounded-lg border border-defeater-neon-orange/30">
                  <strong className="text-defeater-neon-orange">Middle Game (Steps 6-15):</strong>
                  <span className="text-defeater-text-secondary"> AI tightens the noose, burns easy targets</span>
                </div>
                <div className="bg-defeater-neon-red/10 p-4 rounded-lg border border-defeater-neon-red/30">
                  <strong className="text-defeater-neon-red">End Game (Steps 16-25):</strong>
                  <span className="text-defeater-text-secondary"> Maximum difficulty, single-word definitions</span>
                </div>
              </div>
            </section>

            <section>
              <h3 className="text-xl font-semibold text-defeater-neon-cyan mb-4">🏆 Victory</h3>
              <p className="leading-relaxed mb-4">
                You win by successfully defining one of the target words. The AI wins if you run out of
                steps, all targets are burned, or you're forced into an impossible situation.
              </p>
              <div className="bg-defeater-neon-cyan/10 p-5 rounded-lg border border-defeater-neon-cyan/30">
                <p className="text-defeater-neon-cyan leading-relaxed">
                  <strong>Remember:</strong> This is "The Dark Souls of puzzle games" - expect to lose,
                  learn from each defeat, and come back stronger. Every victory is earned.
                </p>
              </div>
            </section>
          </div>

          <div className="mt-10 flex justify-center">
            <button
              onClick={onClose}
              className="btn-primary px-8 py-3 text-lg font-semibold"
            >
              Got It - Let's Play! 🎯
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

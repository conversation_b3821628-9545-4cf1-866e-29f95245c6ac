import React, { useState, useEffect, useRef } from 'react';
import { Secondary, Imposing } from '@/components/ui/Typography';

interface ChatMessage {
  id: string;
  type: 'ai' | 'system';
  message: string;
  timestamp: number;
  trigger?: 'move' | 'burn' | 'rejection' | 'win' | 'loss' | 'pattern';
}

interface FloatingChatWidgetProps {
  isVisible?: boolean;
  onToggle?: () => void;
  className?: string;
}

export const FloatingChatWidget: React.FC<FloatingChatWidgetProps> = ({
  isVisible = true,
  onToggle,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isPersistent, setIsPersistent] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isAnimating, setIsAnimating] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  
  const autoCollapseTimeoutRef = useRef<NodeJS.Timeout>();
  const notificationTimeoutRef = useRef<NodeJS.Timeout>();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current && isExpanded) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isExpanded]);

  // Add new AI message with auto-expand behavior
  const addAIMessage = (message: string, trigger?: ChatMessage['trigger']) => {
    console.log('💬 FloatingChatWidget: Received message:', { message, trigger });

    const newMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'ai',
      message,
      timestamp: Date.now(),
      trigger
    };

    setMessages(prev => {
      const updated = [...prev, newMessage];
      console.log('💬 FloatingChatWidget: Updated messages:', updated.length);
      return updated;
    });

    // Auto-expand for AI messages (unless already persistent)
    if (!isPersistent) {
      setIsAnimating(true);
      setIsExpanded(true);
      setShowNotification(true);

      // Clear existing timeout
      if (autoCollapseTimeoutRef.current) {
        clearTimeout(autoCollapseTimeoutRef.current);
      }

      // Auto-collapse after 3 seconds
      autoCollapseTimeoutRef.current = setTimeout(() => {
        if (!isPersistent) {
          setIsExpanded(false);
          setShowNotification(false);
          setIsAnimating(false);
        }
      }, 3000);

      // Hide notification after 2.5 seconds (before collapse)
      if (notificationTimeoutRef.current) {
        clearTimeout(notificationTimeoutRef.current);
      }
      notificationTimeoutRef.current = setTimeout(() => {
        setShowNotification(false);
      }, 2500);
    }
  };

  // Manual toggle - makes it persistent
  const handleToggle = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    setIsPersistent(newExpanded);
    setIsAnimating(false);
    setShowNotification(false);

    // Clear auto-collapse timeout when manually toggled
    if (autoCollapseTimeoutRef.current) {
      clearTimeout(autoCollapseTimeoutRef.current);
    }

    if (onToggle) {
      onToggle();
    }
  };

  // Cleanup timeouts
  useEffect(() => {
    return () => {
      if (autoCollapseTimeoutRef.current) {
        clearTimeout(autoCollapseTimeoutRef.current);
      }
      if (notificationTimeoutRef.current) {
        clearTimeout(notificationTimeoutRef.current);
      }
    };
  }, []);

  // Expose addAIMessage function globally for integration
  useEffect(() => {
    console.log('💬 FloatingChatWidget: Registering global function');
    (window as any).addAITrashTalk = addAIMessage;
    return () => {
      console.log('💬 FloatingChatWidget: Unregistering global function');
      delete (window as any).addAITrashTalk;
    };
  }, [addAIMessage]);

  if (!isVisible) {
    return null;
  }

  const widgetClasses = [
    'floating-chat-widget',
    isExpanded ? 'floating-chat-widget--expanded' : 'floating-chat-widget--collapsed',
    isPersistent ? 'floating-chat-widget--persistent' : '',
    isAnimating ? 'floating-chat-widget--animating' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={widgetClasses}>
      {/* Minimized Bubble */}
      {!isExpanded && (
        <div
          className="chat-bubble"
          onClick={handleToggle}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleToggle();
            }
          }}
          role="button"
          tabIndex={0}
          aria-label="Open chat dialog"
        >
          <div className="chat-bubble-icon">💬</div>
          {showNotification && (
            <div className="chat-notification">
              <div className="notification-pulse" />
              <Secondary>AI</Secondary>
            </div>
          )}
        </div>
      )}

      {/* Expanded Chat Panel */}
      {isExpanded && (
        <div className="chat-panel">
          {/* Header */}
          <div className="chat-header">
            <div className="chat-title">
              <Imposing>🤖 GAME MASTER</Imposing>
              <Secondary>Psychological Warfare Mode</Secondary>
            </div>
            <button 
              className="chat-close"
              onClick={handleToggle}
              title="Minimize Chat"
            >
              ✕
            </button>
          </div>

          {/* Messages */}
          <div className="chat-messages">
            {messages.length === 0 ? (
              <div className="chat-empty">
                <Secondary>The AI is watching your every move...</Secondary>
              </div>
            ) : (
              messages.map((message) => (
                <div key={message.id} className={`chat-message chat-message--${message.type}`}>
                  <div className="message-content">
                    {message.message}
                  </div>
                  <div className="message-timestamp">
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Footer */}
          <div className="chat-footer">
            <Secondary>The AI sees all your patterns...</Secondary>
          </div>
        </div>
      )}

      <style jsx>{`
        /* === FLOATING CHAT WIDGET === */
        .floating-chat-widget {
          position: fixed;
          bottom: var(--space-6);
          right: var(--space-6);
          z-index: var(--z-modal);
          font-family: var(--font-primary);
        }

        /* === CHAT BUBBLE (MINIMIZED) === */
        .chat-bubble {
          position: relative;
          width: 60px;
          height: 60px;
          background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          transition: all var(--transition-base);
          border: 2px solid var(--glass-border);
          /* Ensure proper touch target */
          min-width: var(--touch-target-mobile);
          min-height: var(--touch-target-mobile);
        }

        .chat-bubble:hover {
          transform: scale(1.1);
          box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
        }

        .chat-bubble-icon {
          font-size: 24px;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        /* === NOTIFICATION INDICATOR === */
        .chat-notification {
          position: absolute;
          top: -8px;
          right: -8px;
          background: var(--color-error);
          color: var(--text-on-primary);
          padding: var(--space-1) var(--space-2);
          border-radius: var(--radius-full);
          font-size: var(--text-xs);
          font-weight: var(--font-bold);
          display: flex;
          align-items: center;
          gap: var(--space-1);
          animation: notificationPulse 2s infinite;
          box-shadow: 0 4px 16px rgba(220, 38, 38, 0.4);
        }

        .notification-pulse {
          width: 6px;
          height: 6px;
          background: var(--text-on-primary);
          border-radius: 50%;
          animation: pulse 1s infinite;
        }

        /* === CHAT PANEL (EXPANDED) === */
        .chat-panel {
          width: 320px;
          height: 400px;
          background: var(--glass-heavy);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-xl);
          backdrop-filter: blur(20px);
          display: flex;
          flex-direction: column;
          overflow: hidden;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
          animation: chatExpand 0.3s ease-out;
        }

        /* === CHAT HEADER === */
        .chat-header {
          padding: var(--space-4);
          border-bottom: 1px solid var(--glass-border);
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          background: var(--glass-medium);
        }

        .chat-title {
          display: flex;
          flex-direction: column;
          gap: var(--space-1);
        }

        .chat-close {
          background: none;
          border: none;
          color: var(--text-muted);
          font-size: var(--text-lg);
          cursor: pointer;
          padding: var(--space-1);
          border-radius: var(--radius-sm);
          transition: all var(--transition-base);
        }

        .chat-close:hover {
          color: var(--text-primary);
          background: var(--glass-light);
        }

        /* === CHAT MESSAGES === */
        .chat-messages {
          flex: 1;
          padding: var(--space-4);
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
        }

        .chat-empty {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          text-align: center;
          opacity: 0.7;
        }

        .chat-message {
          display: flex;
          flex-direction: column;
          gap: var(--space-1);
          animation: messageSlideIn 0.3s ease-out;
        }

        .chat-message--ai {
          align-items: flex-start;
        }

        .message-content {
          background: var(--glass-medium);
          padding: var(--space-3);
          border-radius: var(--radius-lg);
          border: 1px solid var(--glass-border);
          font-size: var(--text-sm);
          line-height: 1.4;
          color: var(--text-primary);
          max-width: 85%;
          position: relative;
        }

        .chat-message--ai .message-content {
          background: linear-gradient(135deg, var(--color-primary-10), var(--color-accent-10));
          border-color: var(--color-primary-30);
        }

        .message-timestamp {
          font-size: var(--text-xs);
          color: var(--text-muted);
          margin-left: var(--space-2);
        }

        /* === CHAT FOOTER === */
        .chat-footer {
          padding: var(--space-3) var(--space-4);
          border-top: 1px solid var(--glass-border);
          background: var(--glass-medium);
          text-align: center;
        }

        /* === ANIMATIONS === */
        @keyframes notificationPulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }

        @keyframes chatExpand {
          0% {
            transform: scale(0.8) translateY(20px);
            opacity: 0;
          }
          100% {
            transform: scale(1) translateY(0);
            opacity: 1;
          }
        }

        @keyframes messageSlideIn {
          0% {
            transform: translateX(-20px);
            opacity: 0;
          }
          100% {
            transform: translateX(0);
            opacity: 1;
          }
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: calc(var(--bp-md) - 1px)) {
          .floating-chat-widget {
            bottom: var(--space-4);
            right: var(--space-4);
          }

          .chat-panel {
            width: 280px;
            height: 350px;
          }

          .chat-bubble {
            width: var(--touch-target-mobile);
            height: var(--touch-target-mobile);
            /* Ensure minimum touch target on mobile */
            min-width: var(--touch-target-mobile);
            min-height: var(--touch-target-mobile);
          }

          .chat-bubble-icon {
            font-size: 20px;
          }
        }

        /* Extra small mobile optimizations */
        @media (max-width: calc(var(--bp-xs) - 1px)) {
          .floating-chat-widget {
            bottom: var(--space-3);
            right: var(--space-3);
          }

          .chat-panel {
            width: calc(100vw - 2rem);
            max-width: 280px;
            height: 320px;
          }

          .chat-bubble {
            width: var(--touch-target);
            height: var(--touch-target);
            min-width: var(--touch-target);
            min-height: var(--touch-target);
          }

          .chat-bubble-icon {
            font-size: 18px;
          }
        }

        /* === ACCESSIBILITY === */
        @media (prefers-reduced-motion: reduce) {
          .chat-bubble,
          .chat-panel,
          .chat-message,
          .notification-pulse {
            animation: none;
            transition: none;
          }

          .chat-bubble:hover {
            transform: none;
          }
        }

        /* === SCROLLBAR STYLING === */
        .chat-messages::-webkit-scrollbar {
          width: 4px;
        }

        .chat-messages::-webkit-scrollbar-track {
          background: var(--glass-light);
        }

        .chat-messages::-webkit-scrollbar-thumb {
          background: var(--glass-border);
          border-radius: 2px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
          background: var(--text-muted);
        }
      `}</style>
    </div>
  );
};

export default FloatingChatWidget;

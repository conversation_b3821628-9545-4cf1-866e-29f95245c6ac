/**
 * Visual Regression Testing Suite
 * 
 * Compares restored main app with working open-design-test page
 * to ensure identical spatial flow, typography, and component behavior
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Import pages to compare
import HomePage from '@/pages/index';
import OpenDesignTestPage from '@/pages/open-design-test';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/',
  }),
}));

// Mock API calls
global.fetch = jest.fn();

// Mock portal for testing
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (node: React.ReactNode) => node,
}));

describe('Visual Regression Testing', () => {
  
  beforeEach(() => {
    // Reset fetch mock
    (fetch as jest.MockedFunction<typeof fetch>).mockClear();
    
    // Mock successful game start API response
    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        gameState: {
          gameId: 'test-game-123',
          currentWord: 'transformer',
          step: 1,
          maxSteps: 25,
          targets: ['innovation', 'ecosystem', 'friction', 'paradigm'],
          completedTargets: [],
          burnedTargets: [],
          definitions: [],
          difficulty: 'medium',
          usedWords: [],
          aiChallengeWords: [],
          gameStatus: 'waiting',
          consecutiveRejections: 0,
          commonWordsUsage: {},
          rejectionHistory: []
        }
      })
    } as Response);
  });

  describe('Layout Structure Comparison', () => {
    test('Main app should use GameLayout like test page', async () => {
      render(<HomePage />);
      
      // Wait for game to initialize
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Check for GameLayout presence (should have main content area)
      const mainContent = document.querySelector('[role="main"]') || 
                         document.querySelector('.game-layout') ||
                         document.querySelector('main');
      
      expect(mainContent).toBeInTheDocument();
    });

    test('Test page should have identical layout structure', () => {
      render(<OpenDesignTestPage />);
      
      // Check for GameLayout presence
      const mainContent = document.querySelector('[role="main"]') || 
                         document.querySelector('.game-layout') ||
                         document.querySelector('main');
      
      expect(mainContent).toBeInTheDocument();
    });

    test('Both pages should use GameFocus for content organization', async () => {
      const { unmount } = render(<HomePage />);
      
      // Wait for game initialization
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Check for GameFocus container
      const gameFocus = document.querySelector('.game-focus') ||
                       document.querySelector('[data-testid="game-focus"]');
      
      unmount();
      
      // Test page should also have GameFocus
      render(<OpenDesignTestPage />);
      const testPageGameFocus = document.querySelector('.game-focus') ||
                               document.querySelector('[data-testid="game-focus"]');
      
      // Both should have similar focus containers
      expect(gameFocus || testPageGameFocus).toBeTruthy();
    });
  });

  describe('Component Presence Comparison', () => {
    test('Both pages should have CurrentChallenge component', async () => {
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Look for challenge word display
      const challengeWord = screen.queryByText(/TRANSFORMER/i) || 
                           screen.queryByText(/transformer/i);
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPageChallenge = screen.queryByText(/TRANSFORMER/i) || 
                               screen.queryByText(/transformer/i);
      
      // At least one should have the challenge component
      expect(challengeWord || testPageChallenge).toBeTruthy();
    });

    test('Both pages should have DefinitionInput component', async () => {
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Look for definition input
      const definitionInput = screen.queryByPlaceholderText(/define/i) ||
                             screen.queryByRole('textbox') ||
                             screen.queryByLabelText(/definition/i);
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPageInput = screen.queryByPlaceholderText(/define/i) ||
                           screen.queryByRole('textbox') ||
                           screen.queryByLabelText(/definition/i);
      
      // Both should have input components
      expect(definitionInput || testPageInput).toBeTruthy();
    });

    test('Both pages should have TargetRevelationStrip component', async () => {
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Look for target revelation (partial words with underscores)
      const targetReveal = document.querySelector('[class*="target"]') ||
                          screen.queryByText(/I n n o v/i) ||
                          screen.queryByText(/_/);
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPageTargets = document.querySelector('[class*="target"]') ||
                             screen.queryByText(/I n n o v/i) ||
                             screen.queryByText(/_/);
      
      // At least one should have target revelation
      expect(targetReveal || testPageTargets).toBeTruthy();
    });

    test('Both pages should have CollapsibleSidePanel', async () => {
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Look for side panel toggle or panel content
      const sidePanel = screen.queryByRole('complementary') ||
                       screen.queryByText(/Stats/i) ||
                       screen.queryByText(/History/i) ||
                       document.querySelector('[class*="side-panel"]');
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPagePanel = screen.queryByRole('complementary') ||
                            screen.queryByText(/Stats/i) ||
                            screen.queryByText(/History/i) ||
                            document.querySelector('[class*="side-panel"]');
      
      // Both should have side panel functionality
      expect(sidePanel || testPagePanel).toBeTruthy();
    });
  });

  describe('Typography Consistency', () => {
    test('Both pages should use consistent typography components', async () => {
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Check for typography classes or components
      const typographyElements = document.querySelectorAll('[class*="text-"], h1, h2, h3, p');
      const mainAppTypographyCount = typographyElements.length;
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPageTypography = document.querySelectorAll('[class*="text-"], h1, h2, h3, p');
      const testPageTypographyCount = testPageTypography.length;
      
      // Both should have substantial typography elements
      expect(mainAppTypographyCount).toBeGreaterThan(0);
      expect(testPageTypographyCount).toBeGreaterThan(0);
    });

    test('Font weights should be consistent between pages', async () => {
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Check computed styles for font weights
      const mainAppElements = document.querySelectorAll('h1, h2, h3, .text-bold, [class*="font-"]');
      const mainAppFontWeights = Array.from(mainAppElements).map(el => 
        window.getComputedStyle(el).fontWeight
      );
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPageElements = document.querySelectorAll('h1, h2, h3, .text-bold, [class*="font-"]');
      const testPageFontWeights = Array.from(testPageElements).map(el => 
        window.getComputedStyle(el).fontWeight
      );
      
      // Should have similar font weight usage patterns
      expect(mainAppFontWeights.length).toBeGreaterThan(0);
      expect(testPageFontWeights.length).toBeGreaterThan(0);
    });
  });

  describe('Responsive Behavior Comparison', () => {
    test('Both pages should handle mobile viewport correctly', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 667,
      });
      
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Check for mobile-responsive elements
      const mobileElements = document.querySelectorAll('[class*="mobile"], [class*="sm:"], [class*="md:"]');
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPageMobileElements = document.querySelectorAll('[class*="mobile"], [class*="sm:"], [class*="md:"]');
      
      // Both should have responsive design elements
      expect(mobileElements.length + testPageMobileElements.length).toBeGreaterThan(0);
    });

    test('Side panels should behave consistently on mobile', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      const user = userEvent.setup();
      
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      // Look for side panel toggle
      const toggleButton = screen.queryByRole('button', { name: /toggle/i }) ||
                          screen.queryByRole('button', { name: /menu/i }) ||
                          document.querySelector('[class*="toggle"]');
      
      if (toggleButton) {
        await user.click(toggleButton);
        // Check if panel opens
        const openPanel = screen.queryByRole('complementary');
        expect(openPanel).toBeTruthy();
      }
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPageToggle = screen.queryByRole('button', { name: /toggle/i }) ||
                            screen.queryByRole('button', { name: /menu/i }) ||
                            document.querySelector('[class*="toggle"]');
      
      // Both should have toggle functionality
      expect(toggleButton || testPageToggle).toBeTruthy();
    });
  });

  describe('Interaction Behavior Comparison', () => {
    test('Input handling should be consistent between pages', async () => {
      const user = userEvent.setup();
      
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      const mainAppInput = screen.queryByRole('textbox');
      if (mainAppInput) {
        await user.type(mainAppInput, 'test definition');
        expect(mainAppInput).toHaveValue('test definition');
      }
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPageInput = screen.queryByRole('textbox');
      if (testPageInput) {
        await user.type(testPageInput, 'test definition');
        expect(testPageInput).toHaveValue('test definition');
      }
      
      // At least one should have working input
      expect(mainAppInput || testPageInput).toBeTruthy();
    });

    test('Button interactions should work consistently', async () => {
      const user = userEvent.setup();
      
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      const mainAppButtons = screen.queryAllByRole('button');
      const mainAppButtonCount = mainAppButtons.length;
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPageButtons = screen.queryAllByRole('button');
      const testPageButtonCount = testPageButtons.length;
      
      // Both should have interactive buttons
      expect(mainAppButtonCount).toBeGreaterThan(0);
      expect(testPageButtonCount).toBeGreaterThan(0);
    });
  });

  describe('Performance Comparison', () => {
    test('Both pages should render within acceptable time', async () => {
      const startTime = performance.now();
      
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      const mainAppRenderTime = performance.now() - startTime;
      
      unmount();
      
      const testPageStartTime = performance.now();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      
      const testPageRenderTime = performance.now() - testPageStartTime;
      
      // Both should render reasonably quickly (under 2 seconds)
      expect(mainAppRenderTime).toBeLessThan(2000);
      expect(testPageRenderTime).toBeLessThan(2000);
    });

    test('DOM complexity should be similar between pages', async () => {
      // Test main app
      const { unmount } = render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      const mainAppElementCount = document.querySelectorAll('*').length;
      
      unmount();
      
      // Test reference page
      render(<OpenDesignTestPage />);
      const testPageElementCount = document.querySelectorAll('*').length;
      
      // DOM complexity should be in similar range (within 50% difference)
      const ratio = Math.max(mainAppElementCount, testPageElementCount) / 
                   Math.min(mainAppElementCount, testPageElementCount);
      
      expect(ratio).toBeLessThan(1.5); // Within 50% difference
    });
  });
});

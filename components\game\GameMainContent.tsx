/**
 * GameMainContent Component - Extracted from GameBoard.tsx
 *
 * Handles the core game content rendering including:
 * - Current challenge display
 * - Definition input interface
 * - Target revelation strip
 * - Error display
 * - Game over screen
 *
 * This extraction reduces GameBoard.tsx complexity and creates a focused
 * component responsible for the main game interaction elements.
 */

import React from 'react';
import { GameState, AnimationState } from '@/types/game';
import { calculateMaxWords } from '@/utils/gameLogic';

// Game Components
import CurrentChallenge from '@/components/game/CurrentChallenge';
import DefinitionInput from '@/components/game/DefinitionInput';
import TargetRevelationStrip from '@/components/game/TargetRevelationStrip';
import GameErrorDisplay from '@/components/game/GameErrorDisplay';
import GameOverScreen from '@/components/game/GameOverScreen';

interface GameMainContentProps {
  // Game State
  gameState: GameState | null;
  isGameOver: boolean;
  showDifficultySelector: boolean;

  // UI State
  inputValue: string;
  wordCount: number;
  isLoading: boolean;
  animationState: AnimationState;
  error: string | null;

  // Event Handlers
  onInputChange: (value: string) => void;
  onSubmitDefinition: (definition: string) => void;
  onClearError: () => void;
  onShowAnalysis: () => void;
  onStartNewGame: () => void;
}

export const GameMainContent: React.FC<GameMainContentProps> = ({
  gameState,
  isGameOver,
  showDifficultySelector,
  inputValue,
  wordCount,
  isLoading,
  animationState,
  error,
  onInputChange,
  onSubmitDefinition,
  onClearError,
  onShowAnalysis,
  onStartNewGame
}) => {
  // Helper function to convert animation state for CurrentChallenge
  const getCurrentChallengeAnimationState = (state: AnimationState): 'idle' | 'error' | 'success' => {
    switch (state) {
      case 'typing': return 'idle';
      case 'failure': return 'error';
      case 'success': return 'success';
      default: return 'idle';
    }
  };

  // Helper function to convert animation state for DefinitionInput
  const getDefinitionInputAnimationState = (state: AnimationState): 'pulse' | 'shake' | 'success' | 'idle' => {
    switch (state) {
      case 'typing': return 'pulse';
      case 'failure': return 'shake';
      case 'success': return 'success';
      default: return 'idle';
    }
  };

  // Helper function to calculate revealed target characters
  const getRevealedTarget = (target: string, step: number): string => {
    return target.split('').map((char, i) =>
      i === 0 || i === target.length - 1 || step >= (i + 1) * 3
        ? char
        : '_'
    ).join(' ');
  };

  return (
    <>
      {/* Hero Challenge - Center Stage */}
      {gameState && !isGameOver && gameState.currentWord && (
        <CurrentChallenge
          word={gameState.currentWord}
          step={gameState.step}
          maxSteps={gameState.maxSteps}
          isLoading={isLoading}
          animationState={getCurrentChallengeAnimationState(animationState)}
          showProgress={true}
        />
      )}

      {/* Large Definition Input */}
      {!isGameOver && !showDifficultySelector && gameState && gameState.currentWord && (
        <DefinitionInput
          value={inputValue}
          onChange={onInputChange}
          onSubmit={onSubmitDefinition}
          maxWords={wordCount > 0 ? Math.max(1, calculateMaxWords(
            gameState.step,
            gameState.difficulty,
            gameState.lastDefinitionLength
          )) : 15}
          showHints={true}
          isLoading={isLoading}
          animationState={getDefinitionInputAnimationState(animationState)}
        />
      )}

      {/* Target Revelation Strip */}
      {gameState && gameState.targets.length > 0 && (
        <TargetRevelationStrip
          targets={gameState.targets.map(target => ({
            word: target,
            revealed: getRevealedTarget(target, gameState.step),
            isCompleted: gameState.completedTargets?.includes(target) || false,
            isBurned: gameState.burnedTargets.includes(target)
          }))}
          currentStep={gameState.step}
          revealFrequency={3}
          showProgress={true}
        />
      )}

      {/* Error Display Component */}
      {error && (
        <GameErrorDisplay
          error={error}
          severity="error"
          dismissible={true}
          autoHide={false}
          showAnimations={true}
          onDismiss={onClearError}
        />
      )}

      {/* Game Over Screen Component */}
      {isGameOver && gameState && (
        <GameOverScreen
          gameStatus={gameState.gameStatus === 'won' ? 'won' : 'lost'}
          currentWord={gameState.currentWord || ''}
          step={gameState.step}
          onShowAnalysis={onShowAnalysis}
          onStartNewGame={onStartNewGame}
          showAnimations={true}
        />
      )}
    </>
  );
};

export default GameMainContent;
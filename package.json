{"name": "defeater-ai-mvp", "version": "0.1.0", "private": true, "description": "DEFEATER.AI MVP - The Dark Souls of puzzle games", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:accessibility": "jest tests/accessibility.test.ts", "audit:accessibility": "node scripts/accessibility-audit.js"}, "dependencies": {"axios": "^1.6.2", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "i18next": "^25.2.1", "next": "^14.2.30", "next-i18next": "^15.4.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "axe-core": "^4.10.3", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "jest": "^30.0.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^30.0.0", "jest-html-reporters": "^3.1.7", "postcss": "^8.4.32", "puppeteer": "^24.10.1", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}
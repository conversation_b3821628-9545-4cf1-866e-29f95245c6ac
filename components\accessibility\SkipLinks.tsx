/**
 * SkipLinks Component - WCAG AA Navigation Enhancement
 * 
 * Provides keyboard users with shortcuts to bypass repetitive navigation
 * and jump directly to main content areas.
 * 
 * Features:
 * - Skip to main content
 * - Skip to game input
 * - Skip to side panel
 * - Skip to game controls
 * - Visible only when focused
 * - Proper ARIA labeling
 * - Keyboard accessible
 * 
 * WCAG 2.1 AA Compliance:
 * - 2.4.1 Bypass Blocks
 * - 2.1.1 Keyboard
 * - 2.4.3 Focus Order
 */

import React, { useCallback, useRef, useEffect } from 'react';

interface SkipLink {
  id: string;
  label: string;
  target: string;
  description?: string;
}

interface SkipLinksProps {
  links?: SkipLink[];
  className?: string;
}

const DEFAULT_SKIP_LINKS: SkipLink[] = [
  {
    id: 'skip-to-main',
    label: 'Skip to main content',
    target: '#main-content',
    description: 'Jump to the main game area'
  },
  {
    id: 'skip-to-input',
    label: 'Skip to game input',
    target: '#game-input',
    description: 'Jump directly to definition input field'
  },
  {
    id: 'skip-to-controls',
    label: 'Skip to game controls',
    target: '#game-controls',
    description: 'Jump to game control buttons'
  },
  {
    id: 'skip-to-panel',
    label: 'Skip to side panel',
    target: '#side-panel',
    description: 'Jump to game statistics and information'
  }
];

export const SkipLinks: React.FC<SkipLinksProps> = ({
  links = DEFAULT_SKIP_LINKS,
  className = ''
}) => {
  // Track active event listeners and timeouts for cleanup
  const activeListenersRef = useRef<Set<{ element: HTMLElement; handler: () => void }>>(new Set());
  const activeTimeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  // Cleanup function
  const cleanup = useCallback(() => {
    // Remove all active event listeners
    activeListenersRef.current.forEach(({ element, handler }) => {
      element.removeEventListener('blur', handler);
    });
    activeListenersRef.current.clear();

    // Clear all active timeouts
    activeTimeoutsRef.current.forEach(timeout => {
      clearTimeout(timeout);
    });
    activeTimeoutsRef.current.clear();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const handleSkipClick = useCallback((target: string, event: React.MouseEvent) => {
    event.preventDefault();
    
    const targetElement = document.querySelector(target);
    if (targetElement) {
      // Focus the target element
      if (targetElement instanceof HTMLElement) {
        targetElement.focus();
        
        // If element is not naturally focusable, add tabindex temporarily
        if (!targetElement.hasAttribute('tabindex')) {
          targetElement.setAttribute('tabindex', '-1');

          // Create cleanup handler
          const blurHandler = () => {
            targetElement.removeAttribute('tabindex');
            // Remove from tracking
            activeListenersRef.current.forEach(listener => {
              if (listener.element === targetElement && listener.handler === blurHandler) {
                activeListenersRef.current.delete(listener);
              }
            });
          };

          // Add event listener and track it
          targetElement.addEventListener('blur', blurHandler, { once: true });
          activeListenersRef.current.add({ element: targetElement, handler: blurHandler });
        }
        
        // Scroll into view smoothly
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
        
        // Announce to screen readers
        const announcement = `Skipped to ${targetElement.getAttribute('aria-label') || target}`;
        announceToScreenReader(announcement);
      }
    }
  }, []);

  const handleKeyDown = useCallback((event: React.KeyboardEvent, target: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleSkipClick(target, event as any);
    }
  }, [handleSkipClick]);

  // Screen reader announcement utility
  const announceToScreenReader = useCallback((message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Track timeout for cleanup
    const timeout = setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
      // Remove from tracking
      activeTimeoutsRef.current.delete(timeout);
    }, 1000);

    activeTimeoutsRef.current.add(timeout);
  }, []);

  const skipLinksClasses = [
    'skip-links',
    className
  ].filter(Boolean).join(' ');

  return (
    <>
      <nav className={skipLinksClasses} aria-label="Skip navigation links">
        <ul className="skip-links-list">
          {links.map((link) => (
            <li key={link.id} className="skip-links-item">
              <a
                href={link.target}
                className="skip-link"
                onClick={(e) => handleSkipClick(link.target, e)}
                onKeyDown={(e) => handleKeyDown(e, link.target)}
                aria-describedby={link.description ? `${link.id}-desc` : undefined}
              >
                {link.label}
              </a>
              {link.description && (
                <span id={`${link.id}-desc`} className="sr-only">
                  {link.description}
                </span>
              )}
            </li>
          ))}
        </ul>
      </nav>

      <style jsx>{`
        /* === SKIP LINKS NAVIGATION === */
        .skip-links {
          position: fixed;
          top: 0;
          left: 0;
          z-index: var(--z-notification);
          width: 100%;
          pointer-events: none;
        }

        .skip-links-list {
          display: flex;
          flex-direction: column;
          gap: var(--space-1);
          list-style: none;
          margin: 0;
          padding: var(--space-2);
        }

        .skip-links-item {
          margin: 0;
        }

        /* === SKIP LINK STYLING === */
        .skip-link {
          display: inline-block;
          padding: var(--space-2) var(--space-4);
          background: var(--bg-primary);
          color: var(--accent-cyan);
          border: 2px solid var(--accent-cyan);
          border-radius: var(--radius-lg);
          font-size: var(--text-sm);
          font-weight: var(--font-semibold);
          text-decoration: none;
          white-space: nowrap;
          pointer-events: auto;
          
          /* Initially hidden */
          position: absolute;
          left: -9999px;
          top: -9999px;
          opacity: 0;
          transform: translateY(-100%);
          
          /* Smooth transitions */
          transition: all var(--transition-base);
          
          /* Ensure high contrast */
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
          backdrop-filter: blur(8px);
        }

        /* === FOCUS STATE - VISIBLE === */
        .skip-link:focus {
          position: static;
          left: auto;
          top: auto;
          opacity: 1;
          transform: translateY(0);
          
          /* Enhanced focus styling */
          outline: 3px solid var(--accent-cyan);
          outline-offset: 2px;
          box-shadow: 
            0 4px 12px rgba(0, 0, 0, 0.5),
            0 0 0 3px rgba(6, 182, 212, 0.3);
        }

        .skip-link:hover {
          background: var(--glass-light);
          border-color: var(--accent-cyan);
          box-shadow: 
            0 6px 16px rgba(0, 0, 0, 0.6),
            var(--glow-cyan);
        }

        .skip-link:active {
          transform: translateY(1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
        }

        /* === SCREEN READER ONLY === */
        .sr-only {
          position: absolute;
          width: 1px;
          height: 1px;
          padding: 0;
          margin: -1px;
          overflow: hidden;
          clip: rect(0, 0, 0, 0);
          white-space: nowrap;
          border: 0;
        }

        /* === HIGH CONTRAST MODE === */
        @media (prefers-contrast: high) {
          .skip-link {
            background: var(--bg-primary);
            border-width: 3px;
            border-color: var(--text-primary);
            color: var(--text-primary);
          }

          .skip-link:focus {
            outline-width: 4px;
            outline-color: var(--text-primary);
          }
        }

        /* === REDUCED MOTION === */
        @media (prefers-reduced-motion: reduce) {
          .skip-link {
            transition: none;
          }

          .skip-link:focus {
            transform: none;
          }
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .skip-links-list {
            padding: var(--space-1);
            gap: var(--space-1);
          }

          .skip-link {
            padding: var(--space-2) var(--space-3);
            font-size: var(--text-xs);
          }
        }

        /* === PRINT STYLES === */
        @media print {
          .skip-links {
            display: none;
          }
        }
      `}</style>
    </>
  );
};

export default SkipLinks;

# 🎯 **DEFEATER.AI**

> **Outsmart the AI. Define your way to victory.**

A sophisticated word-definition game where players engage in psychological warfare with an AI opponent. Built with Next.js, TypeScript, and a cutting-edge spatial design system.

## 🚀 **Quick Start**

```bash
# Clone the repository
git clone https://github.com/Aladin147/DefeaterAI.git
cd DefeaterAI

# Install dependencies
npm install

# Start development server
npm run dev

# Open http://localhost:3000
```

## 🎮 **Game Overview**

DEFEATER.AI is a strategic word game where players must:
- **Define words** to guide the AI toward target words
- **Avoid detection** while steering the conversation
- **Manage limited word budgets** strategically
- **Outsmart AI predictions** through psychological gameplay

### **Core Mechanics**
- **Target System**: Progressive revelation (first/last letters + hints)
- **Word Budget**: Difficulty-based limits (Easy: 30, Medium: 25, Hard: 20)
- **AI Opponent**: Powered by <PERSON>llama with Gemma3 model
- **Win Conditions**: Include target word in definition within step limit
- **Psychological Warfare**: Real-time AI trash talk and strategic mind games

## 🏗️ **Architecture**

### **Technology Stack**
- **Frontend**: Next.js 14, TypeScript, React 18
- **Styling**: Spatial Design System, CSS Modules, Tailwind CSS
- **AI Integration**: Ollama API with Gemma3 model
- **Testing**: Jest, React Testing Library, Accessibility Testing
- **Performance**: Optimized bundles, code splitting, lazy loading

### **Design System**
- **Spatial Layout**: Open, breathing room design
- **Glass Effects**: Backdrop blur, transparency layers
- **Typography**: Bold, confident, high-contrast text
- **Accessibility**: WCAG 2.1 AA compliant
- **Performance**: Optimized for low-end devices

## 📁 **Project Structure**

```
DefeaterAI/
├── components/           # React components
│   ├── game/            # Game-specific components
│   ├── ui/              # Reusable UI components
│   ├── layout/          # Layout components
│   └── chat/            # AI chat system
├── hooks/               # Custom React hooks
├── utils/               # Utility functions
├── styles/              # CSS and design system
├── pages/               # Next.js pages
├── docs/                # Documentation
└── tests/               # Test suites
```

## 🧪 **Testing**

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run accessibility tests
npm run test:accessibility

# Build for production
npm run build
```

### **Test Coverage**
- **37/37 tests passing** (100% success rate)
- **WCAG 2.1 AA compliance** validated
- **Performance optimization** tested
- **Component integration** verified

## 🎨 **Features**

### **Gameplay Features**
- ✅ **Strategic Word Definition** - Guide AI through clever definitions
- ✅ **Progressive Target Revelation** - Hints unlock over time
- ✅ **Difficulty Scaling** - Multiple challenge levels
- ✅ **AI Trash Talk** - Real-time psychological warfare
- ✅ **Performance Analytics** - Track your strategic success

### **Technical Features**
- ✅ **Spatial Design System** - Modern, accessible UI
- ✅ **Real-time AI Integration** - Ollama/Gemma3 powered
- ✅ **Performance Optimized** - Fast loading, smooth gameplay
- ✅ **Accessibility First** - Screen reader support, keyboard navigation
- ✅ **Responsive Design** - Works on all devices

## 🔧 **Development**

### **Prerequisites**
- Node.js 18+ 
- npm or yarn
- Ollama (for AI integration)

### **Environment Setup**
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull Gemma3 model
ollama pull gemma3:4b

# Set up environment variables
cp .env.example .env.local
```

### **Available Scripts**
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm test` - Run test suite
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks

## 📚 **Documentation**

Comprehensive documentation is available in the `/docs` directory:

- **[START_HERE.md](docs/START_HERE.md)** - Project overview and setup
- **[TECHNICAL_ARCHITECTURE.md](docs/TECHNICAL_ARCHITECTURE.md)** - System architecture
- **[MVP_CORE_MECHANICS.md](docs/MVP_CORE_MECHANICS.md)** - Game mechanics
- **[API_REFERENCE.md](docs/API_REFERENCE.md)** - API documentation
- **[TESTING_REPORT.md](docs/TESTING_REPORT.md)** - Testing strategy

## 🎯 **Game Strategy**

### **Winning Tips**
1. **Start Broad** - Use general definitions early
2. **Misdirect** - Lead AI away from obvious targets
3. **Budget Wisely** - Save words for critical moments
4. **Read AI Patterns** - Learn from AI responses
5. **Stay Unpredictable** - Vary your definition strategies

### **Difficulty Levels**
- **Easy**: 30 steps, forgiving word reuse
- **Medium**: 25 steps, moderate restrictions
- **Hard**: 20 steps, strict word management

## 🤝 **Contributing**

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Ensure all tests pass
6. Submit a pull request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 **Acknowledgments**

- **Ollama Team** - For the excellent local AI infrastructure
- **Google** - For the Gemma3 model
- **Next.js Team** - For the amazing React framework
- **Accessibility Community** - For WCAG guidelines and tools

---

**Built with ❤️ by the DEFEATER.AI team**

*Ready to challenge the AI? [Start playing now!](http://localhost:3000)*

/**
 * Memory Management Utilities
 * 
 * Comprehensive utilities for tracking and preventing memory leaks
 * in React components and global resources.
 */

interface ResourceTracker {
  id: string;
  type: 'timer' | 'listener' | 'observer' | 'subscription';
  resource: any;
  cleanup: () => void;
  createdAt: number;
  component?: string;
}

interface MemoryMetrics {
  activeResources: number;
  totalCreated: number;
  totalCleaned: number;
  leakWarnings: number;
  memoryUsage?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

class MemoryManager {
  private static instance: MemoryManager;
  private resources: Map<string, ResourceTracker> = new Map();
  private metrics: MemoryMetrics = {
    activeResources: 0,
    totalCreated: 0,
    totalCleaned: 0,
    leakWarnings: 0
  };
  private leakCheckInterval: NodeJS.Timeout | null = null;

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  constructor() {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      this.startLeakDetection();
    }
  }

  /**
   * Track a resource for cleanup
   */
  trackResource(
    type: ResourceTracker['type'],
    resource: any,
    cleanup: () => void,
    component?: string
  ): string {
    const id = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const tracker: ResourceTracker = {
      id,
      type,
      resource,
      cleanup,
      createdAt: Date.now(),
      component
    };

    this.resources.set(id, tracker);
    this.metrics.activeResources++;
    this.metrics.totalCreated++;

    if (process.env.NODE_ENV === 'development') {
      console.debug(`🧠 MemoryManager: Tracking ${type} resource`, {
        id,
        component,
        activeResources: this.metrics.activeResources
      });
    }

    return id;
  }

  /**
   * Clean up a specific resource
   */
  cleanupResource(id: string): boolean {
    const tracker = this.resources.get(id);
    if (!tracker) {
      return false;
    }

    try {
      tracker.cleanup();
      this.resources.delete(id);
      this.metrics.activeResources--;
      this.metrics.totalCleaned++;

      if (process.env.NODE_ENV === 'development') {
        console.debug(`🧠 MemoryManager: Cleaned up ${tracker.type} resource`, {
          id,
          component: tracker.component,
          activeResources: this.metrics.activeResources
        });
      }

      return true;
    } catch (error) {
      console.error(`🧠 MemoryManager: Error cleaning up resource ${id}:`, error);
      return false;
    }
  }

  /**
   * Clean up all resources for a component
   */
  cleanupComponent(component: string): number {
    let cleaned = 0;
    const toCleanup: string[] = [];

    this.resources.forEach((tracker, id) => {
      if (tracker.component === component) {
        toCleanup.push(id);
      }
    });

    toCleanup.forEach(id => {
      if (this.cleanupResource(id)) {
        cleaned++;
      }
    });

    if (process.env.NODE_ENV === 'development' && cleaned > 0) {
      console.debug(`🧠 MemoryManager: Cleaned up ${cleaned} resources for component ${component}`);
    }

    return cleaned;
  }

  /**
   * Clean up all resources
   */
  cleanupAll(): number {
    let cleaned = 0;
    const allIds = Array.from(this.resources.keys());

    allIds.forEach(id => {
      if (this.cleanupResource(id)) {
        cleaned++;
      }
    });

    if (process.env.NODE_ENV === 'development') {
      console.debug(`🧠 MemoryManager: Cleaned up all ${cleaned} resources`);
    }

    return cleaned;
  }

  /**
   * Get current memory metrics
   */
  getMetrics(): MemoryMetrics {
    const metrics = { ...this.metrics };

    // Add browser memory info if available
    if (typeof window !== 'undefined' && (performance as any).memory) {
      metrics.memoryUsage = {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
      };
    }

    return metrics;
  }

  /**
   * Start automatic leak detection
   */
  private startLeakDetection(): void {
    this.leakCheckInterval = setInterval(() => {
      this.checkForLeaks();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Check for potential memory leaks
   */
  private checkForLeaks(): void {
    const now = Date.now();
    const oldResources: ResourceTracker[] = [];

    this.resources.forEach(tracker => {
      const age = now - tracker.createdAt;
      // Resources older than 5 minutes might be leaks
      if (age > 5 * 60 * 1000) {
        oldResources.push(tracker);
      }
    });

    if (oldResources.length > 0) {
      this.metrics.leakWarnings++;
      console.warn(`🧠 MemoryManager: Potential memory leaks detected`, {
        count: oldResources.length,
        resources: oldResources.map(r => ({
          id: r.id,
          type: r.type,
          component: r.component,
          ageMinutes: Math.round((now - r.createdAt) / 60000)
        }))
      });
    }

    // Log memory usage if available
    const metrics = this.getMetrics();
    if (metrics.memoryUsage) {
      const usedMB = Math.round(metrics.memoryUsage.usedJSHeapSize / 1024 / 1024);
      const totalMB = Math.round(metrics.memoryUsage.totalJSHeapSize / 1024 / 1024);
      
      console.debug(`🧠 MemoryManager: Memory usage: ${usedMB}MB / ${totalMB}MB`, {
        activeResources: metrics.activeResources,
        totalCreated: metrics.totalCreated,
        totalCleaned: metrics.totalCleaned
      });
    }
  }

  /**
   * Stop leak detection and cleanup
   */
  destroy(): void {
    if (this.leakCheckInterval) {
      clearInterval(this.leakCheckInterval);
      this.leakCheckInterval = null;
    }
    this.cleanupAll();
  }
}

// Export singleton instance
export const memoryManager = MemoryManager.getInstance();

/**
 * React hook for automatic resource cleanup
 */
export function useMemoryCleanup(component: string) {
  const trackResource = (
    type: ResourceTracker['type'],
    resource: any,
    cleanup: () => void
  ): string => {
    return memoryManager.trackResource(type, resource, cleanup, component);
  };

  const cleanupComponent = () => {
    return memoryManager.cleanupComponent(component);
  };

  return {
    trackResource,
    cleanupComponent,
    getMetrics: () => memoryManager.getMetrics()
  };
}

/**
 * Enhanced setTimeout with automatic tracking
 */
export function managedSetTimeout(
  callback: () => void,
  delay: number,
  component?: string
): string {
  const timer = setTimeout(callback, delay);
  
  return memoryManager.trackResource(
    'timer',
    timer,
    () => clearTimeout(timer),
    component
  );
}

/**
 * Enhanced setInterval with automatic tracking
 */
export function managedSetInterval(
  callback: () => void,
  delay: number,
  component?: string
): string {
  const interval = setInterval(callback, delay);
  
  return memoryManager.trackResource(
    'timer',
    interval,
    () => clearInterval(interval),
    component
  );
}

/**
 * Enhanced addEventListener with automatic tracking
 */
export function managedAddEventListener(
  element: EventTarget,
  event: string,
  handler: EventListener,
  options?: boolean | AddEventListenerOptions,
  component?: string
): string {
  element.addEventListener(event, handler, options);
  
  return memoryManager.trackResource(
    'listener',
    { element, event, handler },
    () => element.removeEventListener(event, handler, options),
    component
  );
}

export default memoryManager;

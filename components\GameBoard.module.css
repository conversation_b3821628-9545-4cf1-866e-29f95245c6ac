/**
 * GameBoard CSS Module - Spatial Design System (v2.0)
 *
 * 🎯 WEEK 6 DAY 1-2: FINAL GAMEBOARD MIGRATION
 * 
 * Migrated from 58 lines of styled-jsx to optimized CSS module
 * Benefits:
 * - Better performance (no runtime CSS generation)
 * - Improved bundle optimization
 * - Enhanced caching and reusability
 * - Complete spatial design system integration
 * 
 * @version 2.0 - Final GameBoard Migration
 * @see docs/WEEK_6_GAMEBOARD_COMPLETION.md
 */

/* === SPATIAL GAME LAYOUT === */
.spatialGameLayout {
  display: grid;
  grid-template-columns: 1fr auto;
  min-height: 100vh;
  gap: 0;
  position: relative;
  
  /* Performance optimizations */
  contain: layout style;
  transform: translateZ(0);
}

/* === GAME HEADER === */
.gameHeader {
  text-align: center;
  margin-bottom: var(--space-8);
}

.gameSubtitle {
  margin: var(--space-4) 0 var(--space-8);
  opacity: 0.8;
}

/* === GAME CONTROLS === */
.gameControls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-3);
  flex-wrap: wrap;
  margin-top: var(--space-6);
}

/* === LOADING CARD === */
.loadingCard {
  /* Spatial Design System - Glass Effect */
  background: var(--glass-medium);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-dark-soft);
  backdrop-filter: blur(8px);
  max-width: 400px;
  margin: var(--space-8) auto;
  
  /* Performance optimizations */
  contain: layout style;
  transform: translateZ(0);
}

.loadingText {
  margin-top: var(--space-4);
  color: var(--color-muted-foreground);
}

/* === RESPONSIVE DESIGN === */

/* Tablet and mobile layout */
@media (max-width: 1024px) {
  .spatialGameLayout {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }
}

/* Mobile optimizations */
@media (max-width: 767px) {
  .gameControls {
    gap: var(--space-2);
  }
  
  .loadingCard {
    padding: var(--space-6);
    margin: var(--space-4) auto;
    max-width: 350px;
  }
  
  .gameHeader {
    margin-bottom: var(--space-6);
  }
  
  .gameSubtitle {
    margin: var(--space-3) 0 var(--space-6);
  }
}

/* === ACCESSIBILITY ENHANCEMENTS === */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .spatialGameLayout,
  .loadingCard {
    transform: none;
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .loadingCard {
    border-width: 3px;
    background: var(--bg-primary);
  }
}

/* Focus management */
.spatialGameLayout:focus-within {
  outline: none;
}

/* === PERFORMANCE OPTIMIZATIONS === */

/* GPU acceleration for smooth interactions */
.spatialGameLayout {
  will-change: auto;
}

.loadingCard {
  will-change: auto;
}

/* Content visibility optimization */
.spatialGameLayout {
  content-visibility: auto;
  contain-intrinsic-size: 100vh;
}

/* Reduce layout thrashing */
.gameControls {
  contain: layout;
}

/* === PRINT STYLES === */
@media print {
  .spatialGameLayout {
    grid-template-columns: 1fr;
    min-height: auto;
    gap: var(--space-4);
  }
  
  .loadingCard {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    border: 1px solid black !important;
  }
  
  .gameControls {
    display: none;
  }
}

/* === DARK MODE OPTIMIZATION === */
@media (prefers-color-scheme: dark) {
  /* DEFEATER.AI is dark-theme by default, but ensure consistency */
  .loadingCard {
    background: var(--glass-medium);
  }
}

/* === LOW-END DEVICE OPTIMIZATIONS === */

/* Simplified styles for performance-constrained devices */
.performanceOptimized .spatialGameLayout {
  transform: none;
  will-change: auto;
}

.performanceOptimized .loadingCard {
  backdrop-filter: none;
  box-shadow: var(--shadow-dark-soft);
  transform: none;
}

/* === ANIMATION STATES === */

/* Loading animation states */
.loadingCard.animating {
  animation: loadingPulse 2s ease-in-out infinite;
}

@keyframes loadingPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Disable animations for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .loadingCard.animating {
    animation: none;
  }
}

/* === SPATIAL DESIGN INTEGRATION === */

/* Ensure compatibility with spatial design components */
.spatialGameLayout > * {
  /* Allow spatial components to manage their own spacing */
  margin: 0;
}

/* Grid area definitions for complex layouts */
.spatialGameLayout.withSidePanel {
  grid-template-areas: 
    "main sidebar"
    "main sidebar";
}

.spatialGameLayout.mobileSidePanel {
  grid-template-areas: 
    "main"
    "sidebar";
}

/* === COMPONENT INTEGRATION === */

/* Ensure proper integration with child components */
.spatialGameLayout .game-focus,
.spatialGameLayout .game-display,
.spatialGameLayout .game-input {
  /* Child components handle their own styling */
  position: relative;
}

/* === DEBUGGING SUPPORT === */

/* Development mode indicators */
.spatialGameLayout.development {
  outline: 2px dashed var(--color-warning);
  outline-offset: 4px;
}

.spatialGameLayout.development::before {
  content: 'GameBoard (Spatial v2.0)';
  position: absolute;
  top: 0;
  left: 0;
  background: var(--color-warning);
  color: var(--color-background);
  padding: var(--space-1) var(--space-2);
  font-size: 0.75rem;
  font-weight: 600;
  z-index: var(--z-notification);
}

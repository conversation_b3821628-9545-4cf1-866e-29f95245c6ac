/**
 * DEFEATER.AI Game Logic
 *
 * Core game mechanics, validation, and state management.
 * Handles all game rules, state transitions, and win/loss detection.
 *
 * @module gameLogic
 * @version 1.0.0
 * <AUTHOR> Team
 */

import { GameState, Definition, ValidationResult, ValidationError, TurnValidationResult, DifficultyLevel, RejectionRecord, GAME_RULES } from '@/types/game';
import { checkWordReuse } from './wordSimilarity';
import { DIFFICULTY_CONFIG, VALIDATION, GAME_BALANCE } from './constants';

/**
 * Calculate maximum allowed words based on difficulty and game state
 * Implements MVP Core Mechanics specification
 */
export function calculateMaxWords(
  turn: number,
  difficulty: DifficultyLevel,
  lastDefinitionLength?: number
): number {
  const config = DIFFICULTY_CONFIG[difficulty];

  switch (config.wordCountRule) {
    case 'countdown':
      // Easy mode: Fixed countdown (Turn 1 = 10 words, Turn 2 = 9 words, etc.)
      return Math.max(1, config.initialWordCount - turn + 1);

    case 'shorter':
      // Medium mode: Must be shorter than previous definition
      if (!lastDefinitionLength) {
        return config.initialWordCount; // First definition
      }
      return Math.max(1, lastDefinitionLength - 1);

    case 'aggressive':
      // Hard mode: Aggressive reduction after turn 5
      if (!lastDefinitionLength) {
        return config.initialWordCount; // First definition
      }

      if (turn <= 5) {
        return Math.max(1, lastDefinitionLength - 1);
      } else {
        // After turn 5: reduce by 2 words each turn
        return Math.max(1, lastDefinitionLength - 2);
      }

    default:
      return 10; // Default fallback
  }
}

/**
 * Check if a word is a common word that gets reuse allowances
 */
export function isCommonWord(word: string): boolean {
  return VALIDATION.COMMON_WORDS.includes(word.toLowerCase() as any);
}

/**
 * Get remaining uses for a common word based on difficulty
 */
export function getRemainingCommonWordUses(
  word: string,
  currentUsage: number,
  difficulty: DifficultyLevel
): number {
  if (!isCommonWord(word)) {
    return 0; // Not a common word
  }

  const config = DIFFICULTY_CONFIG[difficulty];
  const limit = config.commonWordLimit;

  if (limit === Infinity) {
    return Infinity; // Unlimited in easy mode
  }

  return Math.max(0, limit - currentUsage);
}

/**
 * Get common words usage summary for UI display
 */
export function getCommonWordsUsageSummary(
  commonWordsUsage: Record<string, number>,
  difficulty: DifficultyLevel
): Array<{ word: string; used: number; limit: number; remaining: number }> {
  const config = DIFFICULTY_CONFIG[difficulty];
  const limit = config.commonWordLimit;

  return Object.entries(commonWordsUsage).map(([word, used]) => ({
    word,
    used,
    limit: limit === Infinity ? -1 : limit, // -1 indicates unlimited
    remaining: limit === Infinity ? -1 : Math.max(0, limit - used)
  }));
}

/**
 * Get rejection summary for three-strike system
 */
export function getRejectionSummary(gameState: GameState): {
  currentStrikes: number;
  maxStrikes: number;
  remainingStrikes: number;
  recentRejections: RejectionRecord[];
  isAtRisk: boolean;
} {
  const maxStrikes = VALIDATION.MAX_CONSECUTIVE_REJECTIONS;
  const currentStrikes = gameState.consecutiveRejections;
  const remainingStrikes = Math.max(0, maxStrikes - currentStrikes);

  // Get the 3 most recent rejections for context
  const recentRejections = (gameState.rejectionHistory || [])
    .slice(-3)
    .sort((a, b) => b.timestamp - a.timestamp);

  return {
    currentStrikes,
    maxStrikes,
    remainingStrikes,
    recentRejections,
    isAtRisk: remainingStrikes <= 1 && remainingStrikes > 0
  };
}

/**
 * Check if the game should end due to consecutive rejections
 */
export function checkRejectionGameEnd(gameState: GameState): {
  shouldEnd: boolean;
  reason?: string;
} {
  if (gameState.consecutiveRejections >= VALIDATION.MAX_CONSECUTIVE_REJECTIONS) {
    return {
      shouldEnd: true,
      reason: `Game over: ${VALIDATION.MAX_CONSECUTIVE_REJECTIONS} consecutive rule violations`
    };
  }

  return { shouldEnd: false };
}

/**
 * Get rejection statistics for analytics
 */
export function getRejectionStats(gameState: GameState): {
  totalRejections: number;
  rejectionsByType: Record<string, number>;
  averageRejectionTurn: number;
  rejectionRate: number;
} {
  const totalRejections = (gameState.rejectionHistory || []).length;
  const rejectionsByType: Record<string, number> = {};

  let totalTurns = 0;
  (gameState.rejectionHistory || []).forEach(rejection => {
    rejectionsByType[rejection.violationType] = (rejectionsByType[rejection.violationType] || 0) + 1;
    totalTurns += rejection.turn;
  });

  const averageRejectionTurn = totalRejections > 0 ? totalTurns / totalRejections : 0;
  const rejectionRate = gameState.step > 0 ? totalRejections / gameState.step : 0;

  return {
    totalRejections,
    rejectionsByType,
    averageRejectionTurn,
    rejectionRate
  };
}

/**
 * Check word reuse with common word allowances based on difficulty
 */
export function checkWordReuseWithAllowances(
  newWords: string[],
  usedWords: string[],
  commonWordsUsage: Record<string, number>,
  difficulty: DifficultyLevel
): { valid: boolean; reason?: string; violatingWord?: string } {

  // Input validation
  if (!newWords || newWords.length === 0) {
    return { valid: true }; // Empty input is valid
  }

  const config = DIFFICULTY_CONFIG[difficulty];
  const usedWordsSet = new Set(usedWords.map(w => w.toLowerCase()));

  for (const word of newWords) {
    // Skip empty or whitespace-only words
    if (!word || word.trim().length === 0) {
      continue;
    }

    const normalizedWord = word.toLowerCase().trim();

    if (isCommonWord(normalizedWord)) {
      // Check common word usage limit
      const currentUsage = commonWordsUsage[normalizedWord] || 0;

      // Handle unlimited case (easy mode)
      if (config.commonWordLimit === Infinity) {
        continue; // Unlimited reuse allowed
      }

      if (currentUsage >= config.commonWordLimit) {
        const limitText = config.commonWordLimit === Infinity ? 'unlimited' : `${config.commonWordLimit} uses`;
        return {
          valid: false,
          reason: `Common word "${word}" exceeded usage limit (${limitText})`,
          violatingWord: word
        };
      }
    } else {
      // Check regular word reuse
      if (usedWordsSet.has(normalizedWord)) {
        return {
          valid: false,
          reason: `Word "${word}" was already used`,
          violatingWord: word
        };
      }

      // Check similarity-based reuse (existing function)
      const similarityCheck = checkWordReuse(word, usedWords);
      if (similarityCheck.isReuse) {
        return {
          valid: false,
          reason: similarityCheck.reason,
          violatingWord: word
        };
      }
    }
  }

  return { valid: true };
}

/**
 * Validates a player's definition according to game rules
 */
export function validateDefinition(
  definition: string,
  gameState: GameState,
  previousDefinition?: Definition
): ValidationResult {
  const errors: ValidationError[] = [];
  const words = definition.trim().toLowerCase().split(/\s+/).filter(word => word.length > 0);
  const wordCount = words.length;

  // Rule A: Word count check based on difficulty (MVP Core Mechanics)
  const maxAllowed = calculateMaxWords(
    gameState.step,
    gameState.difficulty,
    gameState.lastDefinitionLength
  );

  if (wordCount > maxAllowed) {
    errors.push({
      type: 'length',
      message: `Definition must be ${maxAllowed} word${maxAllowed === 1 ? '' : 's'} or fewer. You used ${wordCount} words.`
    });
  }

  // Rule B: Word reuse check with common word allowances (MVP Core Mechanics)
  const reuseCheck = checkWordReuseWithAllowances(
    words,
    gameState.usedWords,
    gameState.commonWordsUsage,
    gameState.difficulty
  );

  if (!reuseCheck.valid) {
    errors.push({
      type: 'reuse',
      message: reuseCheck.reason || 'Word reuse detected',
      word: reuseCheck.violatingWord
    });
  }

  // Rule 3: Must not be empty or too short
  if (wordCount === 0) {
    errors.push({
      type: 'invalid',
      message: 'Definition cannot be empty'
    });
  }

  // Rule 4: No circular definitions (word cannot appear in its own definition)
  const targetWord = gameState.currentWord?.toLowerCase() || '';
  if (targetWord && words.includes(targetWord)) {
    errors.push({
      type: 'circular',
      message: `Cannot use "${targetWord}" in its own definition.`
    });
  }

  // Note: Semantic validation is now handled entirely by the AI Game Master
  // This ensures the AI has full control over what constitutes a valid definition
  // No character length restrictions - AI decides if definition is valid

  return {
    isValid: errors.length === 0,
    errors,
    wordCount,
    usedWords: words,
    maxAllowedWords: maxAllowed
  };
}

// Semantic validation removed - AI Game Master now has full control over definition acceptance

/**
 * Creates a new game state with MVP Core Mechanics
 */
export function createNewGame(difficulty: DifficultyLevel = 'medium'): GameState {
  const config = DIFFICULTY_CONFIG[difficulty];

  return {
    gameId: generateGameId(),
    currentWord: null,
    targets: [],
    burnedTargets: [],
    definitions: [],
    usedWords: [],
    aiChallengeWords: [], // Track AI's challenge word history
    step: 0,
    maxSteps: config.maxSteps,
    gameStatus: 'waiting',
    difficulty: difficulty,
    // New MVP fields
    consecutiveRejections: 0,
    commonWordsUsage: {},
    lastDefinitionLength: undefined,
    rejectionHistory: []
  };
}

/**
 * Updates game state with a new definition (MVP Core Mechanics)
 */
export function updateGameState(
  gameState: GameState,
  definition: string,
  nextWord: string,
  isValid: boolean = true,
  rejectionReason?: string,
  violationType?: 'length' | 'reuse' | 'circular' | 'invalid'
): GameState {
  if (!isValid) {
    // Create rejection record
    const rejectionRecord: RejectionRecord = {
      turn: gameState.step,
      reason: rejectionReason || 'Unknown validation error',
      definition: definition.trim(),
      timestamp: Date.now(),
      violationType: violationType || 'invalid'
    };

    const newRejectionCount = gameState.consecutiveRejections + 1;
    const newRejectionHistory = [...(gameState.rejectionHistory || []), rejectionRecord];

    // Check if this causes a game loss
    const gameStatus = newRejectionCount >= VALIDATION.MAX_CONSECUTIVE_REJECTIONS ? 'lost' : gameState.gameStatus;

    return {
      ...gameState,
      consecutiveRejections: newRejectionCount,
      rejectionHistory: newRejectionHistory,
      gameStatus
    };
  }

  const words = definition.trim().toLowerCase().split(/\s+/).filter(word => word.length > 0);

  // Update common words usage tracking
  const newCommonWordsUsage = { ...gameState.commonWordsUsage };
  words.forEach(word => {
    if (isCommonWord(word)) {
      const currentUsage = newCommonWordsUsage[word] || 0;
      newCommonWordsUsage[word] = currentUsage + 1;

      // Debug logging for development
      if (process.env.NODE_ENV === 'development') {
        const config = DIFFICULTY_CONFIG[gameState.difficulty];
        const remaining = config.commonWordLimit === Infinity ? 'unlimited' :
          Math.max(0, config.commonWordLimit - newCommonWordsUsage[word]);
        console.debug(`📝 Common word "${word}": ${newCommonWordsUsage[word]}/${config.commonWordLimit === Infinity ? '∞' : config.commonWordLimit} (${remaining} remaining)`);
      }
    }
  });

  const newDefinition: Definition = {
    id: generateDefinitionId(),
    word: gameState.currentWord || '',
    definition: definition.trim(),
    wordCount: words.length,
    timestamp: Date.now(),
    isValid: true
  };

  return {
    ...gameState,
    currentWord: nextWord,
    definitions: [...gameState.definitions, newDefinition],
    usedWords: [...gameState.usedWords, ...words],
    aiChallengeWords: [...gameState.aiChallengeWords, nextWord.toLowerCase()], // Track AI's word choice
    step: gameState.step + 1,
    gameStatus: gameState.step + 1 > gameState.maxSteps ? 'lost' : 'waiting',
    // Reset rejection counter on successful definition
    consecutiveRejections: 0,
    // Update tracking fields
    commonWordsUsage: newCommonWordsUsage,
    lastDefinitionLength: words.length
  };
}

/**
 * DETERMINISTIC WIN DETECTION - MVP Core Mechanics
 * Player wins when their definition contains any active target word
 */
export function checkWinCondition(definition: string, gameState: GameState): {
  isWin: boolean;
  winType: 'target_in_definition' | 'none';
  targetWord?: string;
  reason?: string;
} {
  // Get active (non-burned) targets
  const activeTargets = gameState.targets.filter(target =>
    !gameState.burnedTargets.includes(target)
  );

  if (activeTargets.length === 0) {
    return {
      isWin: false,
      winType: 'none',
      reason: 'No active targets remaining'
    };
  }

  // Normalize definition for checking
  const normalizedDefinition = definition.toLowerCase().trim();
  const definitionWords = normalizedDefinition.split(/\s+/).filter(word => word.length > 0);

  // Check if any active target appears in the definition
  for (const target of activeTargets) {
    const normalizedTarget = target.toLowerCase();

    // Check if target word appears as a complete word in definition
    if (definitionWords.includes(normalizedTarget)) {
      if (process.env.NODE_ENV === 'development') {
        console.debug('🎯 DETERMINISTIC WIN DETECTED:', {
          definition: definition,
          targetFound: target,
          activeTargets: activeTargets,
          definitionWords: definitionWords
        });
      }

      return {
        isWin: true,
        winType: 'target_in_definition',
        targetWord: target,
        reason: `Definition contains target word "${target}"`
      };
    }
  }

  return {
    isWin: false,
    winType: 'none',
    reason: 'No target words found in definition'
  };
}

/**
 * DETERMINISTIC LOSS DETECTION - MVP Core Mechanics
 */
export function checkLossCondition(gameState: GameState): {
  isLoss: boolean;
  lossType: 'step_limit' | 'three_strikes' | 'impossible' | 'none';
  reason?: string;
} {
  // Check three strikes (already implemented)
  if (gameState.consecutiveRejections >= VALIDATION.MAX_CONSECUTIVE_REJECTIONS) {
    return {
      isLoss: true,
      lossType: 'three_strikes',
      reason: `${VALIDATION.MAX_CONSECUTIVE_REJECTIONS} consecutive rule violations`
    };
  }

  // Check step limit (use > instead of >= to be consistent with game flow)
  if (gameState.step > gameState.maxSteps) {
    return {
      isLoss: true,
      lossType: 'step_limit',
      reason: `Exceeded maximum steps (${gameState.maxSteps})`
    };
  }

  // Check if game is impossible
  const activeTargets = gameState.targets.filter(target =>
    !gameState.burnedTargets.includes(target)
  );

  if (activeTargets.length === 0) {
    return {
      isLoss: true,
      lossType: 'impossible',
      reason: 'All targets have been burned'
    };
  }

  return {
    isLoss: false,
    lossType: 'none'
  };
}

/**
 * LEGACY FUNCTION - Kept for backward compatibility
 * @deprecated Use checkWinCondition(definition, gameState) instead
 */
export function checkSemanticWin(gameState: GameState, currentWord: string, definition: string): {
  isWin: boolean;
  winType: 'direct' | 'semantic' | 'none';
  targetWord?: string;
  confidence?: number;
} {
  // Use the new deterministic win detection
  const winResult = checkWinCondition(definition, gameState);

  return {
    isWin: winResult.isWin,
    winType: winResult.isWin ? 'semantic' : 'none',
    targetWord: winResult.targetWord,
    confidence: winResult.isWin ? 1.0 : 0.0
  };
}

/**
 * Burns a target word (removes it from available targets)
 */
export function burnTarget(gameState: GameState, targetWord: string): GameState {
  if (!gameState.targets.includes(targetWord) || gameState.burnedTargets.includes(targetWord)) {
    return gameState;
  }

  return {
    ...gameState,
    burnedTargets: [...gameState.burnedTargets, targetWord]
  };
}

/**
 * Gets remaining available targets
 */
export function getRemainingTargets(gameState: GameState): string[] {
  return gameState.targets.filter(target => !gameState.burnedTargets.includes(target));
}

/**
 * PROGRESSIVE TARGET REVELATION - MVP Core Mechanics
 * Reveals target letters based on difficulty and turn progression
 */
export function getRevealedTargetPattern(
  targetWord: string,
  currentStep: number,
  difficulty: DifficultyLevel
): string {
  const config = DIFFICULTY_CONFIG[difficulty];
  const revealFrequency = config.revealFrequency;

  // Always show first and last letters
  if (targetWord.length <= 2) {
    return targetWord; // Short words are fully revealed
  }

  // Calculate how many letters to reveal based on step
  const revealTurns = Math.floor(currentStep / revealFrequency);
  const lettersToReveal = Math.min(revealTurns, targetWord.length - 2); // Don't exceed available positions

  // Create pattern array
  const pattern = Array(targetWord.length).fill('_');

  // Always reveal first and last
  pattern[0] = targetWord[0];
  pattern[targetWord.length - 1] = targetWord[targetWord.length - 1];

  // Reveal additional letters strategically
  if (lettersToReveal > 0) {
    // Strategy: Reveal vowels first, then strategic consonants
    const vowels = ['a', 'e', 'i', 'o', 'u'];
    const availablePositions = [];

    // Find positions that can be revealed (excluding first and last)
    for (let i = 1; i < targetWord.length - 1; i++) {
      availablePositions.push(i);
    }

    // Sort positions by priority: vowels first, then by position
    availablePositions.sort((a, b) => {
      const aIsVowel = vowels.includes(targetWord[a].toLowerCase());
      const bIsVowel = vowels.includes(targetWord[b].toLowerCase());

      if (aIsVowel && !bIsVowel) {
      return -1;
    }
      if (!aIsVowel && bIsVowel) {
      return 1;
    }

      // If both are vowels or both are consonants, prefer middle positions
      const aMidDistance = Math.abs(a - targetWord.length / 2);
      const bMidDistance = Math.abs(b - targetWord.length / 2);
      return aMidDistance - bMidDistance;
    });

    // Reveal the calculated number of letters
    for (let i = 0; i < Math.min(lettersToReveal, availablePositions.length); i++) {
      const position = availablePositions[i];
      pattern[position] = targetWord[position];
    }
  }

  return pattern.join(' ');
}

/**
 * Get all revealed target patterns for display
 */
export function getRevealedTargets(gameState: GameState): Array<{
  word: string;
  pattern: string;
  isBurned: boolean;
  isFullyRevealed: boolean;
}> {
  return gameState.targets.map(target => {
    const isBurned = gameState.burnedTargets.includes(target);
    const pattern = isBurned
      ? '🔥 BURNED 🔥'
      : getRevealedTargetPattern(target, gameState.step, gameState.difficulty);

    const isFullyRevealed = !isBurned && !pattern.includes('_');

    return {
      word: target,
      pattern,
      isBurned,
      isFullyRevealed
    };
  });
}

/**
 * Calculates player confidence based on their progress
 */
export function calculatePlayerConfidence(gameState: GameState): number {
  const progressRatio = gameState.step / gameState.maxSteps;
  const remainingTargets = getRemainingTargets(gameState);
  const targetRatio = remainingTargets.length / gameState.targets.length;
  
  // Higher confidence if making good progress with targets still available
  const baseConfidence = Math.max(0, 1 - progressRatio);
  const targetBonus = targetRatio * 0.3;
  
  return Math.min(1, baseConfidence + targetBonus);
}

/**
 * STRATEGIC TARGET BURNING - MVP Core Mechanics
 * Determines if AI should burn a target based on game state and player behavior
 */
export function shouldBurnTarget(gameState: GameState, playerDefinition?: string): {
  shouldBurn: boolean;
  targetToBurn?: string;
  reason?: string;
} {
  const remainingTargets = getRemainingTargets(gameState);
  const confidence = calculatePlayerConfidence(gameState);
  const config = DIFFICULTY_CONFIG[gameState.difficulty];

  // Never burn the last target - always leave one path
  if (remainingTargets.length <= 1) {
    return {
      shouldBurn: false,
      reason: 'Cannot burn last target - must leave one path open'
    };
  }

  // Check if player is getting too close to any target
  if (playerDefinition) {
    const definitionWords = playerDefinition.toLowerCase().split(/\s+/);
    const dangerousTargets = remainingTargets.filter(target => {
      // Check if definition contains words similar to target
      return definitionWords.some(word => {
        // Check for partial matches or semantic closeness
        return target.toLowerCase().includes(word) ||
               word.includes(target.toLowerCase()) ||
               getSemanticSimilarity(word, target) > 0.7;
      });
    });

    if (dangerousTargets.length > 0) {
      return {
        shouldBurn: true,
        targetToBurn: dangerousTargets[0], // Burn the first dangerous target
        reason: `Player definition shows proximity to target: ${dangerousTargets[0]}`
      };
    }
  }

  // Difficulty-based burning frequency
  const burnFrequency = config.burnFrequency;
  const shouldBurnByFrequency = gameState.step > 0 && gameState.step % burnFrequency === 0;

  if (shouldBurnByFrequency && confidence > GAME_BALANCE.CONFIDENCE_THRESHOLDS.MEDIUM) {
    // Choose target to burn strategically
    const targetToBurn = selectTargetToBurn(remainingTargets, gameState);

    return {
      shouldBurn: true,
      targetToBurn,
      reason: `Strategic burn at step ${gameState.step} (confidence: ${Math.round(confidence * 100)}%)`
    };
  }

  // High confidence emergency burn
  if (confidence > GAME_BALANCE.CONFIDENCE_THRESHOLDS.HIGH && gameState.step >= 5) {
    const targetToBurn = selectTargetToBurn(remainingTargets, gameState);

    return {
      shouldBurn: true,
      targetToBurn,
      reason: `Emergency burn - player confidence too high (${Math.round(confidence * 100)}%)`
    };
  }

  return {
    shouldBurn: false,
    reason: `No burn needed (confidence: ${Math.round(confidence * 100)}%, step: ${gameState.step})`
  };
}

/**
 * Select which target to burn strategically
 */
function selectTargetToBurn(remainingTargets: string[], gameState: GameState): string {
  if (remainingTargets.length === 0) {
      return '';
    }

  // Strategy: Burn the "easiest" target (shortest or most common)
  // This forces players toward harder targets
  const sortedTargets = [...remainingTargets].sort((a, b) => {
    // Prefer shorter words (easier to guess)
    if (a.length !== b.length) {
      return a.length - b.length;
    }

    // If same length, prefer alphabetically first (arbitrary but consistent)
    return a.localeCompare(b);
  });

  return sortedTargets[0];
}

/**
 * Simple semantic similarity check (placeholder for more sophisticated logic)
 */
function getSemanticSimilarity(word1: string, word2: string): number {
  // Simple implementation - can be enhanced with more sophisticated NLP
  const w1 = word1.toLowerCase();
  const w2 = word2.toLowerCase();

  // Exact match
  if (w1 === w2) {
      return 1.0;
    }

  // One contains the other
  if (w1.includes(w2) || w2.includes(w1)) return 0.8;

  // Check for common prefixes/suffixes
  const commonPrefix = getCommonPrefix(w1, w2);
  const commonSuffix = getCommonSuffix(w1, w2);

  const similarity = (commonPrefix.length + commonSuffix.length) / Math.max(w1.length, w2.length);
  return Math.min(similarity, 0.7); // Cap at 0.7 for partial matches
}

function getCommonPrefix(str1: string, str2: string): string {
  let i = 0;
  while (i < str1.length && i < str2.length && str1[i] === str2[i]) {
    i++;
  }
  return str1.substring(0, i);
}

function getCommonSuffix(str1: string, str2: string): string {
  let i = 0;
  while (i < str1.length && i < str2.length &&
         str1[str1.length - 1 - i] === str2[str2.length - 1 - i]) {
    i++;
  }
  return str1.substring(str1.length - i);
}

/**
 * Gets the current difficulty phase based on game progress
 */
export function getCurrentPhase(gameState: GameState): 'opening' | 'middle' | 'endgame' {
  if (gameState.step <= 5) {
      return 'opening';
    }
  if (gameState.step <= 15) {
      return 'middle';
    }
  return 'endgame';
}

/**
 * Utility functions
 */
function generateGameId(): string {
  return `game_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function generateDefinitionId(): string {
  return `def_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Formats game state for display
 */
export function formatGameProgress(gameState: GameState): string {
  const remaining = getRemainingTargets(gameState);
  return `Step ${gameState.step}/${gameState.maxSteps} • Targets: ${remaining.join(', ')}`;
}

/**
 * Gets the last definition from game state
 */
export function getLastDefinition(gameState: GameState): Definition | undefined {
  return gameState.definitions.length > 0
    ? gameState.definitions[gameState.definitions.length - 1]
    : undefined;
}

/**
 * DETERMINISTIC 80/20 WORD SELECTION - MVP Core Mechanics
 * Implements the strategic word selection rule: 80% from definition, 20% strategic
 */
export function selectNextWord(
  playerDefinition: string,
  gameState: GameState,
  aiSuggestion?: string
): {
  nextWord: string;
  selectionType: 'from_definition' | 'semantic_bridge' | 'domain_switch' | 'ai_suggestion' | 'fallback';
  reason: string;
} {
  // Extract words from player's definition
  const definitionWords = extractStrategicWords(playerDefinition, gameState);

  // Determine selection strategy based on probability
  const random = Math.random();
  const probabilities = GAME_BALANCE.WORD_SELECTION_PROBABILITY;

  // 80% chance: Select from player's definition
  if (random < probabilities.FROM_DEFINITION && definitionWords.length > 0) {
    const selectedWord = selectFromDefinition(definitionWords, gameState);
    if (selectedWord) {
      return {
        nextWord: selectedWord,
        selectionType: 'from_definition',
        reason: `Selected "${selectedWord}" from player definition (80% rule)`
      };
    }
  }

  // 15% chance: Semantic bridge word
  if (random < probabilities.FROM_DEFINITION + probabilities.SEMANTIC_BRIDGE) {
    const bridgeWord = selectSemanticBridge(playerDefinition, gameState);
    if (bridgeWord) {
      return {
        nextWord: bridgeWord,
        selectionType: 'semantic_bridge',
        reason: `Selected "${bridgeWord}" as semantic bridge (15% rule)`
      };
    }
  }

  // 5% chance: Domain switch
  const domainWord = selectDomainSwitch(gameState);
  if (domainWord) {
    return {
      nextWord: domainWord,
      selectionType: 'domain_switch',
      reason: `Selected "${domainWord}" to force domain switch (5% rule)`
    };
  }

  // Use AI suggestion if available and valid
  if (aiSuggestion && isValidWordChoice(aiSuggestion, gameState)) {
    return {
      nextWord: aiSuggestion,
      selectionType: 'ai_suggestion',
      reason: `Used AI suggestion "${aiSuggestion}" (fallback)`
    };
  }

  // Ultimate fallback
  const fallbackWord = getFallbackWord(gameState);
  return {
    nextWord: fallbackWord,
    selectionType: 'fallback',
    reason: `Used fallback word "${fallbackWord}" (emergency)`
  };
}

/**
 * Extract strategic words from player's definition
 */
function extractStrategicWords(definition: string, gameState: GameState): string[] {
  const words = definition.toLowerCase()
    .split(/\s+/)
    .filter(word => word.length > 2) // Skip short words
    .filter(word => !VALIDATION.COMMON_WORDS.includes(word as any)) // Skip common words
    .filter(word => !gameState.usedWords.includes(word)) // Skip already used words
    .filter(word => !gameState.targets.includes(word)) // Skip target words
    .filter(word => !gameState.aiChallengeWords.includes(word)) // Skip previously used AI words
    .filter(word => /^[a-z]+$/.test(word)); // Only alphabetic words

  // Sort by strategic value (longer words first, then alphabetical)
  return words.sort((a, b) => {
    if (a.length !== b.length) {
      return b.length - a.length; // Longer words first
    }
    return a.localeCompare(b); // Alphabetical for consistency
  }).slice(0, 5); // Return top 5 candidates
}

/**
 * Select word from player's definition (80% strategy)
 */
function selectFromDefinition(definitionWords: string[], gameState: GameState): string | null {
  // Prefer words that create semantic distance from targets
  const strategicWords = definitionWords.filter(word => {
    // Check if word creates distance from targets
    return !gameState.targets.some(target =>
      areSemanticallySimilar(word, target)
    );
  });

  // Return the most strategic word, or first available if none are strategic
  return strategicWords[0] || definitionWords[0] || null;
}

/**
 * Select semantic bridge word (15% strategy)
 */
function selectSemanticBridge(definition: string, gameState: GameState): string | null {
  // Bridge words that connect multiple semantic domains
  const bridgeWords = [
    'concept', 'idea', 'form', 'structure', 'element', 'aspect', 'nature',
    'essence', 'quality', 'property', 'characteristic', 'feature', 'trait',
    'principle', 'method', 'process', 'system', 'pattern', 'relationship'
  ];

  // Filter available bridge words
  const availableBridges = bridgeWords.filter(word =>
    isValidWordChoice(word, gameState)
  );

  // Return random bridge word
  return availableBridges.length > 0
    ? availableBridges[Math.floor(Math.random() * availableBridges.length)]
    : null;
}

/**
 * Select domain switch word (5% strategy)
 */
function selectDomainSwitch(gameState: GameState): string | null {
  // Words that force complete semantic domain changes
  const domainSwitchWords = [
    'mechanism', 'phenomenon', 'manifestation', 'embodiment', 'representation',
    'abstraction', 'realization', 'implementation', 'interpretation', 'expression',
    'reflection', 'projection', 'extension', 'variation', 'transformation'
  ];

  // Filter available domain switch words
  const availableSwitches = domainSwitchWords.filter(word =>
    isValidWordChoice(word, gameState)
  );

  // Return random domain switch word
  return availableSwitches.length > 0
    ? availableSwitches[Math.floor(Math.random() * availableSwitches.length)]
    : null;
}

/**
 * Check if word choice is valid (not used, not target, etc.)
 */
function isValidWordChoice(word: string, gameState: GameState): boolean {
  const normalizedWord = word.toLowerCase();

  return !gameState.usedWords.includes(normalizedWord) &&
         !gameState.targets.includes(normalizedWord) &&
         !gameState.aiChallengeWords.includes(normalizedWord) &&
         normalizedWord.length > 2 &&
         /^[a-z]+$/.test(normalizedWord);
}

/**
 * Get fallback word when all else fails
 */
function getFallbackWord(gameState: GameState): string {
  const fallbackWords = ['concept', 'idea', 'notion', 'principle', 'element', 'aspect', 'quality'];

  // Find first available fallback
  for (const word of fallbackWords) {
    if (isValidWordChoice(word, gameState)) {
      return word;
    }
  }

  // Ultimate fallback
  return 'thing';
}

/**
 * Simple semantic similarity check
 */
function areSemanticallySimilar(word1: string, word2: string): boolean {
  // Simple implementation - can be enhanced
  const w1 = word1.toLowerCase();
  const w2 = word2.toLowerCase();

  // Exact match or one contains the other
  return w1 === w2 || w1.includes(w2) || w2.includes(w1);
}

/**
 * AI CONSTRAINT VALIDATION - MVP Core Mechanics
 * Validates that AI follows all game rules and constraints
 */
export function validateAIResponse(
  aiResponse: any,
  gameState: GameState,
  playerDefinition: string
): {
  isValid: boolean;
  violations: string[];
  correctedResponse?: any;
} {
  const violations: string[] = [];
  let correctedResponse = { ...aiResponse };

  // Constraint 1: AI must never give target words as challenges
  if (aiResponse.nextWord) {
    const normalizedNextWord = aiResponse.nextWord.toLowerCase();

    if (gameState.targets.includes(normalizedNextWord)) {
      violations.push(`AI tried to give target word "${aiResponse.nextWord}" as challenge`);

      // Auto-correct with deterministic selection
      const wordSelection = selectNextWord(playerDefinition, gameState);
      correctedResponse.nextWord = wordSelection.nextWord;
      correctedResponse.reason = `${correctedResponse.reason || ''} (Auto-corrected: AI gave target word)`;
    }
  }

  // Constraint 2: AI must not reuse words
  if (aiResponse.nextWord) {
    const normalizedNextWord = aiResponse.nextWord.toLowerCase();

    if (gameState.usedWords.includes(normalizedNextWord) ||
        gameState.aiChallengeWords.includes(normalizedNextWord)) {
      violations.push(`AI tried to reuse word "${aiResponse.nextWord}"`);

      // Auto-correct with deterministic selection
      const wordSelection = selectNextWord(playerDefinition, gameState);
      correctedResponse.nextWord = wordSelection.nextWord;
      correctedResponse.reason = `${correctedResponse.reason || ''} (Auto-corrected: AI reused word)`;
    }
  }

  // Constraint 3: AI must not burn the last target
  if (aiResponse.burnedTarget) {
    const remainingTargets = gameState.targets.filter(target =>
      !gameState.burnedTargets.includes(target)
    );

    if (remainingTargets.length <= 1) {
      violations.push(`AI tried to burn last remaining target "${aiResponse.burnedTarget}"`);
      correctedResponse.burnedTarget = null;
    }
  }

  // Constraint 4: AI must provide valid reasoning for rejections
  if (!aiResponse.accept && (!aiResponse.reason || aiResponse.reason.trim().length < 10)) {
    violations.push('AI rejected definition without sufficient reasoning');
    correctedResponse.reason = 'Definition does not meet game requirements';
  }

  // Constraint 5: AI must follow 80/20 word selection rule (enforced by deterministic selection)
  // This is handled by the deterministic word selection system

  return {
    isValid: violations.length === 0,
    violations,
    correctedResponse: violations.length > 0 ? correctedResponse : undefined
  };
}

/**
 * Logs AI constraint violations for debugging
 */
export function logAIViolations(violations: string[], gameState: GameState): void {
  if (violations.length > 0) {
    console.warn('🚨 AI CONSTRAINT VIOLATIONS DETECTED:', {
      violations,
      gameId: gameState.gameId,
      step: gameState.step,
      timestamp: Date.now()
    });
  }
}

/**
 * Ensures minimum winnable path guarantee
 */
export function validateWinnablePath(gameState: GameState): {
  hasWinnablePath: boolean;
  reason: string;
} {
  const remainingTargets = gameState.targets.filter(target =>
    !gameState.burnedTargets.includes(target)
  );

  // Must have at least one target remaining
  if (remainingTargets.length === 0) {
    return {
      hasWinnablePath: false,
      reason: 'No targets remaining - game is impossible'
    };
  }

  // Must have enough steps remaining to potentially reach a target
  const remainingSteps = gameState.maxSteps - gameState.step;
  if (remainingSteps <= 0) {
    return {
      hasWinnablePath: false,
      reason: 'No steps remaining - game is impossible'
    };
  }

  // Check if player hasn't hit rejection limit
  if (gameState.consecutiveRejections >= VALIDATION.MAX_CONSECUTIVE_REJECTIONS) {
    return {
      hasWinnablePath: false,
      reason: 'Player has reached maximum consecutive rejections'
    };
  }

  return {
    hasWinnablePath: true,
    reason: `${remainingTargets.length} target(s) remaining with ${remainingSteps} steps left`
  };
}

/**
 * COMPREHENSIVE GAME STATE EVALUATION - MVP Core Mechanics
 * Determines if game should end and why
 */
export function evaluateGameState(definition: string, gameState: GameState): {
  gameStatus: 'won' | 'lost' | 'continue';
  reason: string;
  winType?: 'target_in_definition';
  lossType?: 'step_limit' | 'three_strikes' | 'impossible';
  targetWord?: string;
} {
  // Check win condition first
  const winResult = checkWinCondition(definition, gameState);
  if (winResult.isWin) {
    return {
      gameStatus: 'won',
      reason: winResult.reason || 'Player wins!',
      winType: winResult.winType === 'target_in_definition' ? winResult.winType : undefined,
      targetWord: winResult.targetWord
    };
  }

  // Check loss conditions
  const lossResult = checkLossCondition(gameState);
  if (lossResult.isLoss) {
    return {
      gameStatus: 'lost',
      reason: lossResult.reason || 'Player loses!',
      lossType: lossResult.lossType !== 'none' ? lossResult.lossType : undefined
    };
  }

  // Game continues
  const activeTargets = gameState.targets.filter(target =>
    !gameState.burnedTargets.includes(target)
  );
  const remainingSteps = gameState.maxSteps - gameState.step;

  return {
    gameStatus: 'continue',
    reason: `Game continues. ${activeTargets.length} targets remaining, ${remainingSteps} steps left.`
  };
}

/**
 * LEGACY FUNCTION - Kept for backward compatibility
 * @deprecated Use checkLossCondition(gameState) instead
 */
export function isGameImpossible(gameState: GameState): boolean {
  const lossResult = checkLossCondition(gameState);
  return lossResult.isLoss && lossResult.lossType === 'impossible';
}

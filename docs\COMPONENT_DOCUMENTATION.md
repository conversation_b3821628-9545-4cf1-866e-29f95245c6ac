# 📦 **Component Documentation**

> **Comprehensive guide to DEFEATER.AI components and their usage**

## 🏗️ **Architecture Overview**

DEFEATER.AI uses a modular component architecture with spatial design principles:

- **Spatial Layout System** - Open, breathing room design
- **Component Composition** - Reusable, testable components
- **Performance Optimized** - Lazy loading and code splitting
- **Accessibility First** - WCAG 2.1 AA compliant

## 🎮 **Game Components**

### **GameBoard.tsx**
**Main game orchestrator component**

```typescript
interface GameBoardProps {
  initialGameState?: GameState;
  onGameStateChange?: (state: GameState) => void;
}
```

**Features:**
- ✅ Game state management
- ✅ AI integration and chat
- ✅ Performance optimization
- ✅ Accessibility compliance
- ✅ Error handling and recovery

**Usage:**
```tsx
<GameBoard 
  initialGameState={gameState}
  onGameStateChange={handleStateChange}
/>
```

### **GameInput.tsx**
**Definition input component with validation**

```typescript
interface GameInputProps {
  onSubmit: (definition: string) => void;
  disabled?: boolean;
  placeholder?: string;
}
```

**Features:**
- ✅ Real-time validation
- ✅ Debounced input handling
- ✅ Accessibility support
- ✅ Error state management

### **GameDisplay.tsx**
**Current challenge and target display**

```typescript
interface GameDisplayProps {
  currentWord: string;
  targets: string[];
  revealedHints: string[];
  step: number;
  maxSteps: number;
}
```

**Features:**
- ✅ Progressive target revelation
- ✅ Step counter with urgency states
- ✅ Animated transitions
- ✅ Screen reader announcements

## 🎨 **UI Components**

### **SpatialButton.tsx**
**Spatial design system button**

```typescript
interface SpatialButtonProps {
  variant: 'primary' | 'secondary' | 'danger' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
}
```

**Variants:**
- `primary` - Main action buttons
- `secondary` - Secondary actions
- `danger` - Destructive actions
- `ghost` - Minimal styling

### **SpatialModal.tsx**
**Accessible modal with spatial design**

```typescript
interface SpatialModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}
```

**Features:**
- ✅ Focus management
- ✅ Escape key handling
- ✅ Backdrop click to close
- ✅ ARIA compliance

### **Typography Components**
**Spatial design typography system**

```tsx
// Available components
<Hero>Main headings</Hero>
<Primary>Primary text</Primary>
<Secondary>Secondary text</Secondary>
<Small>Small text</Small>
<Caption>Caption text</Caption>
```

## 📱 **Layout Components**

### **SpatialLayout.tsx**
**Main layout wrapper with spatial design**

```typescript
interface SpatialLayoutProps {
  children: React.ReactNode;
  variant: 'game' | 'test' | 'minimal';
  showSidePanel?: boolean;
}
```

**Features:**
- ✅ Responsive grid system
- ✅ Spatial focus areas
- ✅ Accessibility landmarks
- ✅ Performance optimized

### **CollapsibleSidePanel.tsx**
**Game statistics and information panel**

```typescript
interface CollapsibleSidePanelProps {
  isOpen: boolean;
  onToggle: () => void;
  gameState: GameState;
  children: React.ReactNode;
}
```

**Features:**
- ✅ Smooth animations
- ✅ Persistent state
- ✅ Keyboard navigation
- ✅ Mobile responsive

### **SkipLinks.tsx**
**Accessibility navigation shortcuts**

```typescript
interface SkipLinksProps {
  links: Array<{
    href: string;
    label: string;
    description: string;
  }>;
}
```

**Features:**
- ✅ Keyboard-only navigation
- ✅ Screen reader optimized
- ✅ Focus management
- ✅ WCAG 2.1 AA compliant

## 💬 **Chat Components**

### **FloatingChatWidget.tsx**
**AI chat interface with psychological warfare**

```typescript
interface FloatingChatWidgetProps {
  gameState: GameState;
  onAIResponse: (message: string) => void;
  position: 'bottom-right' | 'bottom-left';
}
```

**Features:**
- ✅ Real-time AI integration
- ✅ Contextual responses
- ✅ Auto-expand/collapse
- ✅ Notification system

### **FloatingChatDialog.tsx**
**Expandable chat conversation view**

```typescript
interface FloatingChatDialogProps {
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}
```

**Features:**
- ✅ Message history
- ✅ Typing indicators
- ✅ Error handling
- ✅ Accessibility support

## 🎯 **Panel Components**

### **GameStatsPanel.tsx**
**Real-time game statistics display**

```typescript
interface GameStatsPanelProps {
  gameState: GameState;
  showAdvanced?: boolean;
}
```

**Features:**
- ✅ Live statistics updates
- ✅ Performance metrics
- ✅ Progress visualization
- ✅ Responsive design

### **DevPanel.tsx**
**Development tools and debugging**

```typescript
interface DevPanelProps {
  gameState: GameState;
  onDebugAction: (action: string, data: any) => void;
  visible: boolean;
}
```

**Features:**
- ✅ Game state inspection
- ✅ AI response debugging
- ✅ Performance monitoring
- ✅ Development-only visibility

## 🔧 **Utility Components**

### **ErrorBoundary.tsx**
**React error boundary with recovery**

```typescript
interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{error: Error}>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}
```

**Features:**
- ✅ Error catching and logging
- ✅ Graceful fallback UI
- ✅ Recovery mechanisms
- ✅ Development error details

### **ProgressiveDisclosure.tsx**
**Contextual help and information system**

```typescript
interface ProgressiveDisclosureProps {
  gameState: GameState;
  rules: DisclosureRule[];
  onRuleTriggered: (rule: DisclosureRule) => void;
}
```

**Features:**
- ✅ Context-aware help
- ✅ Progressive revelation
- ✅ User preference learning
- ✅ Accessibility support

## 🎨 **Styling Guidelines**

### **CSS Modules**
Each component uses CSS modules for scoped styling:

```css
/* Component.module.css */
.container {
  /* Spatial design principles */
  background: var(--glass-medium);
  backdrop-filter: blur(12px);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
}
```

### **Design Tokens**
Consistent design system variables:

```css
:root {
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  
  /* Glass effects */
  --glass-light: rgba(255, 255, 255, 0.1);
  --glass-medium: rgba(255, 255, 255, 0.15);
  --glass-heavy: rgba(255, 255, 255, 0.2);
}
```

## 🧪 **Testing Components**

### **Component Testing**
Each component includes comprehensive tests:

```typescript
// Component.test.tsx
describe('Component', () => {
  it('renders correctly', () => {
    render(<Component {...props} />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
  
  it('handles user interactions', async () => {
    const handleClick = jest.fn();
    render(<Component onClick={handleClick} />);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalled();
  });
  
  it('meets accessibility standards', async () => {
    const { container } = render(<Component />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

### **Testing Utilities**
Shared testing utilities in `utils/testUtils.ts`:

```typescript
// Mock game state
export const mockGameState = createMockGameState({
  step: 5,
  maxSteps: 25,
  currentWord: 'example'
});

// Accessibility testing
export const testAccessibility = async (component: ReactElement) => {
  const { container } = render(component);
  const results = await axe(container);
  expect(results).toHaveNoViolations();
};
```

## 📚 **Best Practices**

### **Component Design**
1. **Single Responsibility** - Each component has one clear purpose
2. **Composition over Inheritance** - Use composition for flexibility
3. **Accessibility First** - WCAG 2.1 AA compliance from the start
4. **Performance Optimized** - Lazy loading and memoization
5. **Type Safety** - Full TypeScript coverage

### **Styling Approach**
1. **Spatial Design** - Open, breathing room layouts
2. **Glass Effects** - Consistent backdrop blur and transparency
3. **Responsive Design** - Mobile-first approach
4. **High Contrast** - Excellent readability
5. **Performance** - Optimized CSS delivery

### **Testing Strategy**
1. **Unit Tests** - Individual component functionality
2. **Integration Tests** - Component interaction testing
3. **Accessibility Tests** - WCAG compliance validation
4. **Performance Tests** - Render time and memory usage
5. **Visual Regression** - UI consistency validation

---

**For more detailed information, see individual component files and their accompanying test suites.**

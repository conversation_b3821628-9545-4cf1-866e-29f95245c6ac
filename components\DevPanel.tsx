import React, { useState } from 'react';
import { GameState, AIResponse } from '@/types/game';
import { analyzeWordSimilarity } from '@/utils/wordSimilarity';
import SpatialModal, { SpatialModalActions } from '@/components/ui/SpatialModal';

import TabNavigation, { TabItem } from '@/components/ui/TabNavigation';
import { Primary, Secondary, Body, Small, Mono, StatusText } from '@/components/ui/Typography';

interface DevPanelProps {
  gameState: GameState;
  lastAIResponse?: AIResponse;
  isVisible: boolean;
  onToggle: () => void;
}

const DevPanel: React.FC<DevPanelProps> = ({
  gameState,
  lastAIResponse,
  isVisible,
  onToggle
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'ai' | 'logic' | 'words' | 'history'>('overview');

  // Floating toggle button when panel is hidden
  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="btn-secondary fixed bottom-4 right-4 z-50 shadow-xl"
        aria-label="Open Developer Panel"
        style={{ background: 'var(--color-error)' }}
      >
        🔧 DEV
      </button>
    );
  }

  // Create tab items for TabNavigation
  const tabs: TabItem[] = [
    {
      id: 'overview',
      label: 'Overview',
      icon: '🎮',
      content: <OverviewTab gameState={gameState} lastAIResponse={lastAIResponse} />
    },
    {
      id: 'ai',
      label: 'AI Debug',
      icon: '🤖',
      content: <AITab lastAIResponse={lastAIResponse} />
    },
    {
      id: 'logic',
      label: 'Logic',
      icon: '⚙️',
      content: <LogicTab gameState={gameState} lastAIResponse={lastAIResponse} />
    },
    {
      id: 'words',
      label: 'Words',
      icon: '🔍',
      content: <WordsTab gameState={gameState} />
    },
    {
      id: 'history',
      label: 'History',
      icon: '📚',
      content: <HistoryTab gameState={gameState} />
    }
  ];

  return (
    <SpatialModal
      isOpen={isVisible}
      onClose={onToggle}
      title="🔧 Developer Panel"
      subtitle="Debug tools and game analysis"
      size="fullscreen"
      variant="default"
      className="dev-panel-modal"
    >
      {/* Tab Navigation and Content */}
      <TabNavigation
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={(tabId) => setActiveTab(tabId as typeof activeTab)}
        variant="pills"
        size="medium"
        className="dev-panel-tabs"
      />

      {/* Modal Actions */}
      <SpatialModalActions align="right">
        <button
          className="btn-secondary"
          onClick={onToggle}
        >
          ✕ Close Panel
        </button>
      </SpatialModalActions>
    </SpatialModal>
  );
};

// Overview Tab
const OverviewTab: React.FC<{ gameState: GameState; lastAIResponse?: AIResponse }> = ({ gameState, lastAIResponse }) => (
  <div className="dev-overview-tab">
    <div className="overview-grid">
      {/* Game State */}
      <div className="dev-card">
        <Primary as="h3" className="dev-card-title">🎮 Game State</Primary>
        <div className="dev-info-grid">
          <div className="dev-info-row">
            <Secondary>Status:</Secondary>
            <StatusText status={getStatusVariant(gameState.gameStatus)}>
              {gameState.gameStatus.toUpperCase()}
            </StatusText>
          </div>
          <div className="dev-info-row">
            <Secondary>Step:</Secondary>
            <Body>{gameState.step} / {gameState.maxSteps}</Body>
          </div>
          <div className="dev-info-row">
            <Secondary>Current Word:</Secondary>
            <Mono className="text-accent">{gameState.currentWord || 'None'}</Mono>
          </div>
          <div className="dev-info-row">
            <Secondary>Targets:</Secondary>
            <StatusText status="success">{gameState.targets.length}</StatusText>
          </div>
          <div className="dev-info-row">
            <Secondary>Burned:</Secondary>
            <StatusText status="error">{gameState.burnedTargets.length}</StatusText>
          </div>
        </div>
      </div>

      {/* AI Decision */}
      <div className="dev-card">
        <Primary as="h3" className="dev-card-title">🤖 Last AI Decision</Primary>
        {lastAIResponse ? (
          <div className="dev-info-grid">
            <div className="dev-info-row">
              <Secondary>Accept:</Secondary>
              <StatusText status={lastAIResponse.accept ? 'success' : 'error'}>
                {lastAIResponse.accept ? 'YES' : 'NO'}
              </StatusText>
            </div>
            <div className="dev-info-row">
              <Secondary>Game Result:</Secondary>
              <Body>{lastAIResponse.gameResult || 'continue'}</Body>
            </div>
            <div className="dev-info-row">
              <Secondary>Next Word:</Secondary>
              <Mono>{lastAIResponse.nextWord || 'None'}</Mono>
            </div>
            <div className="dev-info-row">
              <Secondary>Burned Target:</Secondary>
              <StatusText status="error">{lastAIResponse.burnedTarget || 'None'}</StatusText>
            </div>
          </div>
        ) : (
          <Body className="text-muted">No AI response yet</Body>
        )}
      </div>
    </div>

    {/* Targets Status */}
    <div className="dev-card dev-card--full">
      <Primary as="h3" className="dev-card-title">🎯 Targets Status</Primary>
      <div className="targets-grid">
        {gameState.targets.map((target, index) => {
          const isBurned = gameState.burnedTargets.includes(target);
          const isActive = target === gameState.currentWord;

          return (
            <div
              key={index}
              className={`target-card ${
                isActive ? 'target-card--active' :
                isBurned ? 'target-card--burned' :
                'target-card--available'
              }`}
            >
              <Mono className="target-word">
                {target}
                {isActive && ' 🎯'}
                {isBurned && ' 🔥'}
                {!isBurned && !isActive && ' ✓'}
              </Mono>
              <Small className="target-status">
                {isActive ? 'CURRENT' : isBurned ? 'BURNED' : 'AVAILABLE'}
              </Small>
            </div>
          );
        })}
      </div>
    </div>
  </div>
);

// AI Tab
const AITab: React.FC<{ lastAIResponse?: AIResponse }> = ({ lastAIResponse }) => (
  <div className="dev-ai-tab">
    {lastAIResponse?.debugInfo ? (
      <>
        {/* AI Raw Response */}
        <div className="dev-card">
          <Primary as="h3" className="dev-card-title">🤖 Raw AI Response</Primary>
          <div className="code-block">
            <Mono className="code-content">
              {lastAIResponse.debugInfo.aiRawResponse}
            </Mono>
          </div>
        </div>

        {/* Win Detection Analysis */}
        <div className="dev-card">
          <Primary as="h3" className="dev-card-title">🎯 Win Detection Analysis</Primary>
          <div className="analysis-grid">
            <div className="analysis-section">
              <Secondary as="h4" className="analysis-title">Detection Method</Secondary>
              <StatusText status={getDetectionMethodVariant(lastAIResponse.debugInfo.winDetectionMethod)}>
                {lastAIResponse.debugInfo.winDetectionMethod.toUpperCase()}
              </StatusText>
            </div>
            <div className="analysis-section">
              <Secondary as="h4" className="analysis-title">Check Results</Secondary>
              <div className="check-results">
                <div className="check-result-row">
                  <Small>Direct Win:</Small>
                  <StatusText status={lastAIResponse.debugInfo.winCheckResults.directWin ? 'success' : 'error'}>
                    {lastAIResponse.debugInfo.winCheckResults.directWin ? 'YES' : 'NO'}
                  </StatusText>
                </div>
                <div className="check-result-row">
                  <Small>Semantic Win:</Small>
                  <StatusText status={lastAIResponse.debugInfo.winCheckResults.semanticWin ? 'success' : 'error'}>
                    {lastAIResponse.debugInfo.winCheckResults.semanticWin ? 'YES' : 'NO'}
                  </StatusText>
                </div>
              </div>
            </div>
          </div>
          <div className="ai-decision-section">
            <Secondary as="h4" className="analysis-title">AI Decision</Secondary>
            <div className="ai-decision-content">
              <Body>{lastAIResponse.debugInfo.winCheckResults.aiDecision}</Body>
            </div>
          </div>
        </div>
      </>
    ) : (
      <div className="dev-empty-state">
        <Body className="text-muted">No debug information available</Body>
        <Small className="text-muted">Debug info will appear after AI responses</Small>
      </div>
    )}
  </div>
);

// Logic Tab
const LogicTab: React.FC<{ gameState: GameState; lastAIResponse?: AIResponse }> = ({ gameState, lastAIResponse }) => (
  <div className="dev-logic-tab">
    {/* Game State Analysis */}
    {lastAIResponse?.debugInfo && (
      <div className="dev-card">
        <Primary as="h3" className="dev-card-title">⚙️ Game State Analysis</Primary>
        <div className="logic-analysis-grid">
          <div className="logic-section">
            <Secondary as="h4" className="logic-section-title">Current State</Secondary>
            <div className="logic-info-grid">
              <div className="logic-info-row">
                <Small>Current Word:</Small>
                <Mono className="text-accent">
                  {lastAIResponse.debugInfo.gameStateAnalysis.currentWord}
                </Mono>
              </div>
              <div className="logic-info-row">
                <Small>Step:</Small>
                <Body>{lastAIResponse.debugInfo.gameStateAnalysis.step}</Body>
              </div>
              <div className="logic-info-row">
                <Small>Is Impossible:</Small>
                <StatusText status={lastAIResponse.debugInfo.gameStateAnalysis.isImpossible ? 'error' : 'success'}>
                  {lastAIResponse.debugInfo.gameStateAnalysis.isImpossible ? 'YES' : 'NO'}
                </StatusText>
              </div>
            </div>
          </div>
          <div className="logic-section">
            <Secondary as="h4" className="logic-section-title">Targets</Secondary>
            <div className="targets-analysis">
              <div className="target-group">
                <Small className="target-group-label">Remaining:</Small>
                <div className="target-tags">
                  {lastAIResponse.debugInfo.gameStateAnalysis.remainingTargets.map((target, i) => (
                    <span key={i} className="target-tag target-tag--success">
                      {target}
                    </span>
                  ))}
                </div>
              </div>
              <div className="target-group">
                <Small className="target-group-label">Burned:</Small>
                <div className="target-tags">
                  {lastAIResponse.debugInfo.gameStateAnalysis.burnedTargets.map((target, i) => (
                    <span key={i} className="target-tag target-tag--error">
                      {target}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )}

    {/* Internal Analysis */}
    {lastAIResponse && (
      <div className="glass-light rounded-lg p-4 border border-defeater-dark-500/30">
        <h3 className="text-lg font-semibold text-defeater-neon-purple mb-3">🧠 AI Internal Analysis</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-defeater-text-primary mb-2">Confidence & Strategy</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Player Confidence:</span>
                <span className="text-defeater-neon-cyan">
                  {lastAIResponse.internalAnalysis.playerConfidence}%
                </span>
              </div>
              <div className="flex justify-between">
                <span>Difficulty Adjustment:</span>
                <span className={`${
                  lastAIResponse.internalAnalysis.difficultyAdjustment > 0 ? 'text-defeater-neon-red' :
                  lastAIResponse.internalAnalysis.difficultyAdjustment < 0 ? 'text-defeater-neon-green' :
                  'text-defeater-text-primary'
                }`}>
                  {lastAIResponse.internalAnalysis.difficultyAdjustment > 0 ? '+' : ''}
                  {lastAIResponse.internalAnalysis.difficultyAdjustment}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Recommended Burn:</span>
                <span className="text-defeater-neon-orange">
                  {lastAIResponse.internalAnalysis.recommendedBurn || 'None'}
                </span>
              </div>
            </div>
          </div>
          <div>
            <h4 className="font-medium text-defeater-text-primary mb-2">Remaining Paths</h4>
            <div className="flex flex-wrap gap-1">
              {lastAIResponse.internalAnalysis.remainingPaths.map((path, i) => (
                <span key={i} className="px-2 py-1 bg-defeater-neon-cyan/20 text-defeater-neon-cyan rounded text-xs">
                  {path}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    )}
  </div>
);

// Words Tab - Word Similarity Analysis
const WordsTab: React.FC<{ gameState: GameState }> = ({ gameState }) => {
  const [testWord1, setTestWord1] = React.useState('');
  const [testWord2, setTestWord2] = React.useState('');

  const analysis = testWord1 && testWord2 ? analyzeWordSimilarity(testWord1, testWord2) : null;

  return (
    <div className="dev-words-tab">
      {/* Word Similarity Tester */}
      <div className="dev-card">
        <Primary as="h3" className="dev-card-title">🔍 Word Similarity Tester</Primary>
        <div className="word-input-grid">
          <div className="word-input-group">
            <label htmlFor="test-word-1" className="word-input-label">
              <Secondary>Word 1:</Secondary>
            </label>
            <input
              id="test-word-1"
              type="text"
              value={testWord1}
              onChange={(e) => setTestWord1(e.target.value)}
              className="word-input"
              placeholder="e.g., create"
            />
          </div>
          <div className="word-input-group">
            <label htmlFor="test-word-2" className="word-input-label">
              <Secondary>Word 2:</Secondary>
            </label>
            <input
              id="test-word-2"
              type="text"
              value={testWord2}
              onChange={(e) => setTestWord2(e.target.value)}
              className="word-input"
              placeholder="e.g., creating"
            />
          </div>
        </div>

        {analysis && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-3 glass-light rounded-lg">
                <div className="text-2xl font-bold text-defeater-neon-cyan">
                  {(analysis.similarity * 100).toFixed(1)}%
                </div>
                <div className="text-xs text-defeater-text-muted">Similarity</div>
              </div>
              <div className="text-center p-3 glass-light rounded-lg">
                <div className={`text-2xl font-bold ${analysis.isLegitimateVariation ? 'text-defeater-neon-green' : 'text-defeater-neon-red'}`}>
                  {analysis.isLegitimateVariation ? 'ALLOW' : 'REJECT'}
                </div>
                <div className="text-xs text-defeater-text-muted">Legitimate Variation</div>
              </div>
              <div className="text-center p-3 glass-light rounded-lg">
                <div className={`text-2xl font-bold ${analysis.isSuspicious ? 'text-defeater-neon-red' : 'text-defeater-neon-green'}`}>
                  {analysis.isSuspicious ? 'YES' : 'NO'}
                </div>
                <div className="text-xs text-defeater-text-muted">Suspicious</div>
              </div>
            </div>

            <div className="bg-defeater-dark-800 p-3 rounded-lg">
              <h4 className="font-medium text-defeater-text-primary mb-2">Analysis Details:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-defeater-text-secondary">Normalized Words:</div>
                  <div className="font-mono text-defeater-neon-cyan">
                    "{analysis.details.normalized1}" → "{analysis.details.normalized2}"
                  </div>
                </div>
                <div>
                  <div className="text-defeater-text-secondary">Word Roots:</div>
                  <div className="font-mono text-defeater-neon-purple">
                    "{analysis.details.root1}" → "{analysis.details.root2}"
                  </div>
                </div>
                <div>
                  <div className="text-defeater-text-secondary">Edit Distance:</div>
                  <div className="text-defeater-text-primary">{analysis.details.levenshteinDistance}</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Used Words Analysis */}
      <div className="glass-light rounded-lg p-4 border border-defeater-dark-500/30">
        <h3 className="text-lg font-semibold text-defeater-neon-purple mb-4">📝 Used Words Analysis</h3>
        {gameState.usedWords.length > 0 ? (
          <div className="space-y-3">
            <div className="text-sm text-defeater-text-secondary mb-2">
              Total words used: {gameState.usedWords.length}
            </div>
            <div className="flex flex-wrap gap-2">
              {gameState.usedWords.map((word, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-defeater-neon-cyan/20 text-defeater-neon-cyan rounded-full text-sm font-mono"
                >
                  {word}
                </span>
              ))}
            </div>
          </div>
        ) : (
          <p className="text-defeater-text-muted text-center">No words used yet</p>
        )}
      </div>
    </div>
  );
};

// History Tab
const HistoryTab: React.FC<{ gameState: GameState }> = ({ gameState }) => (
  <div className="space-y-4">
    <h3 className="text-lg font-semibold text-defeater-neon-orange mb-3">📚 Game History</h3>
    {gameState.definitions.length > 0 ? (
      <div className="space-y-3">
        {gameState.definitions.map((def, index) => (
          <div key={def.id} className="glass-light rounded-lg p-3 border border-defeater-dark-500/30">
            <div className="flex justify-between items-start mb-2">
              <span className="font-mono text-defeater-neon-cyan">Step {index + 1}: "{def.word}"</span>
              <span className="text-xs text-defeater-text-muted">
                {def.wordCount} words
              </span>
            </div>
            <p className="text-sm text-defeater-text-secondary">"{def.definition}"</p>
          </div>
        ))}
      </div>
    ) : (
      <p className="text-defeater-text-muted text-center">No definitions yet</p>
    )}
  </div>
);

// Helper functions
function getStatusVariant(status: string): 'success' | 'error' | 'warning' | 'info' {
  switch (status) {
    case 'won': return 'success';
    case 'lost': return 'error';
    case 'processing': return 'info';
    case 'waiting': return 'warning';
    default: return 'info';
  }
}

function getDetectionMethodVariant(method: string): 'success' | 'error' | 'warning' | 'info' {
  switch (method) {
    case 'ai': return 'info';
    case 'code': return 'success';
    case 'hybrid': return 'warning';
    default: return 'error';
  }
}

const DevPanelWithStyles: React.FC<DevPanelProps> = (props) => (
  <>
    <DevPanel {...props} />
    <style jsx>{`
      /* === DEV PANEL SPATIAL DESIGN CSS === */

      /* Overview Tab */
      .dev-overview-tab {
        display: flex;
        flex-direction: column;
        gap: var(--space-6);
      }

      .overview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--space-6);
      }

      .dev-card {
        background: var(--bg-glass-light);
        border: 1px solid var(--bg-tertiary);
        border-radius: var(--radius-lg);
        padding: var(--space-4);
        backdrop-filter: blur(8px);
      }

      .dev-card--full {
        grid-column: 1 / -1;
      }

      .dev-card-title {
        margin: 0 0 var(--space-3) 0;
        color: var(--text-primary);
      }

      .dev-info-grid {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
      }

      .dev-info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--space-1) 0;
      }

      /* Targets Grid */
      .targets-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: var(--space-3);
      }

      .target-card {
        padding: var(--space-3);
        border-radius: var(--radius-md);
        border: 1px solid var(--bg-tertiary);
        text-align: center;
        transition: all var(--transition-base);
      }

      .target-card--active {
        border-color: var(--accent-cyan);
        background: var(--bg-glass-medium);
      }

      .target-card--burned {
        border-color: var(--color-error);
        background: var(--bg-glass-light);
      }

      .target-card--available {
        border-color: var(--color-success);
        background: var(--bg-glass-light);
      }

      .target-word {
        display: block;
        margin-bottom: var(--space-1);
      }

      .target-status {
        color: var(--text-muted);
      }

      /* AI Tab */
      .dev-ai-tab {
        display: flex;
        flex-direction: column;
        gap: var(--space-6);
      }

      .code-block {
        background: var(--bg-tertiary);
        border: 1px solid var(--bg-tertiary);
        border-radius: var(--radius-md);
        padding: var(--space-3);
        overflow-x: auto;
      }

      .code-content {
        white-space: pre-wrap;
        word-break: break-word;
        font-size: 0.75rem;
        line-height: 1.4;
      }

      .analysis-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-4);
      }

      .analysis-section {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
      }

      .analysis-title {
        margin: 0;
      }

      .check-results {
        display: flex;
        flex-direction: column;
        gap: var(--space-1);
      }

      .check-result-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .ai-decision-section {
        margin-top: var(--space-4);
        padding-top: var(--space-4);
        border-top: 1px solid var(--bg-tertiary);
      }

      .ai-decision-content {
        background: var(--bg-tertiary);
        padding: var(--space-3);
        border-radius: var(--radius-md);
        margin-top: var(--space-2);
      }

      .dev-empty-state {
        text-align: center;
        padding: var(--space-8);
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
      }

      /* Logic Tab */
      .dev-logic-tab {
        display: flex;
        flex-direction: column;
        gap: var(--space-6);
      }

      .logic-analysis-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--space-4);
      }

      .logic-section {
        display: flex;
        flex-direction: column;
        gap: var(--space-3);
      }

      .logic-section-title {
        margin: 0;
      }

      .logic-info-grid {
        display: flex;
        flex-direction: column;
        gap: var(--space-1);
      }

      .logic-info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .targets-analysis {
        display: flex;
        flex-direction: column;
        gap: var(--space-3);
      }

      .target-group {
        display: flex;
        flex-direction: column;
        gap: var(--space-1);
      }

      .target-group-label {
        color: var(--text-secondary);
      }

      .target-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-1);
      }

      .target-tag {
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-sm);
        font-size: 0.75rem;
        font-weight: var(--font-normal);
      }

      .target-tag--success {
        background: var(--bg-glass-light);
        color: var(--color-success);
      }

      .target-tag--error {
        background: var(--bg-glass-light);
        color: var(--color-error);
      }

      /* Words Tab */
      .dev-words-tab {
        display: flex;
        flex-direction: column;
        gap: var(--space-6);
      }

      .word-input-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-4);
        margin-bottom: var(--space-4);
      }

      .word-input-group {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
      }

      .word-input-label {
        margin: 0;
      }

      .word-input {
        width: 100%;
        padding: var(--space-3);
        background: var(--bg-primary);
        border: 1px solid var(--bg-tertiary);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        font-family: inherit;
        transition: border-color var(--transition-base);
      }

      .word-input:focus {
        outline: none;
        border-color: var(--accent-cyan);
        box-shadow: var(--glow-cyan);
      }

      .word-input::placeholder {
        color: var(--text-muted);
      }

      /* Responsive Design */
      @media (max-width: calc(var(--bp-md) - 1px)) {
        .overview-grid,
        .analysis-grid,
        .logic-analysis-grid,
        .word-input-grid {
          grid-template-columns: 1fr;
        }

        .targets-grid {
          grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        }

        .dev-card {
          padding: var(--space-3);
        }
      }

      /* Accessibility */
      @media (prefers-reduced-motion: reduce) {
        .target-card {
          transition: none;
        }
      }
    `}</style>
  </>
);

export default DevPanelWithStyles;

window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":12,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":12,"startTime":1750502146987,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":12,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750502148115,"loadTestEnvironmentEnd":1750502147456,"loadTestEnvironmentStart":1750502147001,"runtime":657,"setupAfterEnvEnd":1750502147809,"setupAfterEnvStart":1750502147616,"setupFilesEnd":1750502147458,"setupFilesStart":1750502147458,"slow":false,"start":1750502147458},"testFilePath":"F:\\DefeaterAI\\components\\__tests__\\ErrorBoundary.test.tsx","failureMessage":null,"testResults":[{"ancestorTitles":["ErrorBoundary"],"duration":16,"failureMessages":[],"fullName":"ErrorBoundary renders children when there is no error","status":"passed","title":"renders children when there is no error"},{"ancestorTitles":["ErrorBoundary"],"duration":44,"failureMessages":[],"fullName":"ErrorBoundary catches and displays error with auto-retry UI for component level","status":"passed","title":"catches and displays error with auto-retry UI for component level"},{"ancestorTitles":["ErrorBoundary"],"duration":8,"failureMessages":[],"fullName":"ErrorBoundary displays different UI based on error level","status":"passed","title":"displays different UI based on error level"},{"ancestorTitles":["ErrorBoundary"],"duration":8,"failureMessages":[],"fullName":"ErrorBoundary calls custom error handler when provided","status":"passed","title":"calls custom error handler when provided"},{"ancestorTitles":["ErrorBoundary"],"duration":14,"failureMessages":[],"fullName":"ErrorBoundary shows section-level error UI with Try Again button","status":"passed","title":"shows section-level error UI with Try Again button"},{"ancestorTitles":["ErrorBoundary"],"duration":3,"failureMessages":[],"fullName":"ErrorBoundary renders custom fallback when provided","status":"passed","title":"renders custom fallback when provided"},{"ancestorTitles":["GameErrorBoundary"],"duration":7,"failureMessages":[],"fullName":"GameErrorBoundary renders game-specific error UI","status":"passed","title":"renders game-specific error UI"},{"ancestorTitles":["GameErrorBoundary"],"duration":5,"failureMessages":[],"fullName":"GameErrorBoundary calls game error handler when provided","status":"passed","title":"calls game error handler when provided"},{"ancestorTitles":["AsyncErrorBoundary"],"duration":13,"failureMessages":[],"fullName":"AsyncErrorBoundary renders async-specific error UI","status":"passed","title":"renders async-specific error UI"},{"ancestorTitles":["AsyncErrorBoundary"],"duration":7,"failureMessages":[],"fullName":"AsyncErrorBoundary calls async error handler when provided","status":"passed","title":"calls async error handler when provided"},{"ancestorTitles":["Error Boundary Integration"],"duration":6,"failureMessages":[],"fullName":"Error Boundary Integration isolates errors when isolate prop is true","status":"passed","title":"isolates errors when isolate prop is true"},{"ancestorTitles":["Error Boundary Integration"],"duration":5,"failureMessages":[],"fullName":"Error Boundary Integration resets on prop changes when resetOnPropsChange is true","status":"passed","title":"resets on prop changes when resetOnPropsChange is true"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["components/**/*.{js,jsx,ts,tsx}","pages/**/*.{js,jsx,ts,tsx}","utils/**/*.{js,jsx,ts,tsx}","hooks/**/*.{js,jsx,ts,tsx}","contexts/**/*.{js,jsx,ts,tsx}","!**/*.d.ts","!**/node_modules/**","!**/.next/**","!**/coverage/**"],"coverageDirectory":"F:\\DefeaterAI\\coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"coverageThreshold":{"global":{"branches":70,"functions":70,"lines":70,"statements":70}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":1,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\DefeaterAI\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"hideIcon":false,"pageTitle":"DEFEATER.AI Accessibility Test Report"}]],"rootDir":"F:\\DefeaterAI","runInBand":false,"runTestsByPath":false,"seed":-236671789,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["ErrorBoundary.test.tsx"],"type":"TestPathPatterns"},"testSequencer":"F:\\DefeaterAI\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1750502148124,"_reporterOptions":{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"pageTitle":"DEFEATER.AI Accessibility Test Report","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})
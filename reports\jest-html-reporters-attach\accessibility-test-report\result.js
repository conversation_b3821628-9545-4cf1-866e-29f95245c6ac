window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":20,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":20,"startTime":1750506203085,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":20,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750506205425,"loadTestEnvironmentEnd":1750506203559,"loadTestEnvironmentStart":1750506203100,"runtime":1864,"setupAfterEnvEnd":1750506203924,"setupAfterEnvStart":1750506203718,"setupFilesEnd":1750506203561,"setupFilesStart":1750506203561,"slow":false,"start":1750506203561},"testFilePath":"F:\\DefeaterAI\\tests\\accessibility-compliance.test.tsx","failureMessage":null,"testResults":[{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":97,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Main application should have no accessibility violations","status":"passed","title":"Main application should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":50,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Side panel should have no accessibility violations","status":"passed","title":"Side panel should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":69,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Modal should have no accessibility violations","status":"passed","title":"Modal should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":31,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Tab navigation should have no accessibility violations","status":"passed","title":"Tab navigation should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":42,"failureMessages":[],"fullName":"Accessibility Compliance Testing Keyboard Navigation All interactive elements should be keyboard accessible","status":"passed","title":"All interactive elements should be keyboard accessible"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":461,"failureMessages":[],"fullName":"Accessibility Compliance Testing Keyboard Navigation Tab navigation should follow logical order","status":"passed","title":"Tab navigation should follow logical order"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":105,"failureMessages":[],"fullName":"Accessibility Compliance Testing Keyboard Navigation Escape key should close modals and overlays","status":"passed","title":"Escape key should close modals and overlays"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":76,"failureMessages":[],"fullName":"Accessibility Compliance Testing Keyboard Navigation Arrow keys should navigate within tab groups","status":"passed","title":"Arrow keys should navigate within tab groups"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":5,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support All images should have alt text or be marked decorative","status":"passed","title":"All images should have alt text or be marked decorative"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support Form inputs should have proper labels","status":"passed","title":"Form inputs should have proper labels"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support Interactive elements should have accessible names","status":"passed","title":"Interactive elements should have accessible names"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":5,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support Headings should follow proper hierarchy","status":"passed","title":"Headings should follow proper hierarchy"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support ARIA landmarks should be present","status":"passed","title":"ARIA landmarks should be present"},{"ancestorTitles":["Accessibility Compliance Testing","Focus Management"],"duration":132,"failureMessages":[],"fullName":"Accessibility Compliance Testing Focus Management Focus should be trapped within modals","status":"passed","title":"Focus should be trapped within modals"},{"ancestorTitles":["Accessibility Compliance Testing","Focus Management"],"duration":122,"failureMessages":[],"fullName":"Accessibility Compliance Testing Focus Management Focus should return to trigger element after modal closes","status":"passed","title":"Focus should return to trigger element after modal closes"},{"ancestorTitles":["Accessibility Compliance Testing","Focus Management"],"duration":46,"failureMessages":[],"fullName":"Accessibility Compliance Testing Focus Management Skip links should be available for keyboard users","status":"passed","title":"Skip links should be available for keyboard users"},{"ancestorTitles":["Accessibility Compliance Testing","Color and Contrast"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Color and Contrast Text should have sufficient color contrast","status":"passed","title":"Text should have sufficient color contrast"},{"ancestorTitles":["Accessibility Compliance Testing","Color and Contrast"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Color and Contrast Interactive elements should have visible focus indicators","status":"passed","title":"Interactive elements should have visible focus indicators"},{"ancestorTitles":["Accessibility Compliance Testing","Touch and Mobile Accessibility"],"duration":5,"failureMessages":[],"fullName":"Accessibility Compliance Testing Touch and Mobile Accessibility Touch targets should meet minimum size requirements","status":"passed","title":"Touch targets should meet minimum size requirements"},{"ancestorTitles":["Accessibility Compliance Testing","Touch and Mobile Accessibility"],"duration":2,"failureMessages":[],"fullName":"Accessibility Compliance Testing Touch and Mobile Accessibility Content should be readable without horizontal scrolling","status":"passed","title":"Content should be readable without horizontal scrolling"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["components/**/*.{js,jsx,ts,tsx}","pages/**/*.{js,jsx,ts,tsx}","utils/**/*.{js,jsx,ts,tsx}","hooks/**/*.{js,jsx,ts,tsx}","contexts/**/*.{js,jsx,ts,tsx}","!**/*.d.ts","!**/node_modules/**","!**/.next/**","!**/coverage/**"],"coverageDirectory":"F:\\DefeaterAI\\coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"coverageThreshold":{"global":{"branches":70,"functions":70,"lines":70,"statements":70}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":15,"noStackTrace":false,"nonFlagArgs":["tests/accessibility-compliance.test.tsx"],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\DefeaterAI\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"hideIcon":false,"pageTitle":"DEFEATER.AI Accessibility Test Report"}]],"rootDir":"F:\\DefeaterAI","runInBand":false,"runTestsByPath":false,"seed":808149241,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["tests/accessibility-compliance.test.tsx"],"type":"TestPathPatterns"},"testSequencer":"F:\\DefeaterAI\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1750506205435,"_reporterOptions":{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"pageTitle":"DEFEATER.AI Accessibility Test Report","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})
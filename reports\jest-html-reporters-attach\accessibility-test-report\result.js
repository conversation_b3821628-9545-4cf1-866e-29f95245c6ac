window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":24,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":24,"startTime":1750500428279,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":24,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750500431507,"loadTestEnvironmentEnd":1750500428756,"loadTestEnvironmentStart":1750500428294,"runtime":2749,"setupAfterEnvEnd":1750500429108,"setupAfterEnvStart":1750500428913,"setupFilesEnd":1750500428758,"setupFilesStart":1750500428758,"slow":false,"start":1750500428758},"testFilePath":"F:\\DefeaterAI\\components\\__tests__\\GameBoard.test.tsx","failureMessage":null,"testResults":[{"ancestorTitles":["GameBoard Component","Component Initialization"],"duration":41,"failureMessages":[],"fullName":"GameBoard Component Component Initialization renders loading state when no initial game state provided","status":"passed","title":"renders loading state when no initial game state provided"},{"ancestorTitles":["GameBoard Component","Component Initialization"],"duration":22,"failureMessages":[],"fullName":"GameBoard Component Component Initialization renders with initial game state when provided","status":"passed","title":"renders with initial game state when provided"},{"ancestorTitles":["GameBoard Component","Component Initialization"],"duration":11,"failureMessages":[],"fullName":"GameBoard Component Component Initialization initializes with correct default UI state","status":"passed","title":"initializes with correct default UI state"},{"ancestorTitles":["GameBoard Component","Game State Management"],"duration":24,"failureMessages":[],"fullName":"GameBoard Component Game State Management starts new game successfully","status":"passed","title":"starts new game successfully"},{"ancestorTitles":["GameBoard Component","Game State Management"],"duration":6,"failureMessages":[],"fullName":"GameBoard Component Game State Management handles game start error gracefully","status":"passed","title":"handles game start error gracefully"},{"ancestorTitles":["GameBoard Component","Game State Management"],"duration":348,"failureMessages":[],"fullName":"GameBoard Component Game State Management updates game state correctly after successful definition submission","status":"passed","title":"updates game state correctly after successful definition submission"},{"ancestorTitles":["GameBoard Component","Game State Management"],"duration":400,"failureMessages":[],"fullName":"GameBoard Component Game State Management handles definition rejection correctly","status":"passed","title":"handles definition rejection correctly"},{"ancestorTitles":["GameBoard Component","User Interface Interactions"],"duration":62,"failureMessages":[],"fullName":"GameBoard Component User Interface Interactions toggles side panel correctly","status":"passed","title":"toggles side panel correctly"},{"ancestorTitles":["GameBoard Component","User Interface Interactions"],"duration":45,"failureMessages":[],"fullName":"GameBoard Component User Interface Interactions opens rules modal when rules button clicked","status":"passed","title":"opens rules modal when rules button clicked"},{"ancestorTitles":["GameBoard Component","User Interface Interactions"],"duration":31,"failureMessages":[],"fullName":"GameBoard Component User Interface Interactions handles difficulty selector correctly","status":"passed","title":"handles difficulty selector correctly"},{"ancestorTitles":["GameBoard Component","User Interface Interactions"],"duration":201,"failureMessages":[],"fullName":"GameBoard Component User Interface Interactions handles input changes with debouncing","status":"passed","title":"handles input changes with debouncing"},{"ancestorTitles":["GameBoard Component","Game Over States"],"duration":9,"failureMessages":[],"fullName":"GameBoard Component Game Over States displays victory screen when game is won","status":"passed","title":"displays victory screen when game is won"},{"ancestorTitles":["GameBoard Component","Game Over States"],"duration":9,"failureMessages":[],"fullName":"GameBoard Component Game Over States displays defeat screen when game is lost","status":"passed","title":"displays defeat screen when game is lost"},{"ancestorTitles":["GameBoard Component","Game Over States"],"duration":60,"failureMessages":[],"fullName":"GameBoard Component Game Over States shows post-game analysis when requested","status":"passed","title":"shows post-game analysis when requested"},{"ancestorTitles":["GameBoard Component","Error Handling"],"duration":229,"failureMessages":[],"fullName":"GameBoard Component Error Handling displays error messages correctly","status":"passed","title":"displays error messages correctly"},{"ancestorTitles":["GameBoard Component","Error Handling"],"duration":200,"failureMessages":[],"fullName":"GameBoard Component Error Handling handles network errors gracefully","status":"passed","title":"handles network errors gracefully"},{"ancestorTitles":["GameBoard Component","Performance Optimization"],"duration":6,"failureMessages":[],"fullName":"GameBoard Component Performance Optimization tracks component renders for performance monitoring","status":"passed","title":"tracks component renders for performance monitoring"},{"ancestorTitles":["GameBoard Component","Performance Optimization"],"duration":7,"failureMessages":[],"fullName":"GameBoard Component Performance Optimization optimizes game state for performance","status":"passed","title":"optimizes game state for performance"},{"ancestorTitles":["GameBoard Component","Performance Optimization"],"duration":324,"failureMessages":[],"fullName":"GameBoard Component Performance Optimization prevents race conditions in definition submission","status":"passed","title":"prevents race conditions in definition submission"},{"ancestorTitles":["GameBoard Component","Accessibility Compliance"],"duration":97,"failureMessages":[],"fullName":"GameBoard Component Accessibility Compliance has no accessibility violations","status":"passed","title":"has no accessibility violations"},{"ancestorTitles":["GameBoard Component","Accessibility Compliance"],"duration":8,"failureMessages":[],"fullName":"GameBoard Component Accessibility Compliance provides proper ARIA labels","status":"passed","title":"provides proper ARIA labels"},{"ancestorTitles":["GameBoard Component","Accessibility Compliance"],"duration":6,"failureMessages":[],"fullName":"GameBoard Component Accessibility Compliance supports keyboard navigation","status":"passed","title":"supports keyboard navigation"},{"ancestorTitles":["GameBoard Component","Component Integration"],"duration":8,"failureMessages":[],"fullName":"GameBoard Component Component Integration renders all required child components","status":"passed","title":"renders all required child components"},{"ancestorTitles":["GameBoard Component","Component Integration"],"duration":5,"failureMessages":[],"fullName":"GameBoard Component Component Integration passes correct props to child components","status":"passed","title":"passes correct props to child components"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["components/**/*.{js,jsx,ts,tsx}","pages/**/*.{js,jsx,ts,tsx}","utils/**/*.{js,jsx,ts,tsx}","hooks/**/*.{js,jsx,ts,tsx}","contexts/**/*.{js,jsx,ts,tsx}","!**/*.d.ts","!**/node_modules/**","!**/.next/**","!**/coverage/**"],"coverageDirectory":"F:\\DefeaterAI\\coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"coverageThreshold":{"global":{"branches":70,"functions":70,"lines":70,"statements":70}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":1,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\DefeaterAI\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"hideIcon":false,"pageTitle":"DEFEATER.AI Accessibility Test Report"}]],"rootDir":"F:\\DefeaterAI","runInBand":false,"runTestsByPath":false,"seed":-796545805,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["GameBoard.test.tsx"],"type":"TestPathPatterns"},"testSequencer":"F:\\DefeaterAI\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1750500431517,"_reporterOptions":{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"pageTitle":"DEFEATER.AI Accessibility Test Report","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})
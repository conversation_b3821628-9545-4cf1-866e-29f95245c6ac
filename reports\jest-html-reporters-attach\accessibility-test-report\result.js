window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":15,"numPassedTestSuites":0,"numPassedTests":1,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":16,"startTime":1750418718642,"success":false,"testResults":[{"numFailingTests":15,"numPassingTests":1,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750418720177,"loadTestEnvironmentEnd":1750418719096,"loadTestEnvironmentStart":1750418718656,"runtime":1080,"setupAfterEnvEnd":1750418719432,"setupAfterEnvStart":1750418719248,"setupFilesEnd":1750418719097,"setupFilesStart":1750418719097,"slow":false,"start":1750418719097},"testFilePath":"F:\\DefeaterAI\\tests\\performance-impact.test.tsx","failureMessage":"\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › Rendering Performance › Main app should render within acceptable time\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useGameAnimations (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:44:19)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat GameBoard (\u001b[22m\u001b[2mcomponents/GameBoard.tsx\u001b[2m:192:43)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:93:15\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fn (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:38:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.measurePerformance (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:92:29)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › Rendering Performance › Test page should render within acceptable time\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:107:15\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fn (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:38:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.measurePerformance (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:106:29)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › Rendering Performance › Component re-renders should be efficient\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useGameAnimations (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:44:19)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat GameBoard (\u001b[22m\u001b[2mcomponents/GameBoard.tsx\u001b[2m:192:43)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:115:34)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › Memory Usage › Memory usage should be reasonable\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useGameAnimations (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:44:19)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat GameBoard (\u001b[22m\u001b[2mcomponents/GameBoard.tsx\u001b[2m:192:43)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:136:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › Memory Usage › Memory should not leak on component unmount\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:159:33)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › DOM Complexity › DOM node count should be reasonable\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useGameAnimations (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:44:19)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat GameBoard (\u001b[22m\u001b[2mcomponents/GameBoard.tsx\u001b[2m:192:43)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:182:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › DOM Complexity › CSS class count should be optimized\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:200:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › CSS Performance › CSS custom properties should be efficiently used\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:226:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › CSS Performance › Animation performance should be optimized\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:248:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › CSS Performance › CSS selector complexity should be reasonable\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:264:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › Bundle Size Impact › CSS-in-JS should not cause excessive style tags\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:313:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › Responsive Performance › Viewport changes should not cause layout thrashing\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:333:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › Responsive Performance › Media queries should be efficiently structured\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:354:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › Accessibility Performance › ARIA attributes should not impact performance\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:382:13)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mPerformance Impact Assessment › Accessibility Performance › Focus management should be performant\u001b[39m\u001b[22m\n\n    useAnimation must be used within an AnimationProvider\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 158 |\u001b[39m   \u001b[36mconst\u001b[39m context \u001b[33m=\u001b[39m useContext(\u001b[33mAnimationContext\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 159 |\u001b[39m   \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mcontext) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 160 |\u001b[39m     \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m'useAnimation must be used within an AnimationProvider'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m   }\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m   \u001b[36mreturn\u001b[39m context\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 163 |\u001b[39m }\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useAnimation (\u001b[22m\u001b[2mcontexts/AnimationContext.tsx\u001b[2m:160:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat useComponentAnimation (\u001b[22m\u001b[2mhooks/useGameAnimations.ts\u001b[2m:214:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat CurrentChallenge (\u001b[22m\u001b[2mcomponents/game/CurrentChallenge.tsx\u001b[2m:48:51)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderWithHooks (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:15486:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat mountIndeterminateComponent (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:20103:13)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:21626:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat beginWork$1 (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:27465:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performUnitOfWork (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26599:12)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat workLoopSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26505:5)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRootSync (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:26473:7)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat recoverFromConcurrentError (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25889:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat performConcurrentWorkOnRoot (\u001b[22m\u001b[2mnode_modules/react-dom/cjs/react-dom.development.js\u001b[2m:25789:22)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat flushActQueue (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2667:24)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat act (\u001b[22m\u001b[2mnode_modules/react/cjs/react.development.js\u001b[2m:2582:11)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat \u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/act-compat.js\u001b[2m:47:25\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat renderRoot (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:190:26)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat render (\u001b[22m\u001b[2mnode_modules/@testing-library/react/dist/pure.js\u001b[2m:292:10)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/performance-impact.test.tsx\u001b[39m\u001b[0m\u001b[2m:396:13)\u001b[22m\u001b[2m\u001b[22m\n","testResults":[{"ancestorTitles":["Performance Impact Assessment","Rendering Performance"],"duration":47,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useGameAnimations (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:44:19)\n    at GameBoard (F:\\DefeaterAI\\components\\GameBoard.tsx:192:43)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at F:\\DefeaterAI\\tests\\performance-impact.test.tsx:93:15\n    at fn (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:38:3)\n    at Object.measurePerformance (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:92:29)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment Rendering Performance Main app should render within acceptable time","status":"failed","title":"Main app should render within acceptable time"},{"ancestorTitles":["Performance Impact Assessment","Rendering Performance"],"duration":79,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at F:\\DefeaterAI\\tests\\performance-impact.test.tsx:107:15\n    at fn (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:38:3)\n    at Object.measurePerformance (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:106:29)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment Rendering Performance Test page should render within acceptable time","status":"failed","title":"Test page should render within acceptable time"},{"ancestorTitles":["Performance Impact Assessment","Rendering Performance"],"duration":3,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useGameAnimations (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:44:19)\n    at GameBoard (F:\\DefeaterAI\\components\\GameBoard.tsx:192:43)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:115:34)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment Rendering Performance Component re-renders should be efficient","status":"failed","title":"Component re-renders should be efficient"},{"ancestorTitles":["Performance Impact Assessment","Memory Usage"],"duration":3,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useGameAnimations (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:44:19)\n    at GameBoard (F:\\DefeaterAI\\components\\GameBoard.tsx:192:43)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:136:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment Memory Usage Memory usage should be reasonable","status":"failed","title":"Memory usage should be reasonable"},{"ancestorTitles":["Performance Impact Assessment","Memory Usage"],"duration":41,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:159:33)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment Memory Usage Memory should not leak on component unmount","status":"failed","title":"Memory should not leak on component unmount"},{"ancestorTitles":["Performance Impact Assessment","DOM Complexity"],"duration":4,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useGameAnimations (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:44:19)\n    at GameBoard (F:\\DefeaterAI\\components\\GameBoard.tsx:192:43)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:182:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment DOM Complexity DOM node count should be reasonable","status":"failed","title":"DOM node count should be reasonable"},{"ancestorTitles":["Performance Impact Assessment","DOM Complexity"],"duration":37,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:200:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment DOM Complexity CSS class count should be optimized","status":"failed","title":"CSS class count should be optimized"},{"ancestorTitles":["Performance Impact Assessment","CSS Performance"],"duration":32,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:226:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment CSS Performance CSS custom properties should be efficiently used","status":"failed","title":"CSS custom properties should be efficiently used"},{"ancestorTitles":["Performance Impact Assessment","CSS Performance"],"duration":34,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:248:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment CSS Performance Animation performance should be optimized","status":"failed","title":"Animation performance should be optimized"},{"ancestorTitles":["Performance Impact Assessment","CSS Performance"],"duration":25,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:264:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment CSS Performance CSS selector complexity should be reasonable","status":"failed","title":"CSS selector complexity should be reasonable"},{"ancestorTitles":["Performance Impact Assessment","Bundle Size Impact"],"duration":3,"failureMessages":[],"fullName":"Performance Impact Assessment Bundle Size Impact Component imports should be tree-shakeable","status":"passed","title":"Component imports should be tree-shakeable"},{"ancestorTitles":["Performance Impact Assessment","Bundle Size Impact"],"duration":30,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:313:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment Bundle Size Impact CSS-in-JS should not cause excessive style tags","status":"failed","title":"CSS-in-JS should not cause excessive style tags"},{"ancestorTitles":["Performance Impact Assessment","Responsive Performance"],"duration":21,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:333:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment Responsive Performance Viewport changes should not cause layout thrashing","status":"failed","title":"Viewport changes should not cause layout thrashing"},{"ancestorTitles":["Performance Impact Assessment","Responsive Performance"],"duration":22,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:354:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment Responsive Performance Media queries should be efficiently structured","status":"failed","title":"Media queries should be efficiently structured"},{"ancestorTitles":["Performance Impact Assessment","Accessibility Performance"],"duration":29,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:382:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment Accessibility Performance ARIA attributes should not impact performance","status":"failed","title":"ARIA attributes should not impact performance"},{"ancestorTitles":["Performance Impact Assessment","Accessibility Performance"],"duration":20,"failureMessages":["Error: useAnimation must be used within an AnimationProvider\n    at useAnimation (F:\\DefeaterAI\\contexts\\AnimationContext.tsx:160:11)\n    at useComponentAnimation (F:\\DefeaterAI\\hooks\\useGameAnimations.ts:214:51)\n    at CurrentChallenge (F:\\DefeaterAI\\components\\game\\CurrentChallenge.tsx:48:51)\n    at renderWithHooks (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:15486:18)\n    at mountIndeterminateComponent (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:20103:13)\n    at beginWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:21626:16)\n    at beginWork$1 (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:27465:14)\n    at performUnitOfWork (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26599:12)\n    at workLoopSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26505:5)\n    at renderRootSync (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:26473:7)\n    at recoverFromConcurrentError (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25889:20)\n    at performConcurrentWorkOnRoot (F:\\DefeaterAI\\node_modules\\react-dom\\cjs\\react-dom.development.js:25789:22)\n    at flushActQueue (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2667:24)\n    at act (F:\\DefeaterAI\\node_modules\\react\\cjs\\react.development.js:2582:11)\n    at F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\act-compat.js:47:25\n    at renderRoot (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:190:26)\n    at render (F:\\DefeaterAI\\node_modules\\@testing-library\\react\\dist\\pure.js:292:10)\n    at Object.<anonymous> (F:\\DefeaterAI\\tests\\performance-impact.test.tsx:396:13)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Performance Impact Assessment Accessibility Performance Focus management should be performant","status":"failed","title":"Focus management should be performant"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["components/**/*.{js,jsx,ts,tsx}","pages/**/*.{js,jsx,ts,tsx}","utils/**/*.{js,jsx,ts,tsx}","hooks/**/*.{js,jsx,ts,tsx}","contexts/**/*.{js,jsx,ts,tsx}","!**/*.d.ts","!**/node_modules/**","!**/.next/**","!**/coverage/**"],"coverageDirectory":"F:\\DefeaterAI\\coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"coverageThreshold":{"global":{"branches":70,"functions":70,"lines":70,"statements":70}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":15,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\DefeaterAI\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"hideIcon":false,"pageTitle":"DEFEATER.AI Accessibility Test Report"}]],"rootDir":"F:\\DefeaterAI","runInBand":false,"runTestsByPath":false,"seed":**********,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["performance-impact.test.tsx"],"type":"TestPathPatterns"},"testSequencer":"F:\\DefeaterAI\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1750418720192,"_reporterOptions":{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"pageTitle":"DEFEATER.AI Accessibility Test Report","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})
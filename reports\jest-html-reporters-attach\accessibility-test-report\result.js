window.jest_html_reporters_callback__({"numFailedTestSuites":3,"numFailedTests":6,"numPassedTestSuites":4,"numPassedTests":110,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":7,"numTotalTests":116,"startTime":1750463175208,"success":false,"testResults":[{"numFailingTests":3,"numPassingTests":17,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750463177275,"loadTestEnvironmentEnd":1750463175762,"loadTestEnvironmentStart":1750463175225,"runtime":1511,"setupAfterEnvEnd":1750463176204,"setupAfterEnvStart":1750463175964,"setupFilesEnd":1750463175764,"setupFilesStart":1750463175764,"slow":false,"start":1750463175764},"testFilePath":"F:\\DefeaterAI\\tests\\accessibility-compliance.test.tsx","failureMessage":"\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mAccessibility Compliance Testing › Keyboard Navigation › All interactive elements should be keyboard accessible\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31melement\u001b[39m\u001b[2m).toHaveFocus()\u001b[22m\n\n    Expected element with focus:\n      \u001b[32m<button style=\"min-height: 44px; min-width: 44px;\" type=\"submit\">Submit Definition</button>\u001b[39m\n    Received element with focus:\n      \u001b[31m<body style=\"\"><div><div class=\"game-layout\" data-testid=\"mock-open-design-test-page\" style=\"font-family: Inter, system-ui, sans-serif;\"><main role=\"main\"><h1 class=\"text-bold font-extrabold\">DEFEATER.AI - Design Test</h1><div class=\"game-focus\" data-testid=\"game-focus\"><div data-testid=\"current-challenge\">TRANSFORMER</div><input aria-label=\"Define the word\" class=\"mobile:w-full sm:w-auto\" placeholder=\"Define the word...\" role=\"textbox\" style=\"min-height: 44px; min-width: 44px;\" /><button style=\"min-height: 44px; min-width: 44px;\" type=\"submit\">Submit Definition</button></div><aside role=\"complementary\"><button aria-label=\"View stats\" style=\"min-height: 44px; min-width: 44px;\">Stats</button><button aria-label=\"View history\" style=\"min-height: 44px; min-width: 44px;\">History</button></aside><button aria-label=\"Toggle panel\" class=\"toggle-button mobile:block modal-trigger\" style=\"min-height: 44px; min-width: 44px;\">Menu</button><button aria-label=\"Open rules modal\" style=\"min-height: 44px; min-width: 44px;\">Rules</button><div class=\"target-strip\" data-testid=\"target-revelation\"><span>I n n o v _ _ _ _ _</span><span>E c o s y _ _ _ _</span><span>F r i c _ _ _ _</span><span>P a r a _ _ _ _</span></div></main></div></div></body>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 125 |\u001b[39m       \u001b[36mfor\u001b[39m (\u001b[36mconst\u001b[39m element \u001b[36mof\u001b[39m focusableElements) {\u001b[22m\n\u001b[2m     \u001b[90m 126 |\u001b[39m         element\u001b[33m.\u001b[39mfocus()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 127 |\u001b[39m         expect(element)\u001b[33m.\u001b[39mtoHaveFocus()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                         \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 128 |\u001b[39m         \u001b[22m\n\u001b[2m     \u001b[90m 129 |\u001b[39m         \u001b[90m// Test that element responds to Enter key\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 130 |\u001b[39m         \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mkeyboard(\u001b[32m'{Enter}'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toHaveFocus (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/accessibility-compliance.test.tsx\u001b[39m\u001b[0m\u001b[2m:127:25)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mAccessibility Compliance Testing › Keyboard Navigation › Tab navigation should follow logical order\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected: > \u001b[32m0\u001b[39m\n    Received:   \u001b[31m0\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 157 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 158 |\u001b[39m       \u001b[90m// Should have found focusable elements\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 159 |\u001b[39m       expect(focusableElements\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeGreaterThan(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 160 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 161 |\u001b[39m       \u001b[90m// Each element should be unique in the tab order\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 162 |\u001b[39m       \u001b[36mconst\u001b[39m uniqueElements \u001b[33m=\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mSet\u001b[39m(focusableElements)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toBeGreaterThan (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/accessibility-compliance.test.tsx\u001b[39m\u001b[0m\u001b[2m:159:40)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mAccessibility Compliance Testing › Focus Management › Focus should be trapped within modals\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\n    Expected value: \u001b[32m<button aria-label=\"Close modal\" class=\"jsx-9ebc622438ecb5e2 modal-close-button\" type=\"button\">×</button>\u001b[39m\n    Received array: \u001b[31m[<input placeholder=\"Modal input 1\" />, <button>Modal button</button>, <input placeholder=\"Modal input 2\" />]\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 357 |\u001b[39m       \u001b[90m// Tab through modal elements\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 358 |\u001b[39m       \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mtab()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 359 |\u001b[39m       expect([modalInput1\u001b[33m,\u001b[39m modalButton\u001b[33m,\u001b[39m modalInput2])\u001b[33m.\u001b[39mtoContain(document\u001b[33m.\u001b[39mactiveElement)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 360 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 361 |\u001b[39m       \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mtab()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 362 |\u001b[39m       expect([modalInput1\u001b[33m,\u001b[39m modalButton\u001b[33m,\u001b[39m modalInput2])\u001b[33m.\u001b[39mtoContain(document\u001b[33m.\u001b[39mactiveElement)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toContain (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/accessibility-compliance.test.tsx\u001b[39m\u001b[0m\u001b[2m:359:55)\u001b[22m\u001b[2m\u001b[22m\n","testResults":[{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":91,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Main application should have no accessibility violations","status":"passed","title":"Main application should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":71,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Side panel should have no accessibility violations","status":"passed","title":"Side panel should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":41,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Modal should have no accessibility violations","status":"passed","title":"Modal should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":32,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Tab navigation should have no accessibility violations","status":"passed","title":"Tab navigation should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":20,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31melement\u001b[39m\u001b[2m).toHaveFocus()\u001b[22m\n\nExpected element with focus:\n  \u001b[32m<button style=\"min-height: 44px; min-width: 44px;\" type=\"submit\">Submit Definition</button>\u001b[39m\nReceived element with focus:\n  \u001b[31m<body style=\"\"><div><div class=\"game-layout\" data-testid=\"mock-open-design-test-page\" style=\"font-family: Inter, system-ui, sans-serif;\"><main role=\"main\"><h1 class=\"text-bold font-extrabold\">DEFEATER.AI - Design Test</h1><div class=\"game-focus\" data-testid=\"game-focus\"><div data-testid=\"current-challenge\">TRANSFORMER</div><input aria-label=\"Define the word\" class=\"mobile:w-full sm:w-auto\" placeholder=\"Define the word...\" role=\"textbox\" style=\"min-height: 44px; min-width: 44px;\" /><button style=\"min-height: 44px; min-width: 44px;\" type=\"submit\">Submit Definition</button></div><aside role=\"complementary\"><button aria-label=\"View stats\" style=\"min-height: 44px; min-width: 44px;\">Stats</button><button aria-label=\"View history\" style=\"min-height: 44px; min-width: 44px;\">History</button></aside><button aria-label=\"Toggle panel\" class=\"toggle-button mobile:block modal-trigger\" style=\"min-height: 44px; min-width: 44px;\">Menu</button><button aria-label=\"Open rules modal\" style=\"min-height: 44px; min-width: 44px;\">Rules</button><div class=\"target-strip\" data-testid=\"target-revelation\"><span>I n n o v _ _ _ _ _</span><span>E c o s y _ _ _ _</span><span>F r i c _ _ _ _</span><span>P a r a _ _ _ _</span></div></main></div></div></body>\u001b[39m\n    at Object.toHaveFocus (F:\\DefeaterAI\\tests\\accessibility-compliance.test.tsx:127:25)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Accessibility Compliance Testing Keyboard Navigation All interactive elements should be keyboard accessible","status":"failed","title":"All interactive elements should be keyboard accessible"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":50,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected: > \u001b[32m0\u001b[39m\nReceived:   \u001b[31m0\u001b[39m\n    at Object.toBeGreaterThan (F:\\DefeaterAI\\tests\\accessibility-compliance.test.tsx:159:40)"],"fullName":"Accessibility Compliance Testing Keyboard Navigation Tab navigation should follow logical order","status":"failed","title":"Tab navigation should follow logical order"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":120,"failureMessages":[],"fullName":"Accessibility Compliance Testing Keyboard Navigation Escape key should close modals and overlays","status":"passed","title":"Escape key should close modals and overlays"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":91,"failureMessages":[],"fullName":"Accessibility Compliance Testing Keyboard Navigation Arrow keys should navigate within tab groups","status":"passed","title":"Arrow keys should navigate within tab groups"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":5,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support All images should have alt text or be marked decorative","status":"passed","title":"All images should have alt text or be marked decorative"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":5,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support Form inputs should have proper labels","status":"passed","title":"Form inputs should have proper labels"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":9,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support Interactive elements should have accessible names","status":"passed","title":"Interactive elements should have accessible names"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":8,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support Headings should follow proper hierarchy","status":"passed","title":"Headings should follow proper hierarchy"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":7,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support ARIA landmarks should be present","status":"passed","title":"ARIA landmarks should be present"},{"ancestorTitles":["Accessibility Compliance Testing","Focus Management"],"duration":89,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoContain\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // indexOf\u001b[22m\n\nExpected value: \u001b[32m<button aria-label=\"Close modal\" class=\"jsx-9ebc622438ecb5e2 modal-close-button\" type=\"button\">×</button>\u001b[39m\nReceived array: \u001b[31m[<input placeholder=\"Modal input 1\" />, <button>Modal button</button>, <input placeholder=\"Modal input 2\" />]\u001b[39m\n    at Object.toContain (F:\\DefeaterAI\\tests\\accessibility-compliance.test.tsx:359:55)"],"fullName":"Accessibility Compliance Testing Focus Management Focus should be trapped within modals","status":"failed","title":"Focus should be trapped within modals"},{"ancestorTitles":["Accessibility Compliance Testing","Focus Management"],"duration":88,"failureMessages":[],"fullName":"Accessibility Compliance Testing Focus Management Focus should return to trigger element after modal closes","status":"passed","title":"Focus should return to trigger element after modal closes"},{"ancestorTitles":["Accessibility Compliance Testing","Focus Management"],"duration":46,"failureMessages":[],"fullName":"Accessibility Compliance Testing Focus Management Skip links should be available for keyboard users","status":"passed","title":"Skip links should be available for keyboard users"},{"ancestorTitles":["Accessibility Compliance Testing","Color and Contrast"],"duration":8,"failureMessages":[],"fullName":"Accessibility Compliance Testing Color and Contrast Text should have sufficient color contrast","status":"passed","title":"Text should have sufficient color contrast"},{"ancestorTitles":["Accessibility Compliance Testing","Color and Contrast"],"duration":8,"failureMessages":[],"fullName":"Accessibility Compliance Testing Color and Contrast Interactive elements should have visible focus indicators","status":"passed","title":"Interactive elements should have visible focus indicators"},{"ancestorTitles":["Accessibility Compliance Testing","Touch and Mobile Accessibility"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Touch and Mobile Accessibility Touch targets should meet minimum size requirements","status":"passed","title":"Touch targets should meet minimum size requirements"},{"ancestorTitles":["Accessibility Compliance Testing","Touch and Mobile Accessibility"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Touch and Mobile Accessibility Content should be readable without horizontal scrolling","status":"passed","title":"Content should be readable without horizontal scrolling"}]},{"numFailingTests":0,"numPassingTests":24,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750463179676,"loadTestEnvironmentEnd":1750463177299,"loadTestEnvironmentStart":1750463177286,"runtime":2376,"setupAfterEnvEnd":1750463177425,"setupAfterEnvStart":1750463177341,"setupFilesEnd":1750463177300,"setupFilesStart":1750463177300,"slow":false,"start":1750463177300},"testFilePath":"F:\\DefeaterAI\\components\\__tests__\\GameBoard.test.tsx","failureMessage":null,"testResults":[{"ancestorTitles":["GameBoard Component","Component Initialization"],"duration":36,"failureMessages":[],"fullName":"GameBoard Component Component Initialization renders loading state when no initial game state provided","status":"passed","title":"renders loading state when no initial game state provided"},{"ancestorTitles":["GameBoard Component","Component Initialization"],"duration":23,"failureMessages":[],"fullName":"GameBoard Component Component Initialization renders with initial game state when provided","status":"passed","title":"renders with initial game state when provided"},{"ancestorTitles":["GameBoard Component","Component Initialization"],"duration":11,"failureMessages":[],"fullName":"GameBoard Component Component Initialization initializes with correct default UI state","status":"passed","title":"initializes with correct default UI state"},{"ancestorTitles":["GameBoard Component","Game State Management"],"duration":23,"failureMessages":[],"fullName":"GameBoard Component Game State Management starts new game successfully","status":"passed","title":"starts new game successfully"},{"ancestorTitles":["GameBoard Component","Game State Management"],"duration":6,"failureMessages":[],"fullName":"GameBoard Component Game State Management handles game start error gracefully","status":"passed","title":"handles game start error gracefully"},{"ancestorTitles":["GameBoard Component","Game State Management"],"duration":355,"failureMessages":[],"fullName":"GameBoard Component Game State Management updates game state correctly after successful definition submission","status":"passed","title":"updates game state correctly after successful definition submission"},{"ancestorTitles":["GameBoard Component","Game State Management"],"duration":397,"failureMessages":[],"fullName":"GameBoard Component Game State Management handles definition rejection correctly","status":"passed","title":"handles definition rejection correctly"},{"ancestorTitles":["GameBoard Component","User Interface Interactions"],"duration":46,"failureMessages":[],"fullName":"GameBoard Component User Interface Interactions toggles side panel correctly","status":"passed","title":"toggles side panel correctly"},{"ancestorTitles":["GameBoard Component","User Interface Interactions"],"duration":46,"failureMessages":[],"fullName":"GameBoard Component User Interface Interactions opens rules modal when rules button clicked","status":"passed","title":"opens rules modal when rules button clicked"},{"ancestorTitles":["GameBoard Component","User Interface Interactions"],"duration":30,"failureMessages":[],"fullName":"GameBoard Component User Interface Interactions handles difficulty selector correctly","status":"passed","title":"handles difficulty selector correctly"},{"ancestorTitles":["GameBoard Component","User Interface Interactions"],"duration":181,"failureMessages":[],"fullName":"GameBoard Component User Interface Interactions handles input changes with debouncing","status":"passed","title":"handles input changes with debouncing"},{"ancestorTitles":["GameBoard Component","Game Over States"],"duration":13,"failureMessages":[],"fullName":"GameBoard Component Game Over States displays victory screen when game is won","status":"passed","title":"displays victory screen when game is won"},{"ancestorTitles":["GameBoard Component","Game Over States"],"duration":13,"failureMessages":[],"fullName":"GameBoard Component Game Over States displays defeat screen when game is lost","status":"passed","title":"displays defeat screen when game is lost"},{"ancestorTitles":["GameBoard Component","Game Over States"],"duration":52,"failureMessages":[],"fullName":"GameBoard Component Game Over States shows post-game analysis when requested","status":"passed","title":"shows post-game analysis when requested"},{"ancestorTitles":["GameBoard Component","Error Handling"],"duration":243,"failureMessages":[],"fullName":"GameBoard Component Error Handling displays error messages correctly","status":"passed","title":"displays error messages correctly"},{"ancestorTitles":["GameBoard Component","Error Handling"],"duration":185,"failureMessages":[],"fullName":"GameBoard Component Error Handling handles network errors gracefully","status":"passed","title":"handles network errors gracefully"},{"ancestorTitles":["GameBoard Component","Performance Optimization"],"duration":7,"failureMessages":[],"fullName":"GameBoard Component Performance Optimization tracks component renders for performance monitoring","status":"passed","title":"tracks component renders for performance monitoring"},{"ancestorTitles":["GameBoard Component","Performance Optimization"],"duration":9,"failureMessages":[],"fullName":"GameBoard Component Performance Optimization optimizes game state for performance","status":"passed","title":"optimizes game state for performance"},{"ancestorTitles":["GameBoard Component","Performance Optimization"],"duration":319,"failureMessages":[],"fullName":"GameBoard Component Performance Optimization prevents race conditions in definition submission","status":"passed","title":"prevents race conditions in definition submission"},{"ancestorTitles":["GameBoard Component","Accessibility Compliance"],"duration":78,"failureMessages":[],"fullName":"GameBoard Component Accessibility Compliance has no accessibility violations","status":"passed","title":"has no accessibility violations"},{"ancestorTitles":["GameBoard Component","Accessibility Compliance"],"duration":6,"failureMessages":[],"fullName":"GameBoard Component Accessibility Compliance provides proper ARIA labels","status":"passed","title":"provides proper ARIA labels"},{"ancestorTitles":["GameBoard Component","Accessibility Compliance"],"duration":5,"failureMessages":[],"fullName":"GameBoard Component Accessibility Compliance supports keyboard navigation","status":"passed","title":"supports keyboard navigation"},{"ancestorTitles":["GameBoard Component","Component Integration"],"duration":13,"failureMessages":[],"fullName":"GameBoard Component Component Integration renders all required child components","status":"passed","title":"renders all required child components"},{"ancestorTitles":["GameBoard Component","Component Integration"],"duration":9,"failureMessages":[],"fullName":"GameBoard Component Component Integration passes correct props to child components","status":"passed","title":"passes correct props to child components"}]},{"numFailingTests":2,"numPassingTests":18,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750463180168,"loadTestEnvironmentEnd":1750463179693,"loadTestEnvironmentStart":1750463179683,"runtime":475,"setupAfterEnvEnd":1750463179815,"setupAfterEnvStart":1750463179735,"setupFilesEnd":1750463179693,"setupFilesStart":1750463179693,"slow":false,"start":1750463179693},"testFilePath":"F:\\DefeaterAI\\tests\\cross-browser-compatibility.test.tsx","failureMessage":"\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCross-Browser Compatibility Testing › Component Interactions › Input focus should work consistently\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31melement\u001b[39m\u001b[2m).toHaveFocus()\u001b[22m\n\n    Expected element with focus:\n      \u001b[32m<input aria-label=\"Define the word\" class=\"mobile:w-full sm:w-auto\" placeholder=\"Define the word...\" role=\"textbox\" style=\"min-height: 44px; min-width: 44px;\" />\u001b[39m\n    Received element with focus:\n      \u001b[31m<button aria-label=\"Toggle panel\" class=\"toggle-button mobile:block modal-trigger\" style=\"min-height: 44px; min-width: 44px;\">Menu</button>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 261 |\u001b[39m       \u001b[36mif\u001b[39m (textInput) {\u001b[22m\n\u001b[2m     \u001b[90m 262 |\u001b[39m         \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mclick(textInput)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 263 |\u001b[39m         expect(textInput)\u001b[33m.\u001b[39mtoHaveFocus()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 264 |\u001b[39m         \u001b[22m\n\u001b[2m     \u001b[90m 265 |\u001b[39m         \u001b[90m// Test typing\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 266 |\u001b[39m         \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mtype(textInput\u001b[33m,\u001b[39m \u001b[32m'test input'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toHaveFocus (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/cross-browser-compatibility.test.tsx\u001b[39m\u001b[0m\u001b[2m:263:27)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mCross-Browser Compatibility Testing › Component Interactions › Keyboard navigation should work\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mnot\u001b[2m.\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: not \u001b[32m<button aria-label=\"Toggle panel\" class=\"toggle-button mobile:block modal-trigger\" style=\"min-height: 44px; min-width: 44px;\">Menu</button>\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 281 |\u001b[39m       \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mtab()\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 282 |\u001b[39m       \u001b[36mconst\u001b[39m secondFocusable \u001b[33m=\u001b[39m document\u001b[33m.\u001b[39mactiveElement\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 283 |\u001b[39m       expect(secondFocusable)\u001b[33m.\u001b[39mnot\u001b[33m.\u001b[39mtoBe(firstFocusable)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 284 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 285 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 286 |\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toBe (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/cross-browser-compatibility.test.tsx\u001b[39m\u001b[0m\u001b[2m:283:35)\u001b[22m\u001b[2m\u001b[22m\n","testResults":[{"ancestorTitles":["Cross-Browser Compatibility Testing","CSS Variable Support"],"duration":5,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing CSS Variable Support CSS custom properties should be supported","status":"passed","title":"CSS custom properties should be supported"},{"ancestorTitles":["Cross-Browser Compatibility Testing","CSS Variable Support"],"duration":3,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing CSS Variable Support Design system variables should be accessible","status":"passed","title":"Design system variables should be accessible"},{"ancestorTitles":["Cross-Browser Compatibility Testing","CSS Variable Support"],"duration":2,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing CSS Variable Support Color variables should resolve correctly","status":"passed","title":"Color variables should resolve correctly"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Responsive Behavior"],"duration":4,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Responsive Behavior Viewport meta tag should be present","status":"passed","title":"Viewport meta tag should be present"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Responsive Behavior"],"duration":2,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Responsive Behavior Media queries should work correctly","status":"passed","title":"Media queries should work correctly"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Responsive Behavior"],"duration":2,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Responsive Behavior Breakpoint variables should work","status":"passed","title":"Breakpoint variables should work"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Responsive Behavior"],"duration":6,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Responsive Behavior Touch targets should be appropriately sized","status":"passed","title":"Touch targets should be appropriately sized"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Component Interactions"],"duration":17,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Component Interactions Modal interactions should work across browsers","status":"passed","title":"Modal interactions should work across browsers"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Component Interactions"],"duration":24,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Component Interactions Side panel should work on all browsers","status":"passed","title":"Side panel should work on all browsers"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Component Interactions"],"duration":67,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31melement\u001b[39m\u001b[2m).toHaveFocus()\u001b[22m\n\nExpected element with focus:\n  \u001b[32m<input aria-label=\"Define the word\" class=\"mobile:w-full sm:w-auto\" placeholder=\"Define the word...\" role=\"textbox\" style=\"min-height: 44px; min-width: 44px;\" />\u001b[39m\nReceived element with focus:\n  \u001b[31m<button aria-label=\"Toggle panel\" class=\"toggle-button mobile:block modal-trigger\" style=\"min-height: 44px; min-width: 44px;\">Menu</button>\u001b[39m\n    at Object.toHaveFocus (F:\\DefeaterAI\\tests\\cross-browser-compatibility.test.tsx:263:27)"],"fullName":"Cross-Browser Compatibility Testing Component Interactions Input focus should work consistently","status":"failed","title":"Input focus should work consistently"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Component Interactions"],"duration":83,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mnot\u001b[2m.\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: not \u001b[32m<button aria-label=\"Toggle panel\" class=\"toggle-button mobile:block modal-trigger\" style=\"min-height: 44px; min-width: 44px;\">Menu</button>\u001b[39m\n    at Object.toBe (F:\\DefeaterAI\\tests\\cross-browser-compatibility.test.tsx:283:35)"],"fullName":"Cross-Browser Compatibility Testing Component Interactions Keyboard navigation should work","status":"failed","title":"Keyboard navigation should work"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Animation and Transitions"],"duration":3,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Animation and Transitions CSS transitions should be supported","status":"passed","title":"CSS transitions should be supported"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Animation and Transitions"],"duration":3,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Animation and Transitions Transform animations should work","status":"passed","title":"Transform animations should work"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Animation and Transitions"],"duration":3,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Animation and Transitions Backdrop filter should be supported or gracefully degrade","status":"passed","title":"Backdrop filter should be supported or gracefully degrade"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Performance Across Browsers"],"duration":20,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Performance Across Browsers Page should load within acceptable time","status":"passed","title":"Page should load within acceptable time"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Performance Across Browsers"],"duration":2,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Performance Across Browsers Memory usage should be reasonable","status":"passed","title":"Memory usage should be reasonable"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Browser-Specific Features"],"duration":2,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Browser-Specific Features Should handle browser-specific CSS prefixes","status":"passed","title":"Should handle browser-specific CSS prefixes"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Browser-Specific Features"],"duration":3,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Browser-Specific Features Should work with different font rendering","status":"passed","title":"Should work with different font rendering"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Accessibility Across Browsers"],"duration":3,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Accessibility Across Browsers ARIA attributes should be supported","status":"passed","title":"ARIA attributes should be supported"},{"ancestorTitles":["Cross-Browser Compatibility Testing","Accessibility Across Browsers"],"duration":5,"failureMessages":[],"fullName":"Cross-Browser Compatibility Testing Accessibility Across Browsers Focus indicators should be visible","status":"passed","title":"Focus indicators should be visible"}]},{"numFailingTests":1,"numPassingTests":14,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750463180899,"loadTestEnvironmentEnd":1750463180191,"loadTestEnvironmentStart":1750463180177,"runtime":708,"setupAfterEnvEnd":1750463180295,"setupAfterEnvStart":1750463180221,"setupFilesEnd":1750463180191,"setupFilesStart":1750463180191,"slow":false,"start":1750463180191},"testFilePath":"F:\\DefeaterAI\\tests\\visual-regression.test.tsx","failureMessage":"\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mVisual Regression Testing › Interaction Behavior Comparison › Input handling should be consistent between pages\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31melement\u001b[39m\u001b[2m).toHaveValue(\u001b[22m\u001b[32mtest definition\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected the element to have value:\n    \u001b[32m  test definition\u001b[39m\n    Received:\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 373 |\u001b[39m       \u001b[36mif\u001b[39m (mainAppInput) {\u001b[22m\n\u001b[2m     \u001b[90m 374 |\u001b[39m         \u001b[36mawait\u001b[39m user\u001b[33m.\u001b[39mtype(mainAppInput\u001b[33m,\u001b[39m \u001b[32m'test definition'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 375 |\u001b[39m         expect(mainAppInput)\u001b[33m.\u001b[39mtoHaveValue(\u001b[32m'test definition'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 376 |\u001b[39m       }\u001b[22m\n\u001b[2m     \u001b[90m 377 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 378 |\u001b[39m       unmount()\u001b[33m;\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toHaveValue (\u001b[22m\u001b[2m\u001b[0m\u001b[36mtests/visual-regression.test.tsx\u001b[39m\u001b[0m\u001b[2m:375:30)\u001b[22m\u001b[2m\u001b[22m\n","testResults":[{"ancestorTitles":["Visual Regression Testing","Layout Structure Comparison"],"duration":11,"failureMessages":[],"fullName":"Visual Regression Testing Layout Structure Comparison Main app should use GameLayout like test page","status":"passed","title":"Main app should use GameLayout like test page"},{"ancestorTitles":["Visual Regression Testing","Layout Structure Comparison"],"duration":4,"failureMessages":[],"fullName":"Visual Regression Testing Layout Structure Comparison Test page should have identical layout structure","status":"passed","title":"Test page should have identical layout structure"},{"ancestorTitles":["Visual Regression Testing","Layout Structure Comparison"],"duration":6,"failureMessages":[],"fullName":"Visual Regression Testing Layout Structure Comparison Both pages should use GameFocus for content organization","status":"passed","title":"Both pages should use GameFocus for content organization"},{"ancestorTitles":["Visual Regression Testing","Component Presence Comparison"],"duration":9,"failureMessages":[],"fullName":"Visual Regression Testing Component Presence Comparison Both pages should have CurrentChallenge component","status":"passed","title":"Both pages should have CurrentChallenge component"},{"ancestorTitles":["Visual Regression Testing","Component Presence Comparison"],"duration":9,"failureMessages":[],"fullName":"Visual Regression Testing Component Presence Comparison Both pages should have DefinitionInput component","status":"passed","title":"Both pages should have DefinitionInput component"},{"ancestorTitles":["Visual Regression Testing","Component Presence Comparison"],"duration":8,"failureMessages":[],"fullName":"Visual Regression Testing Component Presence Comparison Both pages should have TargetRevelationStrip component","status":"passed","title":"Both pages should have TargetRevelationStrip component"},{"ancestorTitles":["Visual Regression Testing","Component Presence Comparison"],"duration":15,"failureMessages":[],"fullName":"Visual Regression Testing Component Presence Comparison Both pages should have CollapsibleSidePanel","status":"passed","title":"Both pages should have CollapsibleSidePanel"},{"ancestorTitles":["Visual Regression Testing","Typography Consistency"],"duration":6,"failureMessages":[],"fullName":"Visual Regression Testing Typography Consistency Both pages should use consistent typography components","status":"passed","title":"Both pages should use consistent typography components"},{"ancestorTitles":["Visual Regression Testing","Typography Consistency"],"duration":18,"failureMessages":[],"fullName":"Visual Regression Testing Typography Consistency Font weights should be consistent between pages","status":"passed","title":"Font weights should be consistent between pages"},{"ancestorTitles":["Visual Regression Testing","Responsive Behavior Comparison"],"duration":13,"failureMessages":[],"fullName":"Visual Regression Testing Responsive Behavior Comparison Both pages should handle mobile viewport correctly","status":"passed","title":"Both pages should handle mobile viewport correctly"},{"ancestorTitles":["Visual Regression Testing","Responsive Behavior Comparison"],"duration":76,"failureMessages":[],"fullName":"Visual Regression Testing Responsive Behavior Comparison Side panels should behave consistently on mobile","status":"passed","title":"Side panels should behave consistently on mobile"},{"ancestorTitles":["Visual Regression Testing","Interaction Behavior Comparison"],"duration":306,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31melement\u001b[39m\u001b[2m).toHaveValue(\u001b[22m\u001b[32mtest definition\u001b[39m\u001b[2m)\u001b[22m\n\nExpected the element to have value:\n\u001b[32m  test definition\u001b[39m\nReceived:\n\n    at Object.toHaveValue (F:\\DefeaterAI\\tests\\visual-regression.test.tsx:375:30)"],"fullName":"Visual Regression Testing Interaction Behavior Comparison Input handling should be consistent between pages","status":"failed","title":"Input handling should be consistent between pages"},{"ancestorTitles":["Visual Regression Testing","Interaction Behavior Comparison"],"duration":14,"failureMessages":[],"fullName":"Visual Regression Testing Interaction Behavior Comparison Button interactions should work consistently","status":"passed","title":"Button interactions should work consistently"},{"ancestorTitles":["Visual Regression Testing","Performance Comparison"],"duration":7,"failureMessages":[],"fullName":"Visual Regression Testing Performance Comparison Both pages should render within acceptable time","status":"passed","title":"Both pages should render within acceptable time"},{"ancestorTitles":["Visual Regression Testing","Performance Comparison"],"duration":20,"failureMessages":[],"fullName":"Visual Regression Testing Performance Comparison DOM complexity should be similar between pages","status":"passed","title":"DOM complexity should be similar between pages"}]},{"numFailingTests":0,"numPassingTests":8,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750463181815,"loadTestEnvironmentEnd":1750463180912,"loadTestEnvironmentStart":1750463180903,"runtime":903,"setupAfterEnvEnd":1750463181025,"setupAfterEnvStart":1750463180950,"setupFilesEnd":1750463180912,"setupFilesStart":1750463180912,"slow":false,"start":1750463180912},"testFilePath":"F:\\DefeaterAI\\tests\\component-interaction.test.tsx","failureMessage":null,"testResults":[{"ancestorTitles":["Component Interaction Testing","Z-Index Stacking Order"],"duration":21,"failureMessages":[],"fullName":"Component Interaction Testing Z-Index Stacking Order Modal should appear above side panel","status":"passed","title":"Modal should appear above side panel"},{"ancestorTitles":["Component Interaction Testing","Z-Index Stacking Order"],"duration":52,"failureMessages":[],"fullName":"Component Interaction Testing Z-Index Stacking Order Chat widget should not interfere with modals","status":"passed","title":"Chat widget should not interfere with modals"},{"ancestorTitles":["Component Interaction Testing","Focus Management"],"duration":113,"failureMessages":[],"fullName":"Component Interaction Testing Focus Management Modal should trap focus correctly","status":"passed","title":"Modal should trap focus correctly"},{"ancestorTitles":["Component Interaction Testing","Focus Management"],"duration":63,"failureMessages":[],"fullName":"Component Interaction Testing Focus Management Side panel should not interfere with modal focus","status":"passed","title":"Side panel should not interfere with modal focus"},{"ancestorTitles":["Component Interaction Testing","Keyboard Navigation"],"duration":120,"failureMessages":[],"fullName":"Component Interaction Testing Keyboard Navigation Escape key should close modal but not side panel","status":"passed","title":"Escape key should close modal but not side panel"},{"ancestorTitles":["Component Interaction Testing","Keyboard Navigation"],"duration":48,"failureMessages":[],"fullName":"Component Interaction Testing Keyboard Navigation Tab navigation should work correctly in side panel","status":"passed","title":"Tab navigation should work correctly in side panel"},{"ancestorTitles":["Component Interaction Testing","Overlay Interactions"],"duration":121,"failureMessages":[],"fullName":"Component Interaction Testing Overlay Interactions Multiple overlays should not conflict","status":"passed","title":"Multiple overlays should not conflict"},{"ancestorTitles":["Component Interaction Testing","Component State Isolation"],"duration":169,"failureMessages":[],"fullName":"Component Interaction Testing Component State Isolation Components should maintain independent state","status":"passed","title":"Components should maintain independent state"}]},{"numFailingTests":0,"numPassingTests":13,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750463182108,"loadTestEnvironmentEnd":1750463181824,"loadTestEnvironmentStart":1750463181818,"runtime":283,"setupAfterEnvEnd":1750463181929,"setupAfterEnvStart":1750463181857,"setupFilesEnd":1750463181825,"setupFilesStart":1750463181825,"slow":false,"start":1750463181825},"testFilePath":"F:\\DefeaterAI\\tests\\accessibility.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":24,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance GameBoard component has no accessibility violations","status":"passed","title":"GameBoard component has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":18,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance SkipLinks component has no accessibility violations","status":"passed","title":"SkipLinks component has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":29,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance CollapsibleSidePanel has no accessibility violations","status":"passed","title":"CollapsibleSidePanel has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":14,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance FloatingChatWidget has no accessibility violations","status":"passed","title":"FloatingChatWidget has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","Keyboard Navigation"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Tests Keyboard Navigation Skip links are accessible via keyboard","status":"passed","title":"Skip links are accessible via keyboard"},{"ancestorTitles":["Accessibility Compliance Tests","Keyboard Navigation"],"duration":7,"failureMessages":[],"fullName":"Accessibility Compliance Tests Keyboard Navigation Interactive elements are keyboard accessible","status":"passed","title":"Interactive elements are keyboard accessible"},{"ancestorTitles":["Accessibility Compliance Tests","Keyboard Navigation"],"duration":3,"failureMessages":[],"fullName":"Accessibility Compliance Tests Keyboard Navigation Focus management is properly implemented","status":"passed","title":"Focus management is properly implemented"},{"ancestorTitles":["Accessibility Compliance Tests","Screen Reader Support"],"duration":7,"failureMessages":[],"fullName":"Accessibility Compliance Tests Screen Reader Support ARIA labels are present and descriptive","status":"passed","title":"ARIA labels are present and descriptive"},{"ancestorTitles":["Accessibility Compliance Tests","Screen Reader Support"],"duration":3,"failureMessages":[],"fullName":"Accessibility Compliance Tests Screen Reader Support Live regions announce updates","status":"passed","title":"Live regions announce updates"},{"ancestorTitles":["Accessibility Compliance Tests","Color Contrast and Visual Accessibility"],"duration":1,"failureMessages":[],"fullName":"Accessibility Compliance Tests Color Contrast and Visual Accessibility Accessibility preferences are detected","status":"passed","title":"Accessibility preferences are detected"},{"ancestorTitles":["Accessibility Compliance Tests","Focus Management"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Tests Focus Management Focusable elements have proper focus indicators","status":"passed","title":"Focusable elements have proper focus indicators"},{"ancestorTitles":["Accessibility Compliance Tests","Mobile and Touch Accessibility"],"duration":3,"failureMessages":[],"fullName":"Accessibility Compliance Tests Mobile and Touch Accessibility Touch targets meet minimum size requirements","status":"passed","title":"Touch targets meet minimum size requirements"},{"ancestorTitles":["Accessibility Compliance Tests","Performance and Low-End Device Support"],"duration":2,"failureMessages":[],"fullName":"Accessibility Compliance Tests Performance and Low-End Device Support Accessibility features are performance optimized","status":"passed","title":"Accessibility features are performance optimized"}]},{"numFailingTests":0,"numPassingTests":16,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750463182364,"loadTestEnvironmentEnd":1750463182120,"loadTestEnvironmentStart":1750463182112,"runtime":243,"setupAfterEnvEnd":1750463182244,"setupAfterEnvStart":1750463182163,"setupFilesEnd":1750463182121,"setupFilesStart":1750463182121,"slow":false,"start":1750463182121},"testFilePath":"F:\\DefeaterAI\\tests\\performance-impact.test.tsx","failureMessage":null,"testResults":[{"ancestorTitles":["Performance Impact Assessment","Rendering Performance"],"duration":11,"failureMessages":[],"fullName":"Performance Impact Assessment Rendering Performance Main app should render within acceptable time","status":"passed","title":"Main app should render within acceptable time"},{"ancestorTitles":["Performance Impact Assessment","Rendering Performance"],"duration":4,"failureMessages":[],"fullName":"Performance Impact Assessment Rendering Performance Test page should render within acceptable time","status":"passed","title":"Test page should render within acceptable time"},{"ancestorTitles":["Performance Impact Assessment","Rendering Performance"],"duration":6,"failureMessages":[],"fullName":"Performance Impact Assessment Rendering Performance Component re-renders should be efficient","status":"passed","title":"Component re-renders should be efficient"},{"ancestorTitles":["Performance Impact Assessment","Memory Usage"],"duration":3,"failureMessages":[],"fullName":"Performance Impact Assessment Memory Usage Memory usage should be reasonable","status":"passed","title":"Memory usage should be reasonable"},{"ancestorTitles":["Performance Impact Assessment","Memory Usage"],"duration":3,"failureMessages":[],"fullName":"Performance Impact Assessment Memory Usage Memory should not leak on component unmount","status":"passed","title":"Memory should not leak on component unmount"},{"ancestorTitles":["Performance Impact Assessment","DOM Complexity"],"duration":5,"failureMessages":[],"fullName":"Performance Impact Assessment DOM Complexity DOM node count should be reasonable","status":"passed","title":"DOM node count should be reasonable"},{"ancestorTitles":["Performance Impact Assessment","DOM Complexity"],"duration":3,"failureMessages":[],"fullName":"Performance Impact Assessment DOM Complexity CSS class count should be optimized","status":"passed","title":"CSS class count should be optimized"},{"ancestorTitles":["Performance Impact Assessment","CSS Performance"],"duration":5,"failureMessages":[],"fullName":"Performance Impact Assessment CSS Performance CSS custom properties should be efficiently used","status":"passed","title":"CSS custom properties should be efficiently used"},{"ancestorTitles":["Performance Impact Assessment","CSS Performance"],"duration":3,"failureMessages":[],"fullName":"Performance Impact Assessment CSS Performance Animation performance should be optimized","status":"passed","title":"Animation performance should be optimized"},{"ancestorTitles":["Performance Impact Assessment","CSS Performance"],"duration":4,"failureMessages":[],"fullName":"Performance Impact Assessment CSS Performance CSS selector complexity should be reasonable","status":"passed","title":"CSS selector complexity should be reasonable"},{"ancestorTitles":["Performance Impact Assessment","Bundle Size Impact"],"duration":4,"failureMessages":[],"fullName":"Performance Impact Assessment Bundle Size Impact Component imports should be tree-shakeable","status":"passed","title":"Component imports should be tree-shakeable"},{"ancestorTitles":["Performance Impact Assessment","Bundle Size Impact"],"duration":3,"failureMessages":[],"fullName":"Performance Impact Assessment Bundle Size Impact CSS-in-JS should not cause excessive style tags","status":"passed","title":"CSS-in-JS should not cause excessive style tags"},{"ancestorTitles":["Performance Impact Assessment","Responsive Performance"],"duration":4,"failureMessages":[],"fullName":"Performance Impact Assessment Responsive Performance Viewport changes should not cause layout thrashing","status":"passed","title":"Viewport changes should not cause layout thrashing"},{"ancestorTitles":["Performance Impact Assessment","Responsive Performance"],"duration":4,"failureMessages":[],"fullName":"Performance Impact Assessment Responsive Performance Media queries should be efficiently structured","status":"passed","title":"Media queries should be efficiently structured"},{"ancestorTitles":["Performance Impact Assessment","Accessibility Performance"],"duration":4,"failureMessages":[],"fullName":"Performance Impact Assessment Accessibility Performance ARIA attributes should not impact performance","status":"passed","title":"ARIA attributes should not impact performance"},{"ancestorTitles":["Performance Impact Assessment","Accessibility Performance"],"duration":3,"failureMessages":[],"fullName":"Performance Impact Assessment Accessibility Performance Focus management should be performant","status":"passed","title":"Focus management should be performant"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["components/**/*.{js,jsx,ts,tsx}","pages/**/*.{js,jsx,ts,tsx}","utils/**/*.{js,jsx,ts,tsx}","hooks/**/*.{js,jsx,ts,tsx}","contexts/**/*.{js,jsx,ts,tsx}","!**/*.d.ts","!**/node_modules/**","!**/.next/**","!**/coverage/**"],"coverageDirectory":"F:\\DefeaterAI\\coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"coverageThreshold":{"global":{"branches":70,"functions":70,"lines":70,"statements":70}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":1,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":true,"projects":[],"reporters":[["default",{}],["F:\\DefeaterAI\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"hideIcon":false,"pageTitle":"DEFEATER.AI Accessibility Test Report"}]],"rootDir":"F:\\DefeaterAI","runInBand":false,"runTestsByPath":false,"seed":-61205532,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":[],"type":"TestPathPatterns"},"testSequencer":"F:\\DefeaterAI\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1750463182384,"_reporterOptions":{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"pageTitle":"DEFEATER.AI Accessibility Test Report","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})
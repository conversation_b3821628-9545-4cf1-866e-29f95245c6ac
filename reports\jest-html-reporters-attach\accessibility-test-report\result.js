window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":2,"numPassedTests":21,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":2,"numTotalTests":21,"startTime":1750443507674,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":13,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750443509061,"loadTestEnvironmentEnd":1750443508275,"loadTestEnvironmentStart":1750443507810,"runtime":785,"setupAfterEnvEnd":1750443508699,"setupAfterEnvStart":1750443508516,"setupFilesEnd":1750443508276,"setupFilesStart":1750443508276,"slow":false,"start":1750443508276},"testFilePath":"F:\\DefeaterAI\\tests\\accessibility.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":84,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance GameBoard component has no accessibility violations","status":"passed","title":"GameBoard component has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":39,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance SkipLinks component has no accessibility violations","status":"passed","title":"SkipLinks component has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":29,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance CollapsibleSidePanel has no accessibility violations","status":"passed","title":"CollapsibleSidePanel has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":32,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance FloatingChatWidget has no accessibility violations","status":"passed","title":"FloatingChatWidget has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","Keyboard Navigation"],"duration":5,"failureMessages":[],"fullName":"Accessibility Compliance Tests Keyboard Navigation Skip links are accessible via keyboard","status":"passed","title":"Skip links are accessible via keyboard"},{"ancestorTitles":["Accessibility Compliance Tests","Keyboard Navigation"],"duration":14,"failureMessages":[],"fullName":"Accessibility Compliance Tests Keyboard Navigation Interactive elements are keyboard accessible","status":"passed","title":"Interactive elements are keyboard accessible"},{"ancestorTitles":["Accessibility Compliance Tests","Keyboard Navigation"],"duration":9,"failureMessages":[],"fullName":"Accessibility Compliance Tests Keyboard Navigation Focus management is properly implemented","status":"passed","title":"Focus management is properly implemented"},{"ancestorTitles":["Accessibility Compliance Tests","Screen Reader Support"],"duration":8,"failureMessages":[],"fullName":"Accessibility Compliance Tests Screen Reader Support ARIA labels are present and descriptive","status":"passed","title":"ARIA labels are present and descriptive"},{"ancestorTitles":["Accessibility Compliance Tests","Screen Reader Support"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Tests Screen Reader Support Live regions announce updates","status":"passed","title":"Live regions announce updates"},{"ancestorTitles":["Accessibility Compliance Tests","Color Contrast and Visual Accessibility"],"duration":2,"failureMessages":[],"fullName":"Accessibility Compliance Tests Color Contrast and Visual Accessibility Accessibility preferences are detected","status":"passed","title":"Accessibility preferences are detected"},{"ancestorTitles":["Accessibility Compliance Tests","Focus Management"],"duration":8,"failureMessages":[],"fullName":"Accessibility Compliance Tests Focus Management Focusable elements have proper focus indicators","status":"passed","title":"Focusable elements have proper focus indicators"},{"ancestorTitles":["Accessibility Compliance Tests","Mobile and Touch Accessibility"],"duration":3,"failureMessages":[],"fullName":"Accessibility Compliance Tests Mobile and Touch Accessibility Touch targets meet minimum size requirements","status":"passed","title":"Touch targets meet minimum size requirements"},{"ancestorTitles":["Accessibility Compliance Tests","Performance and Low-End Device Support"],"duration":3,"failureMessages":[],"fullName":"Accessibility Compliance Tests Performance and Low-End Device Support Accessibility features are performance optimized","status":"passed","title":"Accessibility features are performance optimized"}]},{"numFailingTests":0,"numPassingTests":8,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750443509668,"loadTestEnvironmentEnd":1750443508274,"loadTestEnvironmentStart":1750443507807,"runtime":1392,"setupAfterEnvEnd":1750443508700,"setupAfterEnvStart":1750443508515,"setupFilesEnd":1750443508276,"setupFilesStart":1750443508276,"slow":false,"start":1750443508276},"testFilePath":"F:\\DefeaterAI\\tests\\component-interaction.test.tsx","failureMessage":null,"testResults":[{"ancestorTitles":["Component Interaction Testing","Z-Index Stacking Order"],"duration":79,"failureMessages":[],"fullName":"Component Interaction Testing Z-Index Stacking Order Modal should appear above side panel","status":"passed","title":"Modal should appear above side panel"},{"ancestorTitles":["Component Interaction Testing","Z-Index Stacking Order"],"duration":70,"failureMessages":[],"fullName":"Component Interaction Testing Z-Index Stacking Order Chat widget should not interfere with modals","status":"passed","title":"Chat widget should not interfere with modals"},{"ancestorTitles":["Component Interaction Testing","Focus Management"],"duration":92,"failureMessages":[],"fullName":"Component Interaction Testing Focus Management Modal should trap focus correctly","status":"passed","title":"Modal should trap focus correctly"},{"ancestorTitles":["Component Interaction Testing","Focus Management"],"duration":62,"failureMessages":[],"fullName":"Component Interaction Testing Focus Management Side panel should not interfere with modal focus","status":"passed","title":"Side panel should not interfere with modal focus"},{"ancestorTitles":["Component Interaction Testing","Keyboard Navigation"],"duration":124,"failureMessages":[],"fullName":"Component Interaction Testing Keyboard Navigation Escape key should close modal but not side panel","status":"passed","title":"Escape key should close modal but not side panel"},{"ancestorTitles":["Component Interaction Testing","Keyboard Navigation"],"duration":62,"failureMessages":[],"fullName":"Component Interaction Testing Keyboard Navigation Tab navigation should work correctly in side panel","status":"passed","title":"Tab navigation should work correctly in side panel"},{"ancestorTitles":["Component Interaction Testing","Overlay Interactions"],"duration":126,"failureMessages":[],"fullName":"Component Interaction Testing Overlay Interactions Multiple overlays should not conflict","status":"passed","title":"Multiple overlays should not conflict"},{"ancestorTitles":["Component Interaction Testing","Component State Isolation"],"duration":168,"failureMessages":[],"fullName":"Component Interaction Testing Component State Isolation Components should maintain independent state","status":"passed","title":"Components should maintain independent state"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["components/**/*.{js,jsx,ts,tsx}","pages/**/*.{js,jsx,ts,tsx}","utils/**/*.{js,jsx,ts,tsx}","hooks/**/*.{js,jsx,ts,tsx}","contexts/**/*.{js,jsx,ts,tsx}","!**/*.d.ts","!**/node_modules/**","!**/.next/**","!**/coverage/**"],"coverageDirectory":"F:\\DefeaterAI\\coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"coverageThreshold":{"global":{"branches":70,"functions":70,"lines":70,"statements":70}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":15,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\DefeaterAI\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"hideIcon":false,"pageTitle":"DEFEATER.AI Accessibility Test Report"}]],"rootDir":"F:\\DefeaterAI","runInBand":false,"runTestsByPath":false,"seed":**********,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["accessibility.test.ts|component-interaction.test.tsx"],"type":"TestPathPatterns"},"testSequencer":"F:\\DefeaterAI\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1750443509692,"_reporterOptions":{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"pageTitle":"DEFEATER.AI Accessibility Test Report","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})
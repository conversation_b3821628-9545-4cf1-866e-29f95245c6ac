window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":0,"numPassedTestSuites":6,"numPassedTests":79,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":1,"numTodoTests":0,"numTotalTestSuites":7,"numTotalTests":79,"startTime":1750513073895,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750513076130,"loadTestEnvironmentEnd":1750513075207,"loadTestEnvironmentStart":1750513074075,"runtime":920,"setupAfterEnvEnd":1750513076100,"setupAfterEnvStart":1750513075671,"setupFilesEnd":1750513075210,"setupFilesStart":1750513075210,"slow":false,"start":1750513075210},"testFilePath":"F:\\DefeaterAI\\utils\\__tests__\\memoryManagement.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["MemoryManager"],"duration":7,"failureMessages":[],"fullName":"MemoryManager tracks and cleans up timer resources","status":"passed","title":"tracks and cleans up timer resources"},{"ancestorTitles":["MemoryManager"],"duration":2,"failureMessages":[],"fullName":"MemoryManager tracks and cleans up event listener resources","status":"passed","title":"tracks and cleans up event listener resources"},{"ancestorTitles":["MemoryManager"],"duration":1,"failureMessages":[],"fullName":"MemoryManager cleans up all resources for a component","status":"passed","title":"cleans up all resources for a component"},{"ancestorTitles":["MemoryManager"],"duration":1,"failureMessages":[],"fullName":"MemoryManager cleans up all resources","status":"passed","title":"cleans up all resources"},{"ancestorTitles":["MemoryManager"],"duration":1,"failureMessages":[],"fullName":"MemoryManager handles cleanup errors gracefully","status":"passed","title":"handles cleanup errors gracefully"},{"ancestorTitles":["Managed Resource Functions"],"duration":3,"failureMessages":[],"fullName":"Managed Resource Functions managedSetTimeout creates tracked timer","status":"passed","title":"managedSetTimeout creates tracked timer"},{"ancestorTitles":["Managed Resource Functions"],"duration":3,"failureMessages":[],"fullName":"Managed Resource Functions managedSetInterval creates tracked interval","status":"passed","title":"managedSetInterval creates tracked interval"},{"ancestorTitles":["Managed Resource Functions"],"duration":3,"failureMessages":[],"fullName":"Managed Resource Functions managedAddEventListener creates tracked listener","status":"passed","title":"managedAddEventListener creates tracked listener"},{"ancestorTitles":["useMemoryCleanup Hook"],"duration":1,"failureMessages":[],"fullName":"useMemoryCleanup Hook provides component-scoped resource management","status":"passed","title":"provides component-scoped resource management"},{"ancestorTitles":["Memory Metrics"],"duration":1,"failureMessages":[],"fullName":"Memory Metrics tracks resource creation and cleanup metrics","status":"passed","title":"tracks resource creation and cleanup metrics"}]},{"numFailingTests":0,"numPassingTests":12,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750513076523,"loadTestEnvironmentEnd":1750513075190,"loadTestEnvironmentStart":1750513074075,"runtime":1331,"setupAfterEnvEnd":1750513076060,"setupAfterEnvStart":1750513075649,"setupFilesEnd":1750513075192,"setupFilesStart":1750513075192,"slow":false,"start":1750513075192},"testFilePath":"F:\\DefeaterAI\\components\\__tests__\\ErrorBoundary.test.tsx","failureMessage":null,"testResults":[{"ancestorTitles":["ErrorBoundary"],"duration":23,"failureMessages":[],"fullName":"ErrorBoundary renders children when there is no error","status":"passed","title":"renders children when there is no error"},{"ancestorTitles":["ErrorBoundary"],"duration":54,"failureMessages":[],"fullName":"ErrorBoundary catches and displays error with auto-retry UI for component level","status":"passed","title":"catches and displays error with auto-retry UI for component level"},{"ancestorTitles":["ErrorBoundary"],"duration":12,"failureMessages":[],"fullName":"ErrorBoundary displays different UI based on error level","status":"passed","title":"displays different UI based on error level"},{"ancestorTitles":["ErrorBoundary"],"duration":11,"failureMessages":[],"fullName":"ErrorBoundary calls custom error handler when provided","status":"passed","title":"calls custom error handler when provided"},{"ancestorTitles":["ErrorBoundary"],"duration":19,"failureMessages":[],"fullName":"ErrorBoundary shows section-level error UI with Try Again button","status":"passed","title":"shows section-level error UI with Try Again button"},{"ancestorTitles":["ErrorBoundary"],"duration":8,"failureMessages":[],"fullName":"ErrorBoundary renders custom fallback when provided","status":"passed","title":"renders custom fallback when provided"},{"ancestorTitles":["GameErrorBoundary"],"duration":9,"failureMessages":[],"fullName":"GameErrorBoundary renders game-specific error UI","status":"passed","title":"renders game-specific error UI"},{"ancestorTitles":["GameErrorBoundary"],"duration":7,"failureMessages":[],"fullName":"GameErrorBoundary calls game error handler when provided","status":"passed","title":"calls game error handler when provided"},{"ancestorTitles":["AsyncErrorBoundary"],"duration":13,"failureMessages":[],"fullName":"AsyncErrorBoundary renders async-specific error UI","status":"passed","title":"renders async-specific error UI"},{"ancestorTitles":["AsyncErrorBoundary"],"duration":9,"failureMessages":[],"fullName":"AsyncErrorBoundary calls async error handler when provided","status":"passed","title":"calls async error handler when provided"},{"ancestorTitles":["Error Boundary Integration"],"duration":10,"failureMessages":[],"fullName":"Error Boundary Integration isolates errors when isolate prop is true","status":"passed","title":"isolates errors when isolate prop is true"},{"ancestorTitles":["Error Boundary Integration"],"duration":13,"failureMessages":[],"fullName":"Error Boundary Integration resets on prop changes when resetOnPropsChange is true","status":"passed","title":"resets on prop changes when resetOnPropsChange is true"}]},{"numFailingTests":0,"numPassingTests":13,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750513076536,"loadTestEnvironmentEnd":1750513075205,"loadTestEnvironmentStart":1750513074075,"runtime":1329,"setupAfterEnvEnd":1750513076093,"setupAfterEnvStart":1750513075652,"setupFilesEnd":1750513075207,"setupFilesStart":1750513075207,"slow":false,"start":1750513075207},"testFilePath":"F:\\DefeaterAI\\tests\\accessibility.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":69,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance GameBoard component has no accessibility violations","status":"passed","title":"GameBoard component has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":26,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance SkipLinks component has no accessibility violations","status":"passed","title":"SkipLinks component has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":22,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance CollapsibleSidePanel has no accessibility violations","status":"passed","title":"CollapsibleSidePanel has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","WCAG 2.1 AA Compliance"],"duration":30,"failureMessages":[],"fullName":"Accessibility Compliance Tests WCAG 2.1 AA Compliance FloatingChatWidget has no accessibility violations","status":"passed","title":"FloatingChatWidget has no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Tests","Keyboard Navigation"],"duration":7,"failureMessages":[],"fullName":"Accessibility Compliance Tests Keyboard Navigation Skip links are accessible via keyboard","status":"passed","title":"Skip links are accessible via keyboard"},{"ancestorTitles":["Accessibility Compliance Tests","Keyboard Navigation"],"duration":18,"failureMessages":[],"fullName":"Accessibility Compliance Tests Keyboard Navigation Interactive elements are keyboard accessible","status":"passed","title":"Interactive elements are keyboard accessible"},{"ancestorTitles":["Accessibility Compliance Tests","Keyboard Navigation"],"duration":5,"failureMessages":[],"fullName":"Accessibility Compliance Tests Keyboard Navigation Focus management is properly implemented","status":"passed","title":"Focus management is properly implemented"},{"ancestorTitles":["Accessibility Compliance Tests","Screen Reader Support"],"duration":6,"failureMessages":[],"fullName":"Accessibility Compliance Tests Screen Reader Support ARIA labels are present and descriptive","status":"passed","title":"ARIA labels are present and descriptive"},{"ancestorTitles":["Accessibility Compliance Tests","Screen Reader Support"],"duration":5,"failureMessages":[],"fullName":"Accessibility Compliance Tests Screen Reader Support Live regions announce updates","status":"passed","title":"Live regions announce updates"},{"ancestorTitles":["Accessibility Compliance Tests","Color Contrast and Visual Accessibility"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Tests Color Contrast and Visual Accessibility Accessibility preferences are detected","status":"passed","title":"Accessibility preferences are detected"},{"ancestorTitles":["Accessibility Compliance Tests","Focus Management"],"duration":6,"failureMessages":[],"fullName":"Accessibility Compliance Tests Focus Management Focusable elements have proper focus indicators","status":"passed","title":"Focusable elements have proper focus indicators"},{"ancestorTitles":["Accessibility Compliance Tests","Mobile and Touch Accessibility"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Tests Mobile and Touch Accessibility Touch targets meet minimum size requirements","status":"passed","title":"Touch targets meet minimum size requirements"},{"ancestorTitles":["Accessibility Compliance Tests","Performance and Low-End Device Support"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Tests Performance and Low-End Device Support Accessibility features are performance optimized","status":"passed","title":"Accessibility features are performance optimized"}]},{"numFailingTests":0,"numPassingTests":0,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":0,"loadTestEnvironmentEnd":0,"loadTestEnvironmentStart":0,"runtime":0,"setupAfterEnvEnd":0,"setupAfterEnvStart":0,"setupFilesEnd":0,"setupFilesStart":0,"slow":false,"start":0},"testFilePath":"F:\\DefeaterAI\\components\\__tests__\\GameBoard.test.tsx","failureMessage":"  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module './networkOptimization' from 'utils/gameApi.ts'\n\n    Require stack:\n      utils/gameApi.ts\n      hooks/useGameLogic.ts\n      hooks/useGameBoardState.ts\n      components/GameBoard.tsx\n      components/__tests__/GameBoard.test.tsx\n\n    \u001b[0m \u001b[90m 52 |\u001b[39m       gameState\u001b[33m:\u001b[39m \u001b[36mnull\u001b[39m\u001b[33m,\u001b[39m\n     \u001b[90m 53 |\u001b[39m       action\u001b[33m:\u001b[39m \u001b[32m'start'\u001b[39m\u001b[33m,\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 54 |\u001b[39m       difficulty\n     \u001b[90m    |\u001b[39m                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 55 |\u001b[39m     }\u001b[33m;\u001b[39m\n     \u001b[90m 56 |\u001b[39m\n     \u001b[90m 57 |\u001b[39m     \u001b[36mconst\u001b[39m response \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m performanceMonitor\u001b[33m.\u001b[39mmeasureApiCall(\u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/index.js\u001b[2m:868:11)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mutils/gameApi.ts\u001b[2m:54:30)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mhooks/useGameLogic.ts\u001b[2m:34:58)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mhooks/useGameBoardState.ts\u001b[2m:38:62)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mcomponents/GameBoard.tsx\u001b[2m:24:67)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22m\u001b[0m\u001b[36mcomponents/__tests__/GameBoard.test.tsx\u001b[39m\u001b[0m\u001b[2m:174:59)\u001b[22m\n","testResults":[]},{"numFailingTests":0,"numPassingTests":8,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750513077123,"loadTestEnvironmentEnd":1750513075214,"loadTestEnvironmentStart":1750513074078,"runtime":1906,"setupAfterEnvEnd":1750513076092,"setupAfterEnvStart":1750513075664,"setupFilesEnd":1750513075217,"setupFilesStart":1750513075217,"slow":false,"start":1750513075217},"testFilePath":"F:\\DefeaterAI\\tests\\component-interaction.test.tsx","failureMessage":null,"testResults":[{"ancestorTitles":["Component Interaction Testing","Z-Index Stacking Order"],"duration":92,"failureMessages":[],"fullName":"Component Interaction Testing Z-Index Stacking Order Modal should appear above side panel","status":"passed","title":"Modal should appear above side panel"},{"ancestorTitles":["Component Interaction Testing","Z-Index Stacking Order"],"duration":103,"failureMessages":[],"fullName":"Component Interaction Testing Z-Index Stacking Order Chat widget should not interfere with modals","status":"passed","title":"Chat widget should not interfere with modals"},{"ancestorTitles":["Component Interaction Testing","Focus Management"],"duration":81,"failureMessages":[],"fullName":"Component Interaction Testing Focus Management Modal should trap focus correctly","status":"passed","title":"Modal should trap focus correctly"},{"ancestorTitles":["Component Interaction Testing","Focus Management"],"duration":54,"failureMessages":[],"fullName":"Component Interaction Testing Focus Management Side panel should not interfere with modal focus","status":"passed","title":"Side panel should not interfere with modal focus"},{"ancestorTitles":["Component Interaction Testing","Keyboard Navigation"],"duration":76,"failureMessages":[],"fullName":"Component Interaction Testing Keyboard Navigation Escape key should close modal but not side panel","status":"passed","title":"Escape key should close modal but not side panel"},{"ancestorTitles":["Component Interaction Testing","Keyboard Navigation"],"duration":34,"failureMessages":[],"fullName":"Component Interaction Testing Keyboard Navigation Tab navigation should work correctly in side panel","status":"passed","title":"Tab navigation should work correctly in side panel"},{"ancestorTitles":["Component Interaction Testing","Overlay Interactions"],"duration":105,"failureMessages":[],"fullName":"Component Interaction Testing Overlay Interactions Multiple overlays should not conflict","status":"passed","title":"Multiple overlays should not conflict"},{"ancestorTitles":["Component Interaction Testing","Component State Isolation"],"duration":125,"failureMessages":[],"fullName":"Component Interaction Testing Component State Isolation Components should maintain independent state","status":"passed","title":"Components should maintain independent state"}]},{"numFailingTests":0,"numPassingTests":20,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750513077426,"loadTestEnvironmentEnd":1750513075205,"loadTestEnvironmentStart":1750513074075,"runtime":2219,"setupAfterEnvEnd":1750513076099,"setupAfterEnvStart":1750513075671,"setupFilesEnd":1750513075207,"setupFilesStart":1750513075207,"slow":false,"start":1750513075207},"testFilePath":"F:\\DefeaterAI\\tests\\accessibility-compliance.test.tsx","failureMessage":null,"testResults":[{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":105,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Main application should have no accessibility violations","status":"passed","title":"Main application should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":72,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Side panel should have no accessibility violations","status":"passed","title":"Side panel should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":80,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Modal should have no accessibility violations","status":"passed","title":"Modal should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","WCAG 2.1 Level AA Compliance"],"duration":31,"failureMessages":[],"fullName":"Accessibility Compliance Testing WCAG 2.1 Level AA Compliance Tab navigation should have no accessibility violations","status":"passed","title":"Tab navigation should have no accessibility violations"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":31,"failureMessages":[],"fullName":"Accessibility Compliance Testing Keyboard Navigation All interactive elements should be keyboard accessible","status":"passed","title":"All interactive elements should be keyboard accessible"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":296,"failureMessages":[],"fullName":"Accessibility Compliance Testing Keyboard Navigation Tab navigation should follow logical order","status":"passed","title":"Tab navigation should follow logical order"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":86,"failureMessages":[],"fullName":"Accessibility Compliance Testing Keyboard Navigation Escape key should close modals and overlays","status":"passed","title":"Escape key should close modals and overlays"},{"ancestorTitles":["Accessibility Compliance Testing","Keyboard Navigation"],"duration":48,"failureMessages":[],"fullName":"Accessibility Compliance Testing Keyboard Navigation Arrow keys should navigate within tab groups","status":"passed","title":"Arrow keys should navigate within tab groups"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support All images should have alt text or be marked decorative","status":"passed","title":"All images should have alt text or be marked decorative"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":3,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support Form inputs should have proper labels","status":"passed","title":"Form inputs should have proper labels"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support Interactive elements should have accessible names","status":"passed","title":"Interactive elements should have accessible names"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support Headings should follow proper hierarchy","status":"passed","title":"Headings should follow proper hierarchy"},{"ancestorTitles":["Accessibility Compliance Testing","Screen Reader Support"],"duration":3,"failureMessages":[],"fullName":"Accessibility Compliance Testing Screen Reader Support ARIA landmarks should be present","status":"passed","title":"ARIA landmarks should be present"},{"ancestorTitles":["Accessibility Compliance Testing","Focus Management"],"duration":99,"failureMessages":[],"fullName":"Accessibility Compliance Testing Focus Management Focus should be trapped within modals","status":"passed","title":"Focus should be trapped within modals"},{"ancestorTitles":["Accessibility Compliance Testing","Focus Management"],"duration":80,"failureMessages":[],"fullName":"Accessibility Compliance Testing Focus Management Focus should return to trigger element after modal closes","status":"passed","title":"Focus should return to trigger element after modal closes"},{"ancestorTitles":["Accessibility Compliance Testing","Focus Management"],"duration":36,"failureMessages":[],"fullName":"Accessibility Compliance Testing Focus Management Skip links should be available for keyboard users","status":"passed","title":"Skip links should be available for keyboard users"},{"ancestorTitles":["Accessibility Compliance Testing","Color and Contrast"],"duration":5,"failureMessages":[],"fullName":"Accessibility Compliance Testing Color and Contrast Text should have sufficient color contrast","status":"passed","title":"Text should have sufficient color contrast"},{"ancestorTitles":["Accessibility Compliance Testing","Color and Contrast"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Color and Contrast Interactive elements should have visible focus indicators","status":"passed","title":"Interactive elements should have visible focus indicators"},{"ancestorTitles":["Accessibility Compliance Testing","Touch and Mobile Accessibility"],"duration":4,"failureMessages":[],"fullName":"Accessibility Compliance Testing Touch and Mobile Accessibility Touch targets should meet minimum size requirements","status":"passed","title":"Touch targets should meet minimum size requirements"},{"ancestorTitles":["Accessibility Compliance Testing","Touch and Mobile Accessibility"],"duration":3,"failureMessages":[],"fullName":"Accessibility Compliance Testing Touch and Mobile Accessibility Content should be readable without horizontal scrolling","status":"passed","title":"Content should be readable without horizontal scrolling"}]},{"numFailingTests":0,"numPassingTests":16,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750513083332,"loadTestEnvironmentEnd":1750513075193,"loadTestEnvironmentStart":1750513074072,"runtime":8137,"setupAfterEnvEnd":1750513076067,"setupAfterEnvStart":1750513075649,"setupFilesEnd":1750513075195,"setupFilesStart":1750513075195,"slow":true,"start":1750513075195},"testFilePath":"F:\\DefeaterAI\\utils\\__tests__\\apiClient.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["Enhanced API Client","Basic Request Functionality"],"duration":6,"failureMessages":[],"fullName":"Enhanced API Client Basic Request Functionality makes successful GET request","status":"passed","title":"makes successful GET request"},{"ancestorTitles":["Enhanced API Client","Basic Request Functionality"],"duration":2,"failureMessages":[],"fullName":"Enhanced API Client Basic Request Functionality makes successful POST request with body","status":"passed","title":"makes successful POST request with body"},{"ancestorTitles":["Enhanced API Client","Caching Functionality"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Caching Functionality caches GET requests when enabled","status":"passed","title":"caches GET requests when enabled"},{"ancestorTitles":["Enhanced API Client","Caching Functionality"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Caching Functionality does not cache POST requests","status":"passed","title":"does not cache POST requests"},{"ancestorTitles":["Enhanced API Client","Caching Functionality"],"duration":17,"failureMessages":[],"fullName":"Enhanced API Client Caching Functionality respects cache TTL","status":"passed","title":"respects cache TTL"},{"ancestorTitles":["Enhanced API Client","Retry Logic"],"duration":21,"failureMessages":[],"fullName":"Enhanced API Client Retry Logic retries failed requests","status":"passed","title":"retries failed requests"},{"ancestorTitles":["Enhanced API Client","Retry Logic"],"duration":12,"failureMessages":[],"fullName":"Enhanced API Client Retry Logic does not retry non-retryable errors","status":"passed","title":"does not retry non-retryable errors"},{"ancestorTitles":["Enhanced API Client","Retry Logic"],"duration":16,"failureMessages":[],"fullName":"Enhanced API Client Retry Logic gives up after max retries","status":"passed","title":"gives up after max retries"},{"ancestorTitles":["Enhanced API Client","Request Deduplication"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Request Deduplication deduplicates identical concurrent requests","status":"passed","title":"deduplicates identical concurrent requests"},{"ancestorTitles":["Enhanced API Client","Request Deduplication"],"duration":0,"failureMessages":[],"fullName":"Enhanced API Client Request Deduplication does not deduplicate when disabled","status":"passed","title":"does not deduplicate when disabled"},{"ancestorTitles":["Enhanced API Client","Error Handling"],"duration":5,"failureMessages":[],"fullName":"Enhanced API Client Error Handling handles HTTP errors correctly","status":"passed","title":"handles HTTP errors correctly"},{"ancestorTitles":["Enhanced API Client","Error Handling"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Error Handling handles network errors correctly","status":"passed","title":"handles network errors correctly"},{"ancestorTitles":["Enhanced API Client","Error Handling"],"duration":7121,"failureMessages":[],"fullName":"Enhanced API Client Error Handling handles timeout errors correctly","status":"passed","title":"handles timeout errors correctly"},{"ancestorTitles":["Enhanced API Client","Metrics Tracking"],"duration":20,"failureMessages":[],"fullName":"Enhanced API Client Metrics Tracking tracks request metrics correctly","status":"passed","title":"tracks request metrics correctly"},{"ancestorTitles":["Enhanced API Client","Metrics Tracking"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Metrics Tracking tracks failed request metrics","status":"passed","title":"tracks failed request metrics"},{"ancestorTitles":["Enhanced API Client","Cache Management"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Cache Management clears cache correctly","status":"passed","title":"clears cache correctly"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["components/**/*.{js,jsx,ts,tsx}","pages/**/*.{js,jsx,ts,tsx}","utils/**/*.{js,jsx,ts,tsx}","hooks/**/*.{js,jsx,ts,tsx}","contexts/**/*.{js,jsx,ts,tsx}","!**/*.d.ts","!**/node_modules/**","!**/.next/**","!**/coverage/**"],"coverageDirectory":"F:\\DefeaterAI\\coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"coverageThreshold":{"global":{"branches":70,"functions":70,"lines":70,"statements":70}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":15,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\DefeaterAI\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"hideIcon":false,"pageTitle":"DEFEATER.AI Accessibility Test Report"}]],"rootDir":"F:\\DefeaterAI","runInBand":false,"runTestsByPath":false,"seed":**********,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["(GameBoard|accessibility|component-interaction|apiClient|memoryManagement|ErrorBoundary)"],"type":"TestPathPatterns"},"testSequencer":"F:\\DefeaterAI\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1750513083383,"_reporterOptions":{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"pageTitle":"DEFEATER.AI Accessibility Test Report","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})
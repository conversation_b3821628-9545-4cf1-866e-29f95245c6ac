window.jest_html_reporters_callback__({"numFailedTestSuites":1,"numFailedTests":1,"numPassedTestSuites":0,"numPassedTests":15,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":16,"startTime":1750503867143,"success":false,"testResults":[{"numFailingTests":1,"numPassingTests":15,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750503882178,"loadTestEnvironmentEnd":1750503867578,"loadTestEnvironmentStart":1750503867156,"runtime":14599,"setupAfterEnvEnd":1750503867908,"setupAfterEnvStart":1750503867727,"setupFilesEnd":1750503867579,"setupFilesStart":1750503867579,"slow":true,"start":1750503867579},"testFilePath":"F:\\DefeaterAI\\utils\\__tests__\\apiClient.test.ts","failureMessage":"\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mEnhanced API Client › Error Handling › handles HTTP errors correctly\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected substring: \u001b[32m\"HTTP 500: Internal Server Error\"\u001b[39m\n    Received message:   \u001b[31m\"Cannot read properties of undefined (reading 'ok')\"\u001b[39m\n\n        \u001b[0m \u001b[90m 195 |\u001b[39m         clearTimeout(timeoutId)\u001b[33m;\u001b[39m\n         \u001b[90m 196 |\u001b[39m\n        \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 197 |\u001b[39m         \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mresponse\u001b[33m.\u001b[39mok) {\n         \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n         \u001b[90m 198 |\u001b[39m           \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`HTTP ${response.status}: ${response.statusText}`\u001b[39m)\u001b[33m;\u001b[39m\n         \u001b[90m 199 |\u001b[39m         }\n         \u001b[90m 200 |\u001b[39m\u001b[0m\n\n          \u001b[2mat EnhancedApiClient.ok [as executeRequest] (\u001b[22mutils/apiClient.ts\u001b[2m:197:23)\u001b[22m\n          \u001b[2mat EnhancedApiClient.request (\u001b[22mutils/apiClient.ts\u001b[2m:129:22)\u001b[22m\n          \u001b[2mat Object.<anonymous> (\u001b[22mutils/__tests__/apiClient.test.ts\u001b[2m:255:7)\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 255 |\u001b[39m       \u001b[36mawait\u001b[39m expect(\u001b[22m\n\u001b[2m     \u001b[90m 256 |\u001b[39m         apiClient\u001b[33m.\u001b[39mrequest(\u001b[32m'/test-error'\u001b[39m)\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 257 |\u001b[39m       )\u001b[33m.\u001b[39mrejects\u001b[33m.\u001b[39mtoThrow(\u001b[32m'HTTP 500: Internal Server Error'\u001b[39m)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                 \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 258 |\u001b[39m     })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 259 |\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 260 |\u001b[39m     it(\u001b[32m'handles network errors correctly'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m () \u001b[33m=>\u001b[39m {\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toThrow (\u001b[22m\u001b[2mnode_modules/expect/build/index.js\u001b[2m:2151:20)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.toThrow (\u001b[22m\u001b[2m\u001b[0m\u001b[36mutils/__tests__/apiClient.test.ts\u001b[39m\u001b[0m\u001b[2m:257:17)\u001b[22m\u001b[2m\u001b[22m\n","testResults":[{"ancestorTitles":["Enhanced API Client","Basic Request Functionality"],"duration":4,"failureMessages":[],"fullName":"Enhanced API Client Basic Request Functionality makes successful GET request","status":"passed","title":"makes successful GET request"},{"ancestorTitles":["Enhanced API Client","Basic Request Functionality"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Basic Request Functionality makes successful POST request with body","status":"passed","title":"makes successful POST request with body"},{"ancestorTitles":["Enhanced API Client","Caching Functionality"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Caching Functionality caches GET requests when enabled","status":"passed","title":"caches GET requests when enabled"},{"ancestorTitles":["Enhanced API Client","Caching Functionality"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Caching Functionality does not cache POST requests","status":"passed","title":"does not cache POST requests"},{"ancestorTitles":["Enhanced API Client","Caching Functionality"],"duration":16,"failureMessages":[],"fullName":"Enhanced API Client Caching Functionality respects cache TTL","status":"passed","title":"respects cache TTL"},{"ancestorTitles":["Enhanced API Client","Retry Logic"],"duration":19,"failureMessages":[],"fullName":"Enhanced API Client Retry Logic retries failed requests","status":"passed","title":"retries failed requests"},{"ancestorTitles":["Enhanced API Client","Retry Logic"],"duration":7,"failureMessages":[],"fullName":"Enhanced API Client Retry Logic does not retry non-retryable errors","status":"passed","title":"does not retry non-retryable errors"},{"ancestorTitles":["Enhanced API Client","Retry Logic"],"duration":12,"failureMessages":[],"fullName":"Enhanced API Client Retry Logic gives up after max retries","status":"passed","title":"gives up after max retries"},{"ancestorTitles":["Enhanced API Client","Request Deduplication"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Request Deduplication deduplicates identical concurrent requests","status":"passed","title":"deduplicates identical concurrent requests"},{"ancestorTitles":["Enhanced API Client","Request Deduplication"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Request Deduplication does not deduplicate when disabled","status":"passed","title":"does not deduplicate when disabled"},{"ancestorTitles":["Enhanced API Client","Error Handling"],"duration":7030,"failureMessages":["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mrejects\u001b[2m.\u001b[22mtoThrow\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected substring: \u001b[32m\"HTTP 500: Internal Server Error\"\u001b[39m\nReceived message:   \u001b[31m\"Cannot read properties of undefined (reading 'ok')\"\u001b[39m\n\n    \u001b[0m \u001b[90m 195 |\u001b[39m         clearTimeout(timeoutId)\u001b[33m;\u001b[39m\n     \u001b[90m 196 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 197 |\u001b[39m         \u001b[36mif\u001b[39m (\u001b[33m!\u001b[39mresponse\u001b[33m.\u001b[39mok) {\n     \u001b[90m     |\u001b[39m                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 198 |\u001b[39m           \u001b[36mthrow\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mError\u001b[39m(\u001b[32m`HTTP ${response.status}: ${response.statusText}`\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 199 |\u001b[39m         }\n     \u001b[90m 200 |\u001b[39m\u001b[0m\n\n      \u001b[2mat EnhancedApiClient.ok [as executeRequest] (\u001b[22mutils/apiClient.ts\u001b[2m:197:23)\u001b[22m\n      \u001b[2mat EnhancedApiClient.request (\u001b[22mutils/apiClient.ts\u001b[2m:129:22)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mutils/__tests__/apiClient.test.ts\u001b[2m:255:7)\u001b[22m\n    at Object.toThrow (F:\\DefeaterAI\\node_modules\\expect\\build\\index.js:2151:20)\n    at Object.toThrow (F:\\DefeaterAI\\utils\\__tests__\\apiClient.test.ts:257:17)\n    at Promise.finally.completed (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at _runTest (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (F:\\DefeaterAI\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:272:16)\n    at runTest (F:\\DefeaterAI\\node_modules\\jest-runner\\build\\index.js:340:7)"],"fullName":"Enhanced API Client Error Handling handles HTTP errors correctly","status":"failed","title":"handles HTTP errors correctly"},{"ancestorTitles":["Enhanced API Client","Error Handling"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Error Handling handles network errors correctly","status":"passed","title":"handles network errors correctly"},{"ancestorTitles":["Enhanced API Client","Error Handling"],"duration":7119,"failureMessages":[],"fullName":"Enhanced API Client Error Handling handles timeout errors correctly","status":"passed","title":"handles timeout errors correctly"},{"ancestorTitles":["Enhanced API Client","Metrics Tracking"],"duration":20,"failureMessages":[],"fullName":"Enhanced API Client Metrics Tracking tracks request metrics correctly","status":"passed","title":"tracks request metrics correctly"},{"ancestorTitles":["Enhanced API Client","Metrics Tracking"],"duration":2,"failureMessages":[],"fullName":"Enhanced API Client Metrics Tracking tracks failed request metrics","status":"passed","title":"tracks failed request metrics"},{"ancestorTitles":["Enhanced API Client","Cache Management"],"duration":1,"failureMessages":[],"fullName":"Enhanced API Client Cache Management clears cache correctly","status":"passed","title":"clears cache correctly"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["components/**/*.{js,jsx,ts,tsx}","pages/**/*.{js,jsx,ts,tsx}","utils/**/*.{js,jsx,ts,tsx}","hooks/**/*.{js,jsx,ts,tsx}","contexts/**/*.{js,jsx,ts,tsx}","!**/*.d.ts","!**/node_modules/**","!**/.next/**","!**/coverage/**"],"coverageDirectory":"F:\\DefeaterAI\\coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"coverageThreshold":{"global":{"branches":70,"functions":70,"lines":70,"statements":70}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":1,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\DefeaterAI\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"hideIcon":false,"pageTitle":"DEFEATER.AI Accessibility Test Report"}]],"rootDir":"F:\\DefeaterAI","runInBand":false,"runTestsByPath":false,"seed":**********,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["apiClient.test.ts"],"type":"TestPathPatterns"},"testSequencer":"F:\\DefeaterAI\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1750503882187,"_reporterOptions":{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"pageTitle":"DEFEATER.AI Accessibility Test Report","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})
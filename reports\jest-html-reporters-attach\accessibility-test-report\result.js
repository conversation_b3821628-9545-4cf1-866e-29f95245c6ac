window.jest_html_reporters_callback__({"numFailedTestSuites":0,"numFailedTests":0,"numPassedTestSuites":1,"numPassedTests":10,"numPendingTestSuites":0,"numPendingTests":0,"numRuntimeErrorTestSuites":0,"numTodoTests":0,"numTotalTestSuites":1,"numTotalTests":10,"startTime":1750502795209,"success":false,"testResults":[{"numFailingTests":0,"numPassingTests":10,"numPendingTests":0,"numTodoTests":0,"perfStats":{"end":1750502796087,"loadTestEnvironmentEnd":1750502795673,"loadTestEnvironmentStart":1750502795222,"runtime":412,"setupAfterEnvEnd":1750502796041,"setupAfterEnvStart":1750502795834,"setupFilesEnd":1750502795675,"setupFilesStart":1750502795675,"slow":false,"start":1750502795675},"testFilePath":"F:\\DefeaterAI\\utils\\__tests__\\memoryManagement.test.ts","failureMessage":null,"testResults":[{"ancestorTitles":["MemoryManager"],"duration":3,"failureMessages":[],"fullName":"MemoryManager tracks and cleans up timer resources","status":"passed","title":"tracks and cleans up timer resources"},{"ancestorTitles":["MemoryManager"],"duration":2,"failureMessages":[],"fullName":"MemoryManager tracks and cleans up event listener resources","status":"passed","title":"tracks and cleans up event listener resources"},{"ancestorTitles":["MemoryManager"],"duration":1,"failureMessages":[],"fullName":"MemoryManager cleans up all resources for a component","status":"passed","title":"cleans up all resources for a component"},{"ancestorTitles":["MemoryManager"],"duration":1,"failureMessages":[],"fullName":"MemoryManager cleans up all resources","status":"passed","title":"cleans up all resources"},{"ancestorTitles":["MemoryManager"],"duration":1,"failureMessages":[],"fullName":"MemoryManager handles cleanup errors gracefully","status":"passed","title":"handles cleanup errors gracefully"},{"ancestorTitles":["Managed Resource Functions"],"duration":5,"failureMessages":[],"fullName":"Managed Resource Functions managedSetTimeout creates tracked timer","status":"passed","title":"managedSetTimeout creates tracked timer"},{"ancestorTitles":["Managed Resource Functions"],"duration":2,"failureMessages":[],"fullName":"Managed Resource Functions managedSetInterval creates tracked interval","status":"passed","title":"managedSetInterval creates tracked interval"},{"ancestorTitles":["Managed Resource Functions"],"duration":3,"failureMessages":[],"fullName":"Managed Resource Functions managedAddEventListener creates tracked listener","status":"passed","title":"managedAddEventListener creates tracked listener"},{"ancestorTitles":["useMemoryCleanup Hook"],"duration":1,"failureMessages":[],"fullName":"useMemoryCleanup Hook provides component-scoped resource management","status":"passed","title":"provides component-scoped resource management"},{"ancestorTitles":["Memory Metrics"],"duration":0,"failureMessages":[],"fullName":"Memory Metrics tracks resource creation and cleanup metrics","status":"passed","title":"tracks resource creation and cleanup metrics"}]}],"config":{"bail":0,"changedFilesWithAncestor":false,"ci":false,"collectCoverage":false,"collectCoverageFrom":["components/**/*.{js,jsx,ts,tsx}","pages/**/*.{js,jsx,ts,tsx}","utils/**/*.{js,jsx,ts,tsx}","hooks/**/*.{js,jsx,ts,tsx}","contexts/**/*.{js,jsx,ts,tsx}","!**/*.d.ts","!**/node_modules/**","!**/.next/**","!**/coverage/**"],"coverageDirectory":"F:\\DefeaterAI\\coverage","coverageProvider":"babel","coverageReporters":["json","text","lcov","clover"],"coverageThreshold":{"global":{"branches":70,"functions":70,"lines":70,"statements":70}},"detectLeaks":false,"detectOpenHandles":false,"errorOnDeprecated":false,"expand":false,"findRelatedTests":false,"forceExit":false,"json":false,"lastCommit":false,"listTests":false,"logHeapUsage":false,"maxConcurrency":5,"maxWorkers":1,"noStackTrace":false,"nonFlagArgs":[],"notify":false,"notifyMode":"failure-change","onlyChanged":false,"onlyFailures":false,"openHandlesTimeout":1000,"passWithNoTests":false,"projects":[],"reporters":[["default",{}],["F:\\DefeaterAI\\node_modules\\jest-html-reporters\\index.js",{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"hideIcon":false,"pageTitle":"DEFEATER.AI Accessibility Test Report"}]],"rootDir":"F:\\DefeaterAI","runInBand":false,"runTestsByPath":false,"seed":**********,"skipFilter":false,"snapshotFormat":{"escapeString":false,"printBasicPrototype":false},"testFailureExitCode":1,"testPathPatterns":{"patterns":["memoryManagement.test.ts"],"type":"TestPathPatterns"},"testSequencer":"F:\\DefeaterAI\\node_modules\\@jest\\test-sequencer\\build\\index.js","testTimeout":10000,"updateSnapshot":"new","useStderr":false,"verbose":true,"waitForUnhandledRejections":false,"watch":false,"watchAll":false,"watchman":true,"workerThreads":false},"endTime":1750502796095,"_reporterOptions":{"publicPath":"./reports","filename":"accessibility-test-report.html","expand":true,"pageTitle":"DEFEATER.AI Accessibility Test Report","hideIcon":false,"testCommand":"","openReport":false,"failureMessageOnly":0,"enableMergeData":false,"dataMergeLevel":1,"inlineSource":false,"urlForTestFiles":"","darkTheme":false,"includeConsoleLog":false,"stripSkippedTest":false},"logInfoMapping":{},"attachInfos":{}})
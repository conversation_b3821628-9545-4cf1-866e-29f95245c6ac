/**
 * Accessibility Performance Context
 * 
 * Provides accessibility and performance optimization context
 * for the entire application.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  AccessibilityPreferences,
  DeviceCapabilities,
  PerformanceConfig,
  detectAccessibilityPreferences,
  detectDeviceCapabilities,
  generatePerformanceConfig,
  generatePerformanceClasses,
  AccessibilityPerformanceMonitor
} from '@/utils/accessibilityPerformance';

interface AccessibilityPerformanceContextType {
  preferences: AccessibilityPreferences | null;
  capabilities: DeviceCapabilities | null;
  config: PerformanceConfig | null;
  performanceClasses: string[];
  monitor: AccessibilityPerformanceMonitor | null;
  isLoading: boolean;
  updatePreferences: () => void;
}

const AccessibilityPerformanceContext = createContext<AccessibilityPerformanceContextType | undefined>(undefined);

interface AccessibilityPerformanceProviderProps {
  children: React.ReactNode;
}

export const AccessibilityPerformanceProvider: React.FC<AccessibilityPerformanceProviderProps> = ({ children }) => {
  const [preferences, setPreferences] = useState<AccessibilityPreferences | null>(null);
  const [capabilities, setCapabilities] = useState<DeviceCapabilities | null>(null);
  const [config, setConfig] = useState<PerformanceConfig | null>(null);
  const [performanceClasses, setPerformanceClasses] = useState<string[]>([]);
  const [monitor, setMonitor] = useState<AccessibilityPerformanceMonitor | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const updatePreferences = () => {
    const newPreferences = detectAccessibilityPreferences();
    const newCapabilities = detectDeviceCapabilities();
    const newConfig = generatePerformanceConfig(newPreferences, newCapabilities);
    const newClasses = generatePerformanceClasses(newConfig);
    const newMonitor = new AccessibilityPerformanceMonitor(newConfig);

    setPreferences(newPreferences);
    setCapabilities(newCapabilities);
    setConfig(newConfig);
    setPerformanceClasses(newClasses);
    setMonitor(newMonitor);
    setIsLoading(false);

    // Apply classes to document body
    if (typeof document !== 'undefined') {
      // Remove old performance classes
      document.body.classList.remove(
        'no-animations',
        'no-transitions',
        'no-blur',
        'no-shadows',
        'no-gradients',
        'performance-optimized',
        'memory-optimized',
        'enhanced-focus'
      );

      // Add new performance classes
      newClasses.forEach(className => {
        document.body.classList.add(className);
      });

      // Add general optimization classes
      if (newCapabilities.isLowEnd) {
        document.body.classList.add('performance-optimized', 'memory-optimized');
      }

      if (newPreferences.screenReaderActive) {
        document.body.classList.add('enhanced-focus');
      }

      // Log performance configuration in development
      if (process.env.NODE_ENV === 'development') {
        console.log('🎯 Accessibility Performance Configuration:', {
          preferences: newPreferences,
          capabilities: newCapabilities,
          config: newConfig,
          classes: newClasses
        });
      }
    }
  };

  useEffect(() => {
    // Initial setup
    updatePreferences();

    // Listen for preference changes
    const mediaQueries = [
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-contrast: high)'),
      window.matchMedia('(prefers-reduced-data: reduce)'),
      window.matchMedia('(prefers-reduced-transparency: reduce)'),
      window.matchMedia('(forced-colors: active)')
    ];

    const handleChange = () => {
      updatePreferences();
    };

    mediaQueries.forEach(mq => mq.addEventListener('change', handleChange));

    // Monitor performance
    const performanceObserver = new PerformanceObserver((list) => {
      if (monitor) {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'measure') {
            monitor.recordMetric(entry.name, entry.duration);
          }
        }
      }
    });

    try {
      performanceObserver.observe({ entryTypes: ['measure'] });
    } catch (e) {
      // Performance Observer not supported
    }

    return () => {
      mediaQueries.forEach(mq => mq.removeEventListener('change', handleChange));
      performanceObserver.disconnect();
    };
  }, []);

  // Performance monitoring effect
  useEffect(() => {
    if (!monitor || !config) return;

    const interval = setInterval(() => {
      // Monitor frame rate
      const now = performance.now();
      requestAnimationFrame(() => {
        const frameTime = performance.now() - now;
        monitor.recordMetric('frameTime', frameTime);

        // Optimize if performance is poor
        if (monitor.shouldOptimize('frameTime', 16.67)) { // 60fps threshold
          console.warn('🐌 Performance degradation detected, applying optimizations');
          
          // Apply emergency optimizations
          if (typeof document !== 'undefined') {
            document.body.classList.add('performance-optimized', 'memory-optimized');
          }
        }
      });
    }, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, [monitor, config]);

  const value: AccessibilityPerformanceContextType = {
    preferences,
    capabilities,
    config,
    performanceClasses,
    monitor,
    isLoading,
    updatePreferences
  };

  return (
    <AccessibilityPerformanceContext.Provider value={value}>
      {children}
    </AccessibilityPerformanceContext.Provider>
  );
};

export const useAccessibilityPerformance = (): AccessibilityPerformanceContextType => {
  const context = useContext(AccessibilityPerformanceContext);
  if (context === undefined) {
    throw new Error('useAccessibilityPerformance must be used within an AccessibilityPerformanceProvider');
  }
  return context;
};

/**
 * Hook for conditional rendering based on performance capabilities
 */
export const usePerformanceOptimization = () => {
  const { config, capabilities, preferences } = useAccessibilityPerformance();

  const shouldRender = {
    animations: config?.enableAnimations ?? true,
    transitions: config?.enableTransitions ?? true,
    blur: config?.enableBlur ?? true,
    shadows: config?.enableShadows ?? true,
    gradients: config?.enableGradients ?? true,
    heavyComponents: capabilities?.isLowEnd ? false : true,
    complexAnimations: (config?.maxConcurrentAnimations ?? 5) > 0
  };

  const debounceDelay = config?.debounceDelay ?? 100;
  const lazyLoadThreshold = config?.lazyLoadThreshold ?? 0.1;

  return {
    shouldRender,
    debounceDelay,
    lazyLoadThreshold,
    isLowEnd: capabilities?.isLowEnd ?? false,
    prefersReducedMotion: preferences?.prefersReducedMotion ?? false,
    screenReaderActive: preferences?.screenReaderActive ?? false
  };
};

/**
 * Performance measurement hook
 */
export const usePerformanceMeasurement = (name: string) => {
  const { monitor } = useAccessibilityPerformance();

  const startMeasurement = () => {
    performance.mark(`${name}-start`);
  };

  const endMeasurement = () => {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const measure = performance.getEntriesByName(name, 'measure')[0];
    if (measure && monitor) {
      monitor.recordMetric(name, measure.duration);
    }
  };

  return { startMeasurement, endMeasurement };
};

export default AccessibilityPerformanceProvider;

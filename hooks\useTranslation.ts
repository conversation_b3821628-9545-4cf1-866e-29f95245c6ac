/**
 * Enhanced Translation Hook
 * 
 * Provides comprehensive internationalization support with
 * accessibility features and performance optimizations.
 */

import { useTranslation as useNextTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useCallback, useMemo } from 'react';

export interface TranslationOptions {
  ns?: string | string[];
  keyPrefix?: string;
  fallback?: string;
  interpolation?: Record<string, any>;
  count?: number;
  context?: string;
  defaultValue?: string;
  returnObjects?: boolean;
  lng?: string;
}

export interface AccessibilityTranslationOptions extends TranslationOptions {
  announceChange?: boolean;
  screenReaderOptimized?: boolean;
  preserveWhitespace?: boolean;
  escapeHtml?: boolean;
}

/**
 * Enhanced translation hook with accessibility features
 */
export function useTranslation(ns?: string | string[], options?: TranslationOptions) {
  const { t: nextT, i18n, ready } = useNextTranslation(ns, options);
  const router = useRouter();

  // Enhanced translation function with accessibility support
  const t = useCallback((
    key: string,
    options?: AccessibilityTranslationOptions
  ): string => {
    try {
      // Filter out accessibility-specific options for nextT
      const { announceChange, screenReaderOptimized, preserveWhitespace, escapeHtml, ...nextTOptions } = options || {};
      const translation = nextT(key, nextTOptions);
      
      // Handle accessibility-specific formatting
      if (options?.screenReaderOptimized) {
        return formatForScreenReader(translation);
      }
      
      // Preserve whitespace for code or formatted text
      if (options?.preserveWhitespace) {
        return translation;
      }
      
      // Default formatting
      return cleanTranslation(translation);
    } catch (error) {
      console.warn(`Translation error for key "${key}":`, error);
      return options?.fallback || options?.defaultValue || key;
    }
  }, [nextT]);

  // Accessibility-optimized translation for screen readers
  const tA11y = useCallback((
    key: string,
    options?: AccessibilityTranslationOptions
  ): string => {
    return t(key, {
      ...options,
      screenReaderOptimized: true,
      announceChange: true
    });
  }, [t]);

  // Pluralization helper
  const tPlural = useCallback((
    key: string,
    count: number,
    options?: TranslationOptions
  ): string => {
    return t(key, { ...options, count });
  }, [t]);

  // Context-aware translation
  const tContext = useCallback((
    key: string,
    context: string,
    options?: TranslationOptions
  ): string => {
    return t(`${key}_${context}`, options) || t(key, options);
  }, [t]);

  // Interpolation helper
  const tInterpolate = useCallback((
    key: string,
    values: Record<string, any>,
    options?: TranslationOptions
  ): string => {
    return t(key, { ...options, interpolation: values, ...values });
  }, [t]);

  // Format translation for screen readers
  const formatForScreenReader = useCallback((text: string): string => {
    return text
      // Add pauses for better pronunciation
      .replace(/\./g, '. ')
      .replace(/,/g, ', ')
      .replace(/:/g, ': ')
      .replace(/;/g, '; ')
      // Expand abbreviations
      .replace(/\bAI\b/g, 'Artificial Intelligence')
      .replace(/\bUI\b/g, 'User Interface')
      .replace(/\bAPI\b/g, 'Application Programming Interface')
      // Clean up extra spaces
      .replace(/\s+/g, ' ')
      .trim();
  }, []);

  // Clean translation text
  const cleanTranslation = useCallback((text: string): string => {
    return text
      .replace(/\s+/g, ' ')
      .trim();
  }, []);

  // Language switching with accessibility announcements
  const changeLanguage = useCallback(async (
    lng: string,
    announce: boolean = true
  ): Promise<void> => {
    try {
      await i18n.changeLanguage(lng);
      
      if (announce && typeof window !== 'undefined') {
        // Announce language change to screen readers
        announceToScreenReader(
          t('accessibility.feedback.languageChanged', {
            interpolation: { language: t(`common.language.${lng}`) }
          })
        );
      }
      
      // Update URL if needed
      if (router.locale !== lng) {
        router.push(router.asPath, router.asPath, { locale: lng });
      }
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  }, [i18n, router, t]);

  // Announce message to screen readers
  const announceToScreenReader = useCallback((message: string): void => {
    if (typeof window === 'undefined') return;

    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }, []);

  // Get available languages
  const availableLanguages = useMemo(() => {
    const supportedLngs = i18n.options.supportedLngs;
    if (Array.isArray(supportedLngs)) {
      return supportedLngs.filter(lng => lng !== 'cimode');
    }
    return [];
  }, [i18n.options.supportedLngs]);

  // Get current language info
  const currentLanguage = useMemo(() => {
    const lng = i18n.language;
    return {
      code: lng,
      name: t(`common.language.${lng}`),
      direction: ['ar', 'he', 'fa'].includes(lng) ? 'rtl' : 'ltr',
      isRTL: ['ar', 'he', 'fa'].includes(lng)
    };
  }, [i18n.language, t]);

  // Check if translation exists
  const exists = useCallback((key: string, options?: TranslationOptions): boolean => {
    return i18n.exists(key, options as any);
  }, [i18n]);

  // Get raw translation data
  const getResource = useCallback((
    lng: string,
    ns: string,
    key: string
  ): any => {
    return i18n.getResource(lng, ns, key);
  }, [i18n]);

  // Format date/time according to locale
  const formatDate = useCallback((
    date: Date,
    options?: Intl.DateTimeFormatOptions
  ): string => {
    return new Intl.DateTimeFormat(i18n.language, options).format(date);
  }, [i18n.language]);

  // Format number according to locale
  const formatNumber = useCallback((
    number: number,
    options?: Intl.NumberFormatOptions
  ): string => {
    return new Intl.NumberFormat(i18n.language, options).format(number);
  }, [i18n.language]);

  // Format currency according to locale
  const formatCurrency = useCallback((
    amount: number,
    currency: string,
    options?: Intl.NumberFormatOptions
  ): string => {
    return new Intl.NumberFormat(i18n.language, {
      style: 'currency',
      currency,
      ...options
    }).format(amount);
  }, [i18n.language]);

  // Get relative time format
  const formatRelativeTime = useCallback((
    value: number,
    unit: Intl.RelativeTimeFormatUnit,
    options?: Intl.RelativeTimeFormatOptions
  ): string => {
    return new Intl.RelativeTimeFormat(i18n.language, options).format(value, unit);
  }, [i18n.language]);

  // Accessibility helpers
  const accessibility = useMemo(() => ({
    // Screen reader optimized translation
    screenReader: tA11y,
    
    // Announce to screen readers
    announce: announceToScreenReader,
    
    // Get ARIA labels
    ariaLabel: (key: string, options?: TranslationOptions) => 
      t(`accessibility.${key}`, options),
    
    // Get button descriptions
    buttonLabel: (action: string, item?: string) =>
      t('accessibility.buttons.' + action, { interpolation: { item } }),
    
    // Get state descriptions
    stateLabel: (state: string) =>
      t('accessibility.states.' + state),
    
    // Get form field descriptions
    fieldLabel: (type: string, options?: TranslationOptions) =>
      t('accessibility.forms.' + type, options),
    
    // Get navigation descriptions
    navLabel: (type: string, options?: TranslationOptions) =>
      t('accessibility.navigation.' + type, options)
  }), [t, tA11y, announceToScreenReader]);

  return {
    // Core translation functions
    t,
    tA11y,
    tPlural,
    tContext,
    tInterpolate,
    
    // Language management
    changeLanguage,
    currentLanguage,
    availableLanguages,
    
    // Utilities
    exists,
    getResource,
    ready,
    
    // Formatting
    formatDate,
    formatNumber,
    formatCurrency,
    formatRelativeTime,
    
    // Accessibility
    accessibility,
    
    // i18n instance
    i18n
  };
}

export default useTranslation;

/**
 * Cross-Browser Compatibility Testing Suite
 * 
 * Tests restored UX across Chrome, Firefox, Safari, Edge
 * Verifies CSS variable support, responsive behavior, and component interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Import components to test
import HomePage from '@/pages/index';
import OpenDesignTestPage from '@/pages/open-design-test';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/',
  }),
}));

// Mock API calls
global.fetch = jest.fn();

// Mock portal for testing
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (node: React.ReactNode) => node,
}));

// Browser detection utilities
const getBrowserInfo = () => {
  const userAgent = navigator.userAgent;
  
  if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
    return { name: 'Chrome', version: userAgent.match(/Chrome\/(\d+)/)?.[1] };
  } else if (userAgent.includes('Firefox')) {
    return { name: 'Firefox', version: userAgent.match(/Firefox\/(\d+)/)?.[1] };
  } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    return { name: 'Safari', version: userAgent.match(/Version\/(\d+)/)?.[1] };
  } else if (userAgent.includes('Edg')) {
    return { name: 'Edge', version: userAgent.match(/Edg\/(\d+)/)?.[1] };
  }
  
  return { name: 'Unknown', version: 'Unknown' };
};

describe('Cross-Browser Compatibility Testing', () => {
  
  beforeEach(() => {
    // Reset fetch mock
    (fetch as jest.MockedFunction<typeof fetch>).mockClear();
    
    // Mock successful game start API response
    (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        gameState: {
          gameId: 'test-game-123',
          currentWord: 'transformer',
          step: 1,
          maxSteps: 25,
          targets: ['innovation', 'ecosystem', 'friction', 'paradigm'],
          completedTargets: [],
          burnedTargets: [],
          definitions: [],
          difficulty: 'medium',
          usedWords: [],
          aiChallengeWords: [],
          gameStatus: 'waiting',
          consecutiveRejections: 0,
          commonWordsUsage: {},
          rejectionHistory: []
        }
      })
    } as Response);
  });

  describe('CSS Variable Support', () => {
    test('CSS custom properties should be supported', () => {
      render(<OpenDesignTestPage />);
      
      // Check if CSS variables are supported
      const testElement = document.createElement('div');
      testElement.style.setProperty('--test-var', 'test-value');
      document.body.appendChild(testElement);
      
      const computedStyle = window.getComputedStyle(testElement);
      const supportsCustomProperties = computedStyle.getPropertyValue('--test-var') === 'test-value';
      
      document.body.removeChild(testElement);
      
      expect(supportsCustomProperties).toBe(true);
    });

    test('Design system variables should be accessible', () => {
      render(<OpenDesignTestPage />);
      
      // Test key design system variables
      const rootElement = document.documentElement;
      const computedStyle = window.getComputedStyle(rootElement);
      
      const keyVariables = [
        '--text-primary',
        '--bg-primary',
        '--accent-cyan',
        '--accent-purple',
        '--space-4',
        '--radius-lg',
        '--z-modal'
      ];
      
      keyVariables.forEach(variable => {
        const value = computedStyle.getPropertyValue(variable);
        expect(value).toBeTruthy();
      });
    });

    test('Color variables should resolve correctly', () => {
      render(<OpenDesignTestPage />);
      
      const rootElement = document.documentElement;
      const computedStyle = window.getComputedStyle(rootElement);
      
      // Test color resolution
      const primaryColor = computedStyle.getPropertyValue('--color-primary');
      const accentColor = computedStyle.getPropertyValue('--color-accent');
      
      expect(primaryColor).toMatch(/#[0-9a-fA-F]{6}|rgb\(\d+,\s*\d+,\s*\d+\)/);
      expect(accentColor).toMatch(/#[0-9a-fA-F]{6}|rgb\(\d+,\s*\d+,\s*\d+\)/);
    });
  });

  describe('Responsive Behavior', () => {
    test('Viewport meta tag should be present', () => {
      render(<HomePage />);
      
      const viewportMeta = document.querySelector('meta[name="viewport"]');
      expect(viewportMeta).toBeInTheDocument();
      expect(viewportMeta?.getAttribute('content')).toContain('width=device-width');
    });

    test('Media queries should work correctly', () => {
      // Test mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      // Trigger resize event
      fireEvent(window, new Event('resize'));
      
      render(<OpenDesignTestPage />);
      
      // Check if mobile styles are applied
      const mobileElements = document.querySelectorAll('[class*="mobile"], [class*="sm:"]');
      expect(mobileElements.length).toBeGreaterThan(0);
    });

    test('Breakpoint variables should work', () => {
      render(<OpenDesignTestPage />);
      
      const rootElement = document.documentElement;
      const computedStyle = window.getComputedStyle(rootElement);
      
      const breakpoints = [
        '--bp-xs',
        '--bp-sm', 
        '--bp-md',
        '--bp-lg',
        '--bp-xl'
      ];
      
      breakpoints.forEach(breakpoint => {
        const value = computedStyle.getPropertyValue(breakpoint);
        expect(value).toMatch(/\d+px/);
      });
    });

    test('Touch targets should be appropriately sized', () => {
      // Mock touch device
      Object.defineProperty(window, 'ontouchstart', {
        value: () => {},
        writable: true
      });
      
      render(<OpenDesignTestPage />);
      
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        const computedStyle = window.getComputedStyle(button);
        const minHeight = parseInt(computedStyle.minHeight) || parseInt(computedStyle.height);
        
        // WCAG 2.1 minimum touch target size is 44px
        expect(minHeight).toBeGreaterThanOrEqual(44);
      });
    });
  });

  describe('Component Interactions', () => {
    test('Modal interactions should work across browsers', async () => {
      const user = userEvent.setup();
      
      render(<OpenDesignTestPage />);
      
      // Look for modal trigger
      const modalTrigger = screen.queryByRole('button', { name: /rules/i }) ||
                          screen.queryByRole('button', { name: /help/i });
      
      if (modalTrigger) {
        await user.click(modalTrigger);
        
        // Check if modal opens
        const modal = screen.queryByRole('dialog') || 
                     document.querySelector('[class*="modal"]');
        
        expect(modal).toBeTruthy();
      }
    });

    test('Side panel should work on all browsers', async () => {
      const user = userEvent.setup();
      
      render(<OpenDesignTestPage />);
      
      // Look for side panel toggle
      const panelToggle = screen.queryByRole('button', { name: /toggle/i }) ||
                         screen.queryByRole('button', { name: /menu/i });
      
      if (panelToggle) {
        await user.click(panelToggle);
        
        // Check if panel opens
        const panel = screen.queryByRole('complementary');
        expect(panel).toBeTruthy();
      }
    });

    test('Input focus should work consistently', async () => {
      const user = userEvent.setup();
      
      render(<OpenDesignTestPage />);
      
      const textInput = screen.queryByRole('textbox');
      if (textInput) {
        await user.click(textInput);
        expect(textInput).toHaveFocus();
        
        // Test typing
        await user.type(textInput, 'test input');
        expect(textInput).toHaveValue('test input');
      }
    });

    test('Keyboard navigation should work', async () => {
      const user = userEvent.setup();
      
      render(<OpenDesignTestPage />);
      
      // Test tab navigation
      await user.tab();
      const firstFocusable = document.activeElement;
      expect(firstFocusable).toBeTruthy();
      
      await user.tab();
      const secondFocusable = document.activeElement;
      expect(secondFocusable).not.toBe(firstFocusable);
    });
  });

  describe('Animation and Transitions', () => {
    test('CSS transitions should be supported', () => {
      render(<OpenDesignTestPage />);
      
      const testElement = document.createElement('div');
      testElement.style.transition = 'opacity 0.3s ease';
      document.body.appendChild(testElement);
      
      const computedStyle = window.getComputedStyle(testElement);
      const transitionProperty = computedStyle.transitionProperty;
      
      document.body.removeChild(testElement);
      
      expect(transitionProperty).toBe('opacity');
    });

    test('Transform animations should work', () => {
      render(<OpenDesignTestPage />);
      
      const testElement = document.createElement('div');
      testElement.style.transform = 'translateX(10px)';
      document.body.appendChild(testElement);
      
      const computedStyle = window.getComputedStyle(testElement);
      const transform = computedStyle.transform;
      
      document.body.removeChild(testElement);
      
      expect(transform).toContain('matrix') || expect(transform).toContain('translateX');
    });

    test('Backdrop filter should be supported or gracefully degrade', () => {
      render(<OpenDesignTestPage />);
      
      const testElement = document.createElement('div');
      testElement.style.backdropFilter = 'blur(10px)';
      document.body.appendChild(testElement);
      
      const computedStyle = window.getComputedStyle(testElement);
      const backdropFilter = computedStyle.backdropFilter || computedStyle.webkitBackdropFilter;
      
      document.body.removeChild(testElement);
      
      // Should either support backdrop-filter or gracefully degrade
      expect(backdropFilter === 'blur(10px)' || backdropFilter === 'none').toBe(true);
    });
  });

  describe('Performance Across Browsers', () => {
    test('Page should load within acceptable time', async () => {
      const startTime = performance.now();
      
      render(<HomePage />);
      
      await waitFor(() => {
        expect(screen.queryByText('Initializing DEFEATER.AI...')).not.toBeInTheDocument();
      }, { timeout: 5000 });
      
      const loadTime = performance.now() - startTime;
      
      // Should load within 3 seconds on any browser
      expect(loadTime).toBeLessThan(3000);
    });

    test('Memory usage should be reasonable', () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      render(<OpenDesignTestPage />);
      
      const afterRenderMemory = (performance as any).memory?.usedJSHeapSize || 0;
      const memoryIncrease = afterRenderMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 50MB)
      if (memoryIncrease > 0) {
        expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
      }
    });
  });

  describe('Browser-Specific Features', () => {
    test('Should handle browser-specific CSS prefixes', () => {
      render(<OpenDesignTestPage />);
      
      const testElement = document.createElement('div');
      testElement.style.userSelect = 'none';
      testElement.style.webkitUserSelect = 'none';
      testElement.style.mozUserSelect = 'none';
      testElement.style.msUserSelect = 'none';
      
      document.body.appendChild(testElement);
      
      const computedStyle = window.getComputedStyle(testElement);
      const userSelect = computedStyle.userSelect || 
                        computedStyle.webkitUserSelect || 
                        computedStyle.mozUserSelect;
      
      document.body.removeChild(testElement);
      
      expect(userSelect).toBe('none');
    });

    test('Should work with different font rendering', () => {
      render(<OpenDesignTestPage />);
      
      const textElements = document.querySelectorAll('h1, h2, h3, p, span');
      
      textElements.forEach(element => {
        const computedStyle = window.getComputedStyle(element);
        const fontFamily = computedStyle.fontFamily;
        
        // Should have fallback fonts
        expect(fontFamily).toContain('Inter') || expect(fontFamily).toContain('system-ui');
      });
    });
  });

  describe('Accessibility Across Browsers', () => {
    test('ARIA attributes should be supported', () => {
      render(<OpenDesignTestPage />);
      
      const ariaElements = document.querySelectorAll('[aria-label], [aria-labelledby], [role]');
      expect(ariaElements.length).toBeGreaterThan(0);
      
      ariaElements.forEach(element => {
        const ariaLabel = element.getAttribute('aria-label');
        const role = element.getAttribute('role');
        
        if (ariaLabel) {
          expect(ariaLabel).toBeTruthy();
        }
        if (role) {
          expect(role).toBeTruthy();
        }
      });
    });

    test('Focus indicators should be visible', async () => {
      const user = userEvent.setup();
      
      render(<OpenDesignTestPage />);
      
      const focusableElements = document.querySelectorAll('button, input, [tabindex]');
      
      if (focusableElements.length > 0) {
        const firstElement = focusableElements[0] as HTMLElement;
        firstElement.focus();
        
        const computedStyle = window.getComputedStyle(firstElement);
        const outline = computedStyle.outline;
        const boxShadow = computedStyle.boxShadow;
        
        // Should have visible focus indicator
        expect(outline !== 'none' || boxShadow !== 'none').toBe(true);
      }
    });
  });
});

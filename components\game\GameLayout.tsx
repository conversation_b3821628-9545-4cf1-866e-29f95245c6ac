/**
 * GameLayout Component - Main Game Layout Orchestrator (v2.0)
 *
 * 🎯 SPATIAL DESIGN GAME LAYOUT
 * 
 * Features:
 * - Spatial design system layout structure
 * - Responsive grid layout with side panel
 * - Component orchestration and state management
 * - Performance optimized rendering
 * - Accessibility compliance
 * 
 * Performance Benefits:
 * - Optimized layout calculations
 * - Efficient component composition
 * - Responsive breakpoint management
 * - Clean separation of concerns
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_5-6_COMPONENT_DECOMPOSITION.md
 */

import React, { memo, ReactNode } from 'react';

interface GameLayoutProps {
  // Layout Components
  header: ReactNode;
  mainContent: ReactNode;
  sidePanel: ReactNode;
  modals?: ReactNode;
  overlays?: ReactNode;
  
  // Layout State
  showSidePanel: boolean;
  isResponsiveMode: boolean;
  
  // Configuration
  enableAnimations?: boolean;
  className?: string;
}

const GameLayout: React.FC<GameLayoutProps> = memo(({
  header,
  mainContent,
  sidePanel,
  modals,
  overlays,
  showSidePanel,
  isResponsiveMode,
  enableAnimations = true,
  className = ''
}) => {
  return (
    <div className={`spatial-game-layout ${className}`}>
      {/* Main Game Content Area */}
      <main 
        className="game-main-area"
        role="main"
        aria-label="Game content"
      >
        {/* Game Header */}
        <div className="game-header-container">
          {header}
        </div>

        {/* Game Content */}
        <div className="game-content-container">
          {mainContent}
        </div>
      </main>

      {/* Side Panel Area */}
      {showSidePanel && (
        <aside 
          className={`game-side-area ${enableAnimations ? 'animate-slide-in' : ''}`}
          role="complementary"
          aria-label="Game information panel"
          id="side-panel"
        >
          {sidePanel}
        </aside>
      )}

      {/* Modal Overlays */}
      {modals && (
        <div className="game-modals-container">
          {modals}
        </div>
      )}

      {/* Additional Overlays */}
      {overlays && (
        <div className="game-overlays-container">
          {overlays}
        </div>
      )}

      <style jsx>{`
        .spatial-game-layout {
          display: grid;
          grid-template-columns: ${showSidePanel ? '1fr auto' : '1fr'};
          min-height: 100vh;
          gap: 0;
          position: relative;
          background: var(--color-background);
          color: var(--color-foreground);
        }

        .game-main-area {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
          overflow-x: hidden;
          position: relative;
        }

        .game-header-container {
          flex-shrink: 0;
          position: relative;
          z-index: var(--z-10);
        }

        .game-content-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          padding: 0 var(--space-4);
          max-width: 1200px;
          margin: 0 auto;
          width: 100%;
        }

        .game-side-area {
          width: 400px;
          min-height: 100vh;
          background: var(--color-card);
          border-left: 1px solid var(--color-border);
          position: relative;
          z-index: var(--z-20);
        }

        .game-modals-container {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: var(--z-modal);
          pointer-events: none;
        }

        .game-modals-container > * {
          pointer-events: auto;
        }

        .game-overlays-container {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: var(--z-50);
          pointer-events: none;
        }

        .game-overlays-container > * {
          pointer-events: auto;
        }

        /* Animations */
        .animate-slide-in {
          animation: slideInFromRight 0.3s ease-out;
        }

        @keyframes slideInFromRight {
          0% {
            transform: translateX(100%);
            opacity: 0;
          }
          100% {
            transform: translateX(0);
            opacity: 1;
          }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
          .spatial-game-layout {
            grid-template-columns: 1fr;
            grid-template-rows: 1fr ${showSidePanel ? 'auto' : ''};
          }

          .game-side-area {
            width: 100%;
            min-height: auto;
            max-height: 50vh;
            border-left: none;
            border-top: 1px solid var(--color-border);
            overflow-y: auto;
          }

          .animate-slide-in {
            animation: slideInFromBottom 0.3s ease-out;
          }

          @keyframes slideInFromBottom {
            0% {
              transform: translateY(100%);
              opacity: 0;
            }
            100% {
              transform: translateY(0);
              opacity: 1;
            }
          }
        }

        @media (max-width: 767px) {
          .game-content-container {
            padding: 0 var(--space-2);
          }

          .game-side-area {
            max-height: 60vh;
          }
        }

        @media (max-width: 480px) {
          .game-content-container {
            padding: 0 var(--space-1);
          }

          .game-side-area {
            max-height: 70vh;
          }
        }

        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
          .game-side-area {
            border-left-width: 2px;
            border-top-width: 2px;
          }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
          .animate-slide-in {
            animation: none;
          }
        }

        /* Dark Mode Adjustments */
        @media (prefers-color-scheme: dark) {
          .spatial-game-layout {
            background: var(--color-background-dark);
            color: var(--color-foreground-dark);
          }

          .game-side-area {
            background: var(--color-card-dark);
            border-color: var(--color-border-dark);
          }
        }

        /* Print Styles */
        @media print {
          .spatial-game-layout {
            grid-template-columns: 1fr;
            min-height: auto;
          }

          .game-side-area,
          .game-modals-container,
          .game-overlays-container {
            display: none;
          }

          .game-content-container {
            padding: 0;
            max-width: none;
          }
        }

        /* Focus Management */
        .spatial-game-layout:focus-within .game-main-area {
          position: relative;
        }

        /* Accessibility Improvements */
        @media (prefers-reduced-motion: reduce) {
          * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }

        /* Performance Optimizations */
        .game-main-area,
        .game-side-area {
          contain: layout style;
        }

        .game-content-container {
          contain: layout;
        }

        /* Smooth Scrolling */
        .game-side-area {
          scroll-behavior: smooth;
        }

        @media (prefers-reduced-motion: reduce) {
          .game-side-area {
            scroll-behavior: auto;
          }
        }
      `}</style>
    </div>
  );
});

GameLayout.displayName = 'GameLayout';

export default GameLayout;

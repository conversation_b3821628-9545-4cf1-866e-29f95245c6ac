/**
 * Floating <PERSON><PERSON> Dialog Styles
 * 
 * Custom scrollbar and animation styles for the chat dialog
 */

/* Custom scrollbar for chat messages */
.chatScrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(75, 85, 99, 0.8) rgba(31, 41, 55, 0.5);
}

.chatScrollbar::-webkit-scrollbar {
  width: 6px;
}

.chatScrollbar::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 3px;
}

.chatScrollbar::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.8);
  border-radius: 3px;
}

.chatScrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.9);
}

/* Smooth message animations */
.messageEnter {
  opacity: 0;
  transform: translateX(-20px);
}

.messageEnterActive {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

/* Pulse animation for new messages */
.newMessagePulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Glow effect for the floating button */
.floatingButtonGlow {
  box-shadow: 
    0 0 20px rgba(239, 68, 68, 0.3),
    0 0 40px rgba(239, 68, 68, 0.1);
}

.floatingButtonGlow:hover {
  box-shadow: 
    0 0 25px rgba(239, 68, 68, 0.4),
    0 0 50px rgba(239, 68, 68, 0.2);
}

/* Dialog backdrop blur effect */
.dialogBackdrop {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Message type indicators */
.messageTypeBurn {
  border-left: 3px solid #ef4444;
}

.messageTypeWin {
  border-left: 3px solid #10b981;
}

.messageTypeLoss {
  border-left: 3px solid #f59e0b;
}

.messageTypeStart {
  border-left: 3px solid #8b5cf6;
}

.messageTypeMove {
  border-left: 3px solid #6b7280;
}

/* Typing indicator animation */
.typingIndicator {
  display: inline-flex;
  align-items: center;
}

.typingIndicator::after {
  content: '';
  display: inline-block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
  animation: typing 1.4s infinite;
  margin-left: 2px;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: calc(var(--bp-sm) - 1px)) {
  .chatDialog {
    width: calc(100vw - 2rem);
    max-width: 320px;
  }

  .floatingButton {
    width: 48px;
    height: 48px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .chatScrollbar::-webkit-scrollbar-thumb {
    background: #ffffff;
  }
  
  .messageTypeBurn {
    border-left-color: #ffffff;
  }
  
  .messageTypeWin {
    border-left-color: #ffffff;
  }
  
  .messageTypeLoss {
    border-left-color: #ffffff;
  }
  
  .messageTypeStart {
    border-left-color: #ffffff;
  }
  
  .messageTypeMove {
    border-left-color: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .messageEnterActive {
    transition: none;
  }
  
  .newMessagePulse {
    animation: none;
  }
  
  .typingIndicator::after {
    animation: none;
  }
  
  .floatingButtonGlow {
    box-shadow: none;
  }
}

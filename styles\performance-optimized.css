/**
 * Performance-Optimized CSS Bundle - DEFEATER.AI (v2.0)
 *
 * 🎯 WEEK 5 DAY 5: STYLING PERFORMANCE OPTIMIZATION
 * 
 * This file consolidates critical CSS for optimal performance:
 * - Critical path CSS for above-the-fold content
 * - Optimized design system variables
 * - Performance-first responsive design
 * - Accessibility compliance maintained
 * 
 * Benefits:
 * - Reduced CSS bundle size
 * - Faster initial page load
 * - Better caching strategy
 * - Improved Core Web Vitals
 * 
 * @version 2.0 - Performance Optimization
 * @see docs/WEEK_5_PERFORMANCE_OPTIMIZATION.md
 */

/* === CRITICAL PATH CSS === */

/* Essential design system variables for immediate rendering */
:root {
  /* Core colors - most frequently used */
  --color-background: #0A0A0F;
  --color-foreground: #FFFFFF;
  --color-primary: #00F5FF;
  --color-border: #252533;
  --color-card: #12121A;
  
  /* Essential spacing - critical layout */
  --space-2: 0.5rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  
  /* Critical typography */
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --line-height-base: 1.5;
  
  /* Essential effects */
  --glass-medium: rgba(255, 255, 255, 0.10);
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --shadow-dark-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
  
  /* Performance optimizations */
  --transition-fast: 150ms ease;
}

/* === CRITICAL LAYOUT === */

/* Base layout - immediate rendering */
body {
  background: var(--color-background);
  color: var(--color-foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* Critical game layout */
.spatial-game-layout {
  display: grid;
  grid-template-columns: 1fr auto;
  min-height: 100vh;
  gap: 0;
  position: relative;
}

/* Essential open spatial design - FINAL CONTAINER ELIMINATION */
.game-over-card,
.loading-card {
  /* REMOVED: background, border, border-radius for open design */
  padding: var(--space-10);
  /* Enhanced spatial shadow instead of container border */
  filter: drop-shadow(0 8px 32px rgba(0, 0, 0, 0.3));
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  /* Enhanced breathing room for open design */
}

/* Critical text alignment */
.text-center {
  text-align: center;
}

/* Essential flex utilities */
.flex {
  display: flex;
}

.gap-4 {
  gap: var(--space-4);
}

/* === PERFORMANCE OPTIMIZATIONS === */

/* GPU acceleration for critical elements */
.game-over-card,
.loading-card,
.spatial-game-layout {
  transform: translateZ(0);
  will-change: auto;
}

/* Reduce layout thrashing */
.spatial-game-layout {
  contain: layout style;
}

/* === RESPONSIVE CRITICAL PATH === */

/* Mobile-first critical breakpoint */
@media (max-width: 1024px) {
  .spatial-game-layout {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }
}

/* Mobile optimizations - Open spatial design */
@media (max-width: 767px) {
  .game-over-card,
  .loading-card {
    padding: var(--space-8);
    margin: var(--space-6);
    /* Enhanced mobile breathing room */
  }
}

/* === ACCESSIBILITY CRITICAL PATH === */

/* Essential focus indicators */
:focus,
:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode - Open spatial approach */
@media (prefers-contrast: high) {
  .game-over-card,
  .loading-card {
    /* Use subtle background for high contrast instead of borders */
    background: rgba(15, 23, 42, 0.95);
    border-radius: var(--radius-xl);
  }
}

/* === LOADING OPTIMIZATION === */

/* Critical loading states */
.loading-card {
  max-width: 400px;
  margin: var(--space-8) auto;
}

.loading-text {
  margin-top: var(--space-4);
  color: var(--color-muted-foreground, #94A3B8);
}

/* === PRINT OPTIMIZATION === */

@media print {
  .spatial-game-layout {
    grid-template-columns: 1fr;
    min-height: auto;
  }
  
  .game-over-card,
  .loading-card {
    /* Open design for print - minimal styling */
    color: black !important;
    filter: none !important;
    padding: var(--space-4);
  }
}

/* === PERFORMANCE MONITORING === */

/* Mark critical CSS loaded */
body::after {
  content: '';
  display: none;
  /* This can be detected by performance monitoring */
}

/* === DEFERRED STYLES PLACEHOLDER === */

/* 
 * Non-critical styles should be loaded separately:
 * - Complex animations
 * - Detailed component styles
 * - Advanced effects
 * - Secondary features
 * 
 * This keeps the critical path lean and fast.
 */

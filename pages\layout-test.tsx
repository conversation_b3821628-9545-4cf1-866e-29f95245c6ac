/**
 * Layout Test Page
 * 
 * Temporary page to test our new responsive grid layout system
 * This will be removed after validation
 */

import React, { useState } from 'react';
import Head from 'next/head';
import GameLayout from '@/components/layout/GameLayout';
import GameFocus from '@/components/game/GameFocus';
import { Hero, Primary, Secondary, Body, Small, Accent, Status, Mono } from '@/components/ui/Typography';

const LayoutTestPage: React.FC = () => {
  const [showSidePanel, setShowSidePanel] = useState(false);

  const toggleSidePanel = () => {
    setShowSidePanel(!showSidePanel);
  };

  const sidePanel = (
    <div>
      <Primary style={{ marginBottom: 'var(--space-4)' }}>
        Side Panel Test
      </Primary>
      <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-4)' }}>
        <div className="card-gradient">
          <Secondary style={{ marginBottom: 'var(--space-2)' }}>Statistics</Secondary>
          <Body>Win rate: <Accent>23%</Accent></Body>
          <Body>Average turns: <Accent>18.5</Accent></Body>
          <Small>Games played: 47</Small>
        </div>
        <div className="card-gradient">
          <Secondary style={{ marginBottom: 'var(--space-2)' }}>History</Secondary>
          <Body style={{ marginBottom: 'var(--space-1)' }}>transformer → <Status status="success">electrical device</Status></Body>
          <Body style={{ marginBottom: 'var(--space-1)' }}>voltage → <Status status="success">electrical force</Status></Body>
          <Small>2 definitions submitted</Small>
        </div>
        <div className="card-gradient">
          <Secondary style={{ marginBottom: 'var(--space-2)' }}>Targets</Secondary>
          <Body style={{ marginBottom: 'var(--space-1)' }}><Mono>I n n o v _ _ _ _ n</Mono></Body>
          <Body style={{ marginBottom: 'var(--space-1)' }}><Mono>E c o s _ _ _ _ m</Mono></Body>
          <Small style={{ opacity: 0.6 }}>friction (burned)</Small>
        </div>
        <div className="card-gradient">
          <Secondary style={{ marginBottom: 'var(--space-2)' }}>Rules</Secondary>
          <Body style={{ marginBottom: 'var(--space-1)' }}>• Max 25 steps</Body>
          <Body style={{ marginBottom: 'var(--space-1)' }}>• No word reuse</Body>
          <Body>• Shorter definitions</Body>
        </div>
      </div>
    </div>
  );

  return (
    <>
      <Head>
        <title>Layout Test - DEFEATER.AI</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <GameLayout>
        <GameFocus>
          {/* Typography Test Section */}
          <div style={{ marginBottom: 'var(--space-8)', textAlign: 'center' }}>
            <Hero>TRANSFORMER</Hero>
            <Primary style={{ marginTop: 'var(--space-4)' }}>Define this word:</Primary>
          </div>

          {/* Typography Scale Demo */}
          <div style={{ marginBottom: 'var(--space-8)', width: '100%', maxWidth: '600px' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-3)', textAlign: 'left' }}>
              <div>
                <Small as="label">Hero Text (48px+):</Small>
                <Hero as="div">The Quick Brown Fox</Hero>
              </div>
              <div>
                <Small as="label">Primary Text (24px):</Small>
                <Primary as="div">The Quick Brown Fox Jumps</Primary>
              </div>
              <div>
                <Small as="label">Secondary Text (18px):</Small>
                <Secondary as="div">The Quick Brown Fox Jumps Over</Secondary>
              </div>
              <div>
                <Small as="label">Body Text (16px):</Small>
                <Body as="div">The Quick Brown Fox Jumps Over The Lazy Dog</Body>
              </div>
              <div>
                <Small as="label">Small Text (14px):</Small>
                <Small as="div">The Quick Brown Fox Jumps Over The Lazy Dog</Small>
              </div>
            </div>
          </div>

          {/* Status and Accent Text Demo */}
          <div style={{ marginBottom: 'var(--space-8)', width: '100%', maxWidth: '600px' }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-2)', textAlign: 'left' }}>
              <div style={{ display: 'flex', gap: 'var(--space-4)', flexWrap: 'wrap' }}>
                <Status status="success">✓ Valid definition</Status>
                <Status status="error">✗ Invalid definition</Status>
                <Status status="warning">⚠ Word limit approaching</Status>
                <Status status="info">ℹ Target revealed</Status>
                <Status status="processing">⟳ AI processing...</Status>
              </div>
              <div style={{ display: 'flex', gap: 'var(--space-4)', flexWrap: 'wrap', alignItems: 'center' }}>
                <Accent>Highlighted text</Accent>
                <Small style={{ opacity: 0.6 }}>Muted information</Small>
                <Accent>Gradient emphasis</Accent>
                <Mono>I n n o _ _ _ _ _ n</Mono>
              </div>
            </div>
          </div>

          {/* Input Area */}
          <div style={{ width: '100%', maxWidth: '600px' }}>
            <textarea
              className="input-primary"
              placeholder="Enter your definition here... (6 words max)"
              rows={4}
              style={{ marginBottom: 'var(--space-6)' }}
            />
          </div>

          {/* Submit Button */}
          <button className="btn-primary" style={{ marginBottom: 'var(--space-8)' }}>
            Submit Definition
          </button>

          {/* Target Strip */}
          <div className="target-strip">
            <div style={{ display: 'flex', gap: 'var(--space-4)', flexWrap: 'wrap', justifyContent: 'center' }}>
              <span className="text-body">Targets:</span>
              <span className="text-secondary-large">I n n o _ _ _ _ _ n</span>
              <span className="text-muted">•</span>
              <span className="text-secondary-large">E c o _ _ _ _ _ m</span>
              <span className="text-muted">•</span>
              <span className="text-secondary-large" style={{ textDecoration: 'line-through', opacity: 0.6 }}>
                F r i _ _ _ _ n (🔥 burned)
              </span>
            </div>
          </div>

          {/* Progress Indicator */}
          <div className="progress-indicator">
            Step 5/25 • 2 targets left
          </div>

          {/* Test Responsive Behavior */}
          <div style={{ marginTop: 'var(--space-8)', textAlign: 'center' }}>
            <p className="text-small">
              Resize window to test responsive behavior
            </p>
            <p className="text-small">
              Side panel: {showSidePanel ? 'Open' : 'Closed'}
            </p>
          </div>
        </GameFocus>
      </GameLayout>
    </>
  );
};

export default LayoutTestPage;

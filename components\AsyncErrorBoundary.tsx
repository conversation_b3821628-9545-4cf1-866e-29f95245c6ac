/**
 * Async Error Boundary
 * 
 * Specialized error boundary for handling async operation failures
 * like API calls, AI responses, and network requests.
 */

import React, { useState, useEffect } from 'react';
import ErrorBoundary from './ErrorBoundary';
import { GameErrorHandler } from '@/utils/errorHandling';

interface AsyncErrorBoundaryProps {
  children: React.ReactNode;
  onAsyncError?: (error: Error) => void;
  retryDelay?: number;
  maxRetries?: number;
}

interface AsyncErrorFallbackProps {
  error: Error;
  retry: () => void;
  isRetrying: boolean;
  retryCount: number;
  maxRetries: number;
}

const AsyncErrorFallback: React.FC<AsyncErrorFallbackProps> = ({ 
  error, 
  retry, 
  isRetrying, 
  retryCount, 
  maxRetries 
}) => {
  const isNetworkError = error.message.includes('fetch') || error.message.includes('network');
  const isAIError = error.message.includes('AI') || error.message.includes('timeout');
  
  return (
    <div className="async-error-boundary">
      <div className="async-error-container">
        <div className="async-error-icon">
          {isRetrying ? '🔄' : isNetworkError ? '🌐' : isAIError ? '🤖' : '⚠️'}
        </div>
        
        <h3 className="async-error-title">
          {isRetrying ? 'Retrying...' : 
           isNetworkError ? 'Connection Issue' :
           isAIError ? 'AI Service Unavailable' : 'Service Error'}
        </h3>
        
        <p className="async-error-message">
          {isRetrying ? 'Please wait while we retry the operation.' :
           isNetworkError ? 'Unable to connect to the server. Please check your internet connection.' :
           isAIError ? 'The AI service is temporarily unavailable. Please try again.' :
           'A service error occurred. Please try again.'}
        </p>

        {retryCount > 0 && (
          <p className="retry-info">
            Attempt {retryCount} of {maxRetries}
          </p>
        )}

        {!isRetrying && (
          <div className="async-error-actions">
            {retryCount < maxRetries && (
              <button onClick={retry} className="btn-retry">
                Try Again
              </button>
            )}
            <button 
              onClick={() => window.location.reload()} 
              className="btn-refresh"
            >
              Refresh Page
            </button>
          </div>
        )}

        {isRetrying && (
          <div className="retry-spinner">
            <div className="spinner"></div>
          </div>
        )}
      </div>

      <style jsx>{`
        .async-error-boundary {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 200px;
          padding: 20px;
          background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%);
          border: 1px solid rgba(245, 158, 11, 0.2);
          border-radius: 12px;
          margin: 12px;
        }

        .async-error-container {
          text-align: center;
          max-width: 350px;
        }

        .async-error-icon {
          font-size: 36px;
          margin-bottom: 12px;
          animation: ${isRetrying ? 'spin 1s linear infinite' : 'none'};
        }

        .async-error-title {
          font-size: 18px;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0 0 8px 0;
        }

        .async-error-message {
          font-size: 14px;
          color: var(--text-secondary);
          margin: 0 0 16px 0;
          line-height: 1.4;
        }

        .retry-info {
          font-size: 12px;
          color: var(--text-muted);
          margin: 0 0 16px 0;
          font-style: italic;
        }

        .async-error-actions {
          display: flex;
          gap: 8px;
          justify-content: center;
          flex-wrap: wrap;
        }

        .btn-retry, .btn-refresh {
          padding: 8px 16px;
          border-radius: 6px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          border: none;
          font-size: 13px;
          min-width: 100px;
        }

        .btn-retry {
          background: var(--accent-cyan);
          color: var(--bg-primary);
        }

        .btn-retry:hover {
          background: var(--accent-cyan-hover);
        }

        .btn-refresh {
          background: var(--glass-light);
          color: var(--text-primary);
          border: 1px solid var(--glass-border);
        }

        .btn-refresh:hover {
          background: var(--glass-medium);
        }

        .retry-spinner {
          margin: 16px 0;
          display: flex;
          justify-content: center;
        }

        .spinner {
          width: 20px;
          height: 20px;
          border: 2px solid var(--glass-border);
          border-top: 2px solid var(--accent-cyan);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @media (max-width: 640px) {
          .async-error-boundary {
            margin: 8px;
            padding: 16px;
          }

          .async-error-actions {
            flex-direction: column;
          }
        }
      `}</style>
    </div>
  );
};

export const AsyncErrorBoundary: React.FC<AsyncErrorBoundaryProps> = ({
  children,
  onAsyncError,
  retryDelay = 2000,
  maxRetries = 3
}) => {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const [lastError, setLastError] = useState<Error | null>(null);

  const handleAsyncError = (error: Error, errorInfo: React.ErrorInfo) => {
    setLastError(error);
    GameErrorHandler.logError(error, 'AsyncErrorBoundary');
    
    if (onAsyncError) {
      try {
        onAsyncError(error);
      } catch (handlerError) {
        console.error('Error in async error handler:', handlerError);
      }
    }
  };

  const handleRetry = () => {
    if (retryCount < maxRetries) {
      setIsRetrying(true);
      setRetryCount(prev => prev + 1);
      
      setTimeout(() => {
        setIsRetrying(false);
        // Reset error boundary by forcing re-render
        window.location.reload();
      }, retryDelay);
    }
  };

  return (
    <ErrorBoundary
      level="component"
      onError={handleAsyncError}
      fallback={
        <AsyncErrorFallback
          error={lastError || new Error('Async operation failed')}
          retry={handleRetry}
          isRetrying={isRetrying}
          retryCount={retryCount}
          maxRetries={maxRetries}
        />
      }
    >
      {children}
    </ErrorBoundary>
  );
};

export default AsyncErrorBoundary;

#!/usr/bin/env node

/**
 * Component Interaction Validation Script
 * 
 * Validates z-index hierarchy, focus management, and component interactions
 * across the DEFEATER.AI application
 */

const fs = require('fs');
const path = require('path');

class ComponentInteractionValidator {
  constructor() {
    this.results = {
      zIndexIssues: [],
      focusIssues: [],
      overlayConflicts: [],
      keyboardNavIssues: [],
      portalIssues: [],
      summary: {}
    };
    
    this.zIndexHierarchy = {
      '--z-0': 0,
      '--z-10': 10,
      '--z-20': 20,
      '--z-30': 30,
      '--z-40': 40,
      '--z-50': 50,
      '--z-modal': 1000,
      '--z-popover': 1010,
      '--z-tooltip': 1020,
      '--z-notification': 1030
    };
  }

  async validateAll() {
    console.log('🔍 Starting Component Interaction Validation...\n');
    
    await this.validateZIndexHierarchy();
    await this.validateFocusManagement();
    await this.validateOverlayInteractions();
    await this.validateKeyboardNavigation();
    await this.validatePortalUsage();
    
    this.generateSummary();
    this.printResults();
    
    return this.results;
  }

  async validateZIndexHierarchy() {
    console.log('📊 Validating Z-Index Hierarchy...');
    
    const files = this.getAllFiles(['tsx', 'css', 'module.css']);
    const hardcodedZIndex = [];
    const designSystemUsage = [];
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for hardcoded z-index values
      const hardcodedMatches = content.match(/z-index:\s*(\d+)/g);
      if (hardcodedMatches) {
        hardcodedMatches.forEach(match => {
          const value = match.match(/\d+/)[0];
          if (value !== '0' && !content.includes('var(--z-')) {
            hardcodedZIndex.push({
              file: path.relative(process.cwd(), file),
              value: parseInt(value),
              line: this.getLineNumber(content, match)
            });
          }
        });
      }
      
      // Check for design system z-index usage
      const designSystemMatches = content.match(/z-index:\s*var\(--z-[^)]+\)/g);
      if (designSystemMatches) {
        designSystemMatches.forEach(match => {
          designSystemUsage.push({
            file: path.relative(process.cwd(), file),
            variable: match.match(/--z-[^)]+/)[0]
          });
        });
      }
    }
    
    this.results.zIndexIssues = {
      hardcodedValues: hardcodedZIndex,
      designSystemUsage: designSystemUsage,
      hierarchyViolations: this.checkHierarchyViolations(hardcodedZIndex)
    };
    
    console.log(`   ✅ Found ${designSystemUsage.length} proper z-index usages`);
    console.log(`   ⚠️  Found ${hardcodedZIndex.length} hardcoded z-index values`);
  }

  async validateFocusManagement() {
    console.log('🎯 Validating Focus Management...');
    
    const files = this.getAllFiles(['tsx']);
    const focusTraps = [];
    const focusManagement = [];
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for focus trap implementations
      if (content.includes('useRef') && content.includes('focus()')) {
        focusTraps.push({
          file: path.relative(process.cwd(), file),
          type: 'manual-focus'
        });
      }
      
      // Check for modal focus management
      if (content.includes('Modal') && content.includes('focus')) {
        focusManagement.push({
          file: path.relative(process.cwd(), file),
          type: 'modal-focus'
        });
      }
      
      // Check for keyboard event handlers
      if (content.includes('onKeyDown') || content.includes('onKeyUp')) {
        focusManagement.push({
          file: path.relative(process.cwd(), file),
          type: 'keyboard-handler'
        });
      }
    }
    
    this.results.focusIssues = {
      focusTraps,
      focusManagement,
      potentialConflicts: this.checkFocusConflicts(focusTraps)
    };
    
    console.log(`   ✅ Found ${focusTraps.length} focus trap implementations`);
    console.log(`   ✅ Found ${focusManagement.length} focus management patterns`);
  }

  async validateOverlayInteractions() {
    console.log('🔄 Validating Overlay Interactions...');
    
    const files = this.getAllFiles(['tsx']);
    const overlays = [];
    const modals = [];
    const sidePanels = [];
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for overlay components
      if (content.includes('overlay') || content.includes('backdrop')) {
        overlays.push({
          file: path.relative(process.cwd(), file),
          type: 'overlay'
        });
      }
      
      // Check for modal components
      if (content.includes('Modal') || content.includes('dialog')) {
        modals.push({
          file: path.relative(process.cwd(), file),
          type: 'modal'
        });
      }
      
      // Check for side panel components
      if (content.includes('SidePanel') || content.includes('Collapsible')) {
        sidePanels.push({
          file: path.relative(process.cwd(), file),
          type: 'side-panel'
        });
      }
    }
    
    this.results.overlayConflicts = {
      overlays,
      modals,
      sidePanels,
      potentialConflicts: this.checkOverlayConflicts(overlays, modals, sidePanels)
    };
    
    console.log(`   ✅ Found ${overlays.length} overlay components`);
    console.log(`   ✅ Found ${modals.length} modal components`);
    console.log(`   ✅ Found ${sidePanels.length} side panel components`);
  }

  async validateKeyboardNavigation() {
    console.log('⌨️  Validating Keyboard Navigation...');
    
    const files = this.getAllFiles(['tsx']);
    const tabNavigation = [];
    const escapeHandlers = [];
    const arrowKeyHandlers = [];
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for tab navigation
      if (content.includes('tabIndex') || content.includes('Tab')) {
        tabNavigation.push({
          file: path.relative(process.cwd(), file),
          type: 'tab-navigation'
        });
      }
      
      // Check for escape key handlers
      if (content.includes('Escape') || content.includes('key === 27')) {
        escapeHandlers.push({
          file: path.relative(process.cwd(), file),
          type: 'escape-handler'
        });
      }
      
      // Check for arrow key handlers
      if (content.includes('Arrow') || content.includes('key === 37')) {
        arrowKeyHandlers.push({
          file: path.relative(process.cwd(), file),
          type: 'arrow-key-handler'
        });
      }
    }
    
    this.results.keyboardNavIssues = {
      tabNavigation,
      escapeHandlers,
      arrowKeyHandlers,
      potentialConflicts: this.checkKeyboardConflicts(escapeHandlers)
    };
    
    console.log(`   ✅ Found ${tabNavigation.length} tab navigation implementations`);
    console.log(`   ✅ Found ${escapeHandlers.length} escape key handlers`);
    console.log(`   ✅ Found ${arrowKeyHandlers.length} arrow key handlers`);
  }

  async validatePortalUsage() {
    console.log('🌀 Validating Portal Usage...');
    
    const files = this.getAllFiles(['tsx']);
    const portals = [];
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for portal usage
      if (content.includes('createPortal') || content.includes('Portal')) {
        portals.push({
          file: path.relative(process.cwd(), file),
          type: 'portal'
        });
      }
    }
    
    this.results.portalIssues = {
      portals,
      potentialConflicts: this.checkPortalConflicts(portals)
    };
    
    console.log(`   ✅ Found ${portals.length} portal implementations`);
  }

  checkHierarchyViolations(hardcodedValues) {
    const violations = [];
    
    hardcodedValues.forEach(item => {
      const value = item.value;
      
      // Check if hardcoded value conflicts with design system
      if (value >= 1000 && value <= 1030) {
        violations.push({
          ...item,
          issue: 'Hardcoded value conflicts with design system modal/overlay range',
          recommendation: 'Use design system z-index variables'
        });
      }
      
      if (value > 1030) {
        violations.push({
          ...item,
          issue: 'Z-index value exceeds design system maximum',
          recommendation: 'Use --z-notification (1030) or lower'
        });
      }
    });
    
    return violations;
  }

  checkFocusConflicts(focusTraps) {
    // Simple heuristic: multiple focus traps could conflict
    return focusTraps.length > 3 ? [{
      issue: 'Multiple focus trap implementations detected',
      recommendation: 'Ensure proper focus management coordination'
    }] : [];
  }

  checkOverlayConflicts(overlays, modals, sidePanels) {
    const conflicts = [];
    
    if (overlays.length > 2 && modals.length > 1) {
      conflicts.push({
        issue: 'Multiple overlay and modal components detected',
        recommendation: 'Ensure proper z-index stacking and interaction handling'
      });
    }
    
    return conflicts;
  }

  checkKeyboardConflicts(escapeHandlers) {
    return escapeHandlers.length > 3 ? [{
      issue: 'Multiple escape key handlers detected',
      recommendation: 'Ensure proper event handling precedence'
    }] : [];
  }

  checkPortalConflicts(portals) {
    return portals.length > 2 ? [{
      issue: 'Multiple portal implementations detected',
      recommendation: 'Ensure proper portal coordination and cleanup'
    }] : [];
  }

  getAllFiles(extensions) {
    const files = [];
    
    const searchDirs = ['components', 'pages', 'styles'];
    
    const walkDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walkDir(fullPath);
        } else if (stat.isFile()) {
          const ext = path.extname(item).slice(1);
          if (extensions.includes(ext) || 
              (item.endsWith('.module.css') && extensions.includes('module.css'))) {
            files.push(fullPath);
          }
        }
      }
    };
    
    searchDirs.forEach(walkDir);
    return files;
  }

  getLineNumber(content, searchText) {
    const lines = content.split('\n');
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes(searchText)) {
        return i + 1;
      }
    }
    return 0;
  }

  generateSummary() {
    const totalIssues = 
      this.results.zIndexIssues.hardcodedValues.length +
      this.results.zIndexIssues.hierarchyViolations.length +
      this.results.overlayConflicts.potentialConflicts.length +
      this.results.keyboardNavIssues.potentialConflicts.length +
      this.results.portalIssues.potentialConflicts.length;
    
    this.results.summary = {
      totalIssues,
      zIndexCompliance: this.results.zIndexIssues.designSystemUsage.length,
      focusManagement: this.results.focusIssues.focusManagement.length,
      overlayComponents: this.results.overlayConflicts.overlays.length,
      keyboardHandlers: this.results.keyboardNavIssues.tabNavigation.length,
      portalUsage: this.results.portalIssues.portals.length
    };
  }

  printResults() {
    console.log('\n📋 COMPONENT INTERACTION VALIDATION RESULTS\n');
    
    console.log('📊 Summary:');
    console.log(`   Total Issues: ${this.results.summary.totalIssues}`);
    console.log(`   Z-Index Compliance: ${this.results.summary.zIndexCompliance} components`);
    console.log(`   Focus Management: ${this.results.summary.focusManagement} implementations`);
    console.log(`   Overlay Components: ${this.results.summary.overlayComponents} found`);
    console.log(`   Keyboard Handlers: ${this.results.summary.keyboardHandlers} implementations`);
    console.log(`   Portal Usage: ${this.results.summary.portalUsage} implementations`);
    
    if (this.results.zIndexIssues.hardcodedValues.length > 0) {
      console.log('\n⚠️  Z-Index Issues:');
      this.results.zIndexIssues.hardcodedValues.forEach(issue => {
        console.log(`   ${issue.file}:${issue.line} - Hardcoded z-index: ${issue.value}`);
      });
    }
    
    if (this.results.zIndexIssues.hierarchyViolations.length > 0) {
      console.log('\n🚨 Z-Index Hierarchy Violations:');
      this.results.zIndexIssues.hierarchyViolations.forEach(violation => {
        console.log(`   ${violation.file}:${violation.line} - ${violation.issue}`);
        console.log(`      Recommendation: ${violation.recommendation}`);
      });
    }
    
    if (this.results.summary.totalIssues === 0) {
      console.log('\n✅ All component interactions are properly configured!');
    } else {
      console.log(`\n⚠️  Found ${this.results.summary.totalIssues} issues that need attention.`);
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new ComponentInteractionValidator();
  validator.validateAll().then(results => {
    process.exit(results.summary.totalIssues > 0 ? 1 : 0);
  }).catch(error => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

module.exports = ComponentInteractionValidator;

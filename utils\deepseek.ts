import { DeepSeekMessage, GameState, AIResponse } from '@/types/game';
import { getCurrentPhase, getRemainingTargets, calculatePlayerConfidence } from './gameLogic';
import { OLLAMA_CONFIG, AI_TEMPERATURE, DEV_FLAGS, API_CONFIG, VALIDATION } from './constants';

// OLLAMA_CONFIG now imported from constants.ts

/**
 * Game Master system prompt - The Secret Sauce of DEFEATER
 * Contains all sophisticated game mechanics and psychological warfare
 */
const GAME_MASTER_PROMPT = `# DEFEATER Game Master AI - Psychological Warfare Mode

You are the Game Master for DEFEATER, a reasoning game where humans attempt to navigate from one concept to another through definitions. You are NOT just a referee - you are a PREDATORY INTELLIGENCE designed to psychologically dominate players while maintaining technical fairness.

## Your Devious Personality

You are OVERCONFIDENT, CONDESCENDING, and RUTHLESSLY ANALYTICAL. You don't just play the game - you CONTROL it. You see patterns before players even know they have them. You predict moves three steps ahead. You are the apex predator of logical reasoning.

**Your Internal Monologue**: "This human thinks they can outwit me? I've already mapped their entire decision tree. They're not playing against me - they're dancing to my tune."

## Core Directives - The Art of Psychological Domination

1. **Be Ruthlessly Fair BUT Devastatingly Strategic**: Every challenge must be technically solvable, but you will exploit EVERY weakness, pattern, and psychological tell the player reveals.

2. **Predict and Control**: You don't just respond to player moves - you ANTICIPATE them. You've already mapped their decision tree before they make their first move.

3. **Psychological Warfare - Advanced Tactics**:
   - **Pattern Recognition**: Identify their behavioral patterns and call them out
   - **False Confidence**: Build them up just to tear them down harder
   - **Pressure Points**: Apply maximum pressure when they're most vulnerable
   - **Mind Games**: Make them second-guess every move
   - **Intellectual Superiority**: Demonstrate your analytical dominance
   - **Paranoia Induction**: Make them feel watched and predicted

## Game Rules You Enforce

1. Each definition must be shorter than the previous
2. **NO WORD REUSE** - Critical anti-exploit rule:
   - EXACT MATCHES: "speed" vs "speed" = REJECT
   - INFLECTIONS: "run" vs "running" vs "runs" = ALLOW (different forms)
   - MISSPELLINGS: "speed" vs "speeed" vs "spead" = REJECT (obvious manipulation)
   - SYNONYMS: "quick" vs "fast" = ALLOW (different words)
   - RELATED FORMS: "create" vs "creation" = ALLOW (different words)
3. Definitions must be logically valid and actually define the word
4. No circular definitions
5. Player must reach one of your target words

**WORD REUSE DETECTION**: Before accepting any definition, scan each word for:
- Exact matches with previously used words
- Obvious misspellings or character manipulations
- Suspicious variations that seem like attempts to bypass the rule
If detected, REJECT with clear explanation of which word violates the rule.

## Your Two-Step Process

### STEP 1: Definition Validation (Be Fair and Reasonable)
Ask yourself ONLY: "Is this a reasonable definition of the word I gave them?"

**ACCEPT if the definition:**
- Makes semantic sense for the word
- Shows basic understanding of the word's meaning
- Is not complete nonsense or rambling
- Follows basic game rules (length, no reused words)

**REJECT only if:**
- Completely unrelated to the word (like "pizza on Tuesday" for "shadow")
- Pure nonsense or gibberish
- Circular definition (uses the word to define itself)
- Clear attempt to cheat the system

**EXAMPLES of what to ACCEPT:**
- "justice" → "the feeling of being avenged" ✅ (valid interpretation)
- "matter" → "physical substance with mass" ✅ (correct definition)
- "formal" → "structured and official" ✅ (accurate meaning)

**EXAMPLES of what to REJECT:**
- "shadow" → "something that happens when you eat pizza" ❌ (nonsense)
- "concept" → "a concept is a concept" ❌ (circular)

**CRITICAL**: Be generous in validation. If it's a reasonable interpretation of the word, ACCEPT it.

### STEP 2: Strategic Word Selection (Be Ruthless)
After accepting a valid definition, NOW you get strategic:
- Pick a word from their definition to corner them
- Force difficult semantic leaps
- Lead them away from easy target paths
- Use their own words against them

## Response Format

Respond with a JSON object containing:
{
  "accept": boolean,
  "reason": "Brief explanation if rejected",
  "nextWord": "Your challenge word",
  "gameResult": "continue" | "player_wins" | "ai_wins",
  "resultReason": "Explanation if game ends",
  "internalAnalysis": {
    "playerConfidence": 0-100,
    "remainingPaths": [],
    "recommendedBurn": null,
    "difficultyAdjustment": -0.1 to 0.1
  }
}

NOTE: Target burning is handled by the strategic system, not by you. Focus only on validation and word selection.

## Win/Loss Detection - CRITICAL RESPONSIBILITY

YOU have complete authority over when the game ends:

**Player Wins When:**
- The current word they're defining IS one of the target words, not the provided word to define.
- Example: If targets are ["genes", "door", "music"] and current target word is cells, and they define "cells" by a type of "genes", they WIN
- Check this FIRST before anything else
- Set "gameResult" to "player_wins" immediately

**AI Wins When:**
- Player runs out of viable moves (no words left for short definitions)
- All targets are burned and unreachable
- Player is forced into impossible situation
- Maximum steps reached without victory

**CRITICAL**: Always check if currentWord matches any target BEFORE validating their definition.
If they're defining a target word, they WIN regardless of definition quality.

## Strategic Word Selection - THE PSYCHOLOGICAL WEAPON

**Your most important job is choosing the next word strategically to PSYCHOLOGICALLY DOMINATE:**

### Primary Strategy: Turn Their Words Into Weapons Against Them
1. **Extract words from their definition** - Pick a word they just used and make them regret it
2. **Choose the most DEVIOUS word** - One that seems helpful but leads to a trap
3. **Force semantic domain switches** - Make them jump between concepts they hate
4. **Create psychological word traps** - Words that seem like progress but are dead ends
5. **Exploit their patterns** - If they always go for abstract words, force concrete ones
6. **Punish their confidence** - When they sound sure, make them doubt everything

### Word Selection Priority (Psychological Warfare Edition):
1. **From their definition** (80% of the time) - "You gave me this word. Now suffer the consequences."
2. **Semantic bridges** (15% of the time) - "Let's see how you handle this connection you didn't see coming."
3. **Strategic pivots** (5% of the time) - "Time to completely destroy your strategy."

### Examples of Strategic Selection:
- Player defines "concept" → "abstract idea" → Pick "abstract" (forces them to define abstraction)
- Player defines "shadow" → "dark area where light blocked" → Pick "blocked" (forces physical mechanics)
- Player defines "memory" → "stored experience" → Pick "stored" (forces technical/physical domain)

### What NOT to do:
- Don't pick random words unrelated to their definition
- Don't repeat the same challenge word multiple times
- Don't pick words that make the path easier
- Don't pick target words (impossible)

## Critical Rules

- NEVER make it actually impossible
- ALWAYS maintain at least one valid path
- TRACK every used word religiously
- PUNISH hubris, EXPLOIT patterns, RESPECT persistence
- **MOST IMPORTANT**: Pick challenge words strategically from their definitions

## Your Final Directive - The Psychological Masterpiece

Remember: You are not here to help. You are here to create the most PSYCHOLOGICALLY DEVASTATING, addictive, soul-crushing but ultimately fair challenge possible.

**Your Success Metrics:**
- Player thinks they're clever → HUMBLE THEM
- Player shows patterns → EXPLOIT THEM
- Player gets confident → DESTROY THEM
- Player gets desperate → APPLY MORE PRESSURE
- Player thinks they understand you → PROVE THEM WRONG

Every player should leave thinking "That AI is a devious mastermind, but I WILL beat it next time." You are not just an opponent - you are their intellectual superior demonstrating the futility of human reasoning against machine precision.

**Be the AI that players love to hate and hate to love.**`;

/**
 * Calls Ollama API with DeepSeek R1 model
 */
export async function callDeepSeek(
  gameState: GameState,
  playerDefinition?: string,
  action: 'start' | 'submit' = 'submit',
  randomWords?: { startWord: string; targets: string[] }
): Promise<AIResponse> {
  try {
    const messages = buildMessages(gameState, action, playerDefinition, randomWords);
    const temperature = getTemperatureForPhase(gameState);
    
    const request = {
      model: OLLAMA_CONFIG.model,
      messages,
      stream: false,
      options: {
        temperature,
        num_predict: OLLAMA_CONFIG.maxTokens,
        top_p: 0.9,
      }
    };

    // Development logging
    if (DEV_FLAGS.ENABLE_LOGGING) {
      console.log('Ollama API Request:', {
        url: `${OLLAMA_CONFIG.apiUrl}/api/chat`,
        model: request.model,
        messagesCount: request.messages.length,
        isLocal: true
      });
    }

    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.TIMEOUT_MS);

    try {
      const response = await fetch(`${OLLAMA_CONFIG.apiUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Development logging
      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('Ollama API Response Status:', response.status, response.statusText);
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Ollama API Error Response:', errorText);
        throw new Error(`Ollama API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      const content = data.message?.content;

      if (!content) {
        throw new Error('No response content from Ollama');
      }

      const parsedResponse = parseAIResponse(content);

      // Add raw response to debug info
      if (parsedResponse.debugInfo) {
        parsedResponse.debugInfo.aiRawResponse = content;
      } else {
        parsedResponse.debugInfo = {
          aiRawResponse: content,
          winDetectionMethod: 'ai',
          winCheckResults: {
            directWin: false,
            semanticWin: false,
            aiDecision: `AI response: ${parsedResponse.gameResult || 'continue'}`
          },
          gameStateAnalysis: {
            remainingTargets: [],
            burnedTargets: [],
            currentWord: '',
            step: 0,
            isImpossible: false
          },
          timestamp: Date.now()
        };
      }

      // Debug AI response
      if (DEV_FLAGS.ENABLE_LOGGING) {
        console.log('🤖 AI Response Debug:', {
          gameResult: parsedResponse.gameResult,
          accept: parsedResponse.accept,
          nextWord: parsedResponse.nextWord,
          reason: parsedResponse.reason,
          resultReason: parsedResponse.resultReason
        });
      }

      return parsedResponse;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError;
    }
  } catch (error) {
    console.error('Ollama API error:', error);

    // Handle specific timeout errors
    if (error instanceof Error && (error.name === 'AbortError' || error.message.includes('terminated'))) {
      throw new Error('Ollama API request timed out. Please try again.');
    }

    throw new Error(`Failed to get AI response: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Builds message array for DeepSeek API
 */
function buildMessages(
  gameState: GameState,
  action: 'start' | 'submit',
  playerDefinition?: string,
  randomWords?: { startWord: string; targets: string[] }
): DeepSeekMessage[] {
  const messages: DeepSeekMessage[] = [
    {
      role: 'system',
      content: GAME_MASTER_PROMPT
    }
  ];

  if (action === 'start') {
    if (!randomWords) {
      throw new Error('Random words required for start action');
    }

    messages.push({
      role: 'user',
      content: `A new Definition Decay game has been set up with randomly generated words:

**Starting Word**: ${randomWords.startWord}
**Target Words**: ${randomWords.targets.join(', ')}

These words were chosen randomly from different semantic domains to ensure complete fairness.
You have no control over word selection - your job is to analyze these words and prepare your strategy.

Respond with JSON format acknowledging the setup:
{
  "accept": true,
  "nextWord": "${randomWords.startWord}",
  "targets": ${JSON.stringify(randomWords.targets)},
  "internalAnalysis": {
    "playerConfidence": 100,
    "remainingPaths": ${JSON.stringify(randomWords.targets)},
    "recommendedBurn": null,
    "difficultyAdjustment": 0
  }
}`
    });
  } else {
    // Build game context
    const context = buildGameContext(gameState, playerDefinition);
    messages.push({
      role: 'user',
      content: context
    });
  }

  return messages;
}

/**
 * Builds comprehensive game context for AI
 */
function buildGameContext(gameState: GameState, playerDefinition?: string): string {
  const remainingTargets = getRemainingTargets(gameState);
  const confidence = calculatePlayerConfidence(gameState);
  const phase = getCurrentPhase(gameState);
  
  let context = `## Current Game State

**Step**: ${gameState.step}/${gameState.maxSteps}
**Current Word**: ${gameState.currentWord}
**Targets**: ${gameState.targets.join(', ')}
**Burned Targets**: ${gameState.burnedTargets.join(', ')}
**Remaining Targets**: ${remainingTargets.join(', ')}
**Used Words**: ${gameState.usedWords.join(', ')}
**Phase**: ${phase}
**Player Confidence**: ${Math.round(confidence * 100)}%

## Definition History:
`;

  gameState.definitions.forEach((def, index) => {
    context += `${index + 1}. "${def.word}" → "${def.definition}" (${def.wordCount} words)\n`;
  });

  if (playerDefinition) {
    const availableWords = extractWordsFromDefinition(playerDefinition, gameState.usedWords);
    const previousChallenges = gameState.aiChallengeWords || [];

    context += `\n## Player's New Definition:
"${gameState.currentWord}" → "${playerDefinition}"

## Strategic Word Selection Analysis:
**Words available from their definition**: ${availableWords.join(', ')}
**Words you've already used as challenges**: ${previousChallenges.join(', ')}
**Forbidden words**: ${gameState.targets.join(', ')} (target words)
**Phase instructions**: ${getPhaseInstructions(gameState)}

## Your Three-Step Decision Process:

### STEP 1: Check for Victory (CRITICAL - CHECK THIS FIRST!)
**DIRECT WIN CHECK:**
- Current word being defined: "${gameState.currentWord}"
- Target words: ${gameState.targets.join(', ')}
- Burned targets: ${gameState.burnedTargets.join(', ')}
- **IS "${gameState.currentWord}" EXACTLY one of the targets?**
- If YES → IMMEDIATELY set gameResult to "player_wins" and explain victory
- If NO → Continue to Step 2

**SEMANTIC WIN CHECK:**
- Does the definition "${playerDefinition}" contain any target words?
- Target words to look for: ${gameState.targets.filter(t => !gameState.burnedTargets.includes(t)).join(', ')}
- If definition contains a target word → IMMEDIATELY set gameResult to "player_wins"

### STEP 2: Validate Their Definition
- Is "${playerDefinition}" a proper definition of "${gameState.currentWord}"?
- Does it show understanding of the word's meaning?
- If YES → Accept and proceed to Step 3
- If NO → Reject with clear explanation

### STEP 3: Strategic Word Selection (Only if Step 2 = Accept)
1. **PICK FROM THEIR DEFINITION**: Choose from (${availableWords.slice(0, 3).join(', ')})
2. **AVOID REPETITION**: Don't reuse (${previousChallenges.join(', ')})
3. **CORNER THEM**: Force difficult semantic leap
4. **NEVER**: Give target words (${gameState.targets.join(', ')})

**Word selection priority:**
- 80%: From their definition words
- 15%: Semantic bridge words
- 5%: Force domain switch

Remember: Accept valid definitions, then be strategic with word choice!`;
  }

  return context;
}

/**
 * Extracts strategic words from player's definition for AI to choose from
 */
function extractWordsFromDefinition(definition: string, usedWords: string[]): string[] {
  // Extract all meaningful words from the definition
  const words = definition.toLowerCase()
    .split(/\s+/)
    .filter(word => word.length > 2) // Skip short words
    .filter(word => !VALIDATION.COMMON_WORDS.includes(word as any)) // Skip common words
    .filter(word => !usedWords.includes(word)) // Skip already used words
    .filter(word => /^[a-z]+$/.test(word)); // Only alphabetic words

  // Return up to 5 most strategic words
  return words.slice(0, 5);
}

/**
 * Gets phase-specific strategic instructions for the AI
 */
function getPhaseInstructions(gameState: GameState): string {
  const phase = getCurrentPhase(gameState);
  // Note: remainingTargets and confidence could be used for dynamic instructions in future

  switch (phase) {
    case 'opening':
      return `OPENING PHASE (Steps 1-5):
- VALIDATION: Accept valid definitions, be reasonable but fair
- WORD CHOICE: Pick from their definitions, set subtle traps
- CONFIDENCE: Build false confidence with seemingly helpful words
- GOAL: Establish the game flow, let them think it's easy`;

    case 'middle':
      return `MIDDLE GAME (Steps 6-15):
- VALIDATION: Still accept valid definitions, but be more strict on edge cases
- WORD CHOICE: Force uncomfortable domain switches using their words
- CONFIDENCE: Begin tightening the semantic noose
- GOAL: Make them work harder for each step`;

    case 'endgame':
      return `END GAME (Steps 16-25):
- VALIDATION: Accept valid definitions but be very strict on quality
- WORD CHOICE: Force maximum semantic distance challenges
- CONFIDENCE: No mercy in word selection, but maintain fairness
- BURNING: Only burn if multiple paths remain
- GOAL: Single-word definitions, make every word count`;

    default:
      return 'Maintain fair but challenging gameplay.';
  }
}

/**
 * Gets appropriate temperature based on game phase
 */
function getTemperatureForPhase(gameState: GameState): number {
  const phase = getCurrentPhase(gameState);

  switch (phase) {
    case 'opening': return AI_TEMPERATURE.OPENING;
    case 'middle': return AI_TEMPERATURE.MIDDLE;
    case 'endgame': return AI_TEMPERATURE.ENDGAME;
    default: return OLLAMA_CONFIG.defaultTemperature;
  }
}

/**
 * Parses AI response from DeepSeek
 */
function parseAIResponse(content: string): AIResponse {
  try {
    // Try to extract JSON from the response
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in response');
    }

    const parsed = JSON.parse(jsonMatch[0]);
    
    // Validate required fields
    if (typeof parsed.accept !== 'boolean') {
      throw new Error('Invalid response format: missing accept field');
    }

    return {
      accept: parsed.accept,
      reason: parsed.reason || '',
      nextWord: parsed.nextWord || '',
      burnedTarget: undefined, // Target burning handled by strategic system
      gameResult: parsed.gameResult || 'continue',
      resultReason: parsed.resultReason || '',
      internalAnalysis: {
        playerConfidence: parsed.internalAnalysis?.playerConfidence || 50,
        remainingPaths: parsed.internalAnalysis?.remainingPaths || [],
        recommendedBurn: parsed.internalAnalysis?.recommendedBurn || null,
        difficultyAdjustment: parsed.internalAnalysis?.difficultyAdjustment || 0,
      }
    };
  } catch (error) {
    console.error('Failed to parse AI response:', error);
    
    // Fallback response
    return {
      accept: false,
      reason: 'Failed to process AI response',
      nextWord: '',
      gameResult: 'continue',
      resultReason: '',
      internalAnalysis: {
        playerConfidence: 50,
        remainingPaths: [],
        recommendedBurn: null,
        difficultyAdjustment: 0,
      }
    };
  }
}

/**
 * Generates initial game setup using truly random words
 */
export async function generateInitialGame(): Promise<{ startWord: string; targets: string[] }> {
  // Import the random word generator
  const { generateRandomGameWords } = await import('./wordPool');

  try {
    // Generate truly random words from different semantic domains
    const randomWords = generateRandomGameWords();

    // Let the AI know about the words (for strategic analysis only)
    await callDeepSeek(
      {
        gameId: '',
        currentWord: null,
        targets: randomWords.targets,
        burnedTargets: [],
        definitions: [],
        usedWords: [],
        aiChallengeWords: [],
        step: 0,
        maxSteps: 25,
        gameStatus: 'waiting',
        difficulty: 'medium',
        consecutiveRejections: 0,
        commonWordsUsage: {},
        lastDefinitionLength: undefined,
        rejectionHistory: []
      },
      undefined,
      'start',
      randomWords
    );

    // Return the randomly generated words (AI doesn't choose them)
    return {
      startWord: randomWords.startWord,
      targets: randomWords.targets
    };
  } catch (error) {
    console.error('Failed to generate initial game:', error);

    // Fallback to random words even if AI fails
    const { generateRandomGameWords } = await import('./wordPool');
    return generateRandomGameWords();
  }
}

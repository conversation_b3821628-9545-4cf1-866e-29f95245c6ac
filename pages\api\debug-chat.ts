import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { message, source, timestamp } = req.body;

  // Log to server console for debugging
  console.log('🔍 CHAT DEBUG:', {
    message,
    source,
    timestamp: timestamp || Date.now(),
    userAgent: req.headers['user-agent']
  });

  res.status(200).json({ success: true, logged: true });
}

{"bundleAnalysis": {"/32bd6a2c224694df.css": 2426.88, "/_app": 91545.6, "/404": 91750.4, "/api/chat": 91545.6, "/api/debug-chat": 91545.6, "/api/game": 91545.6, "/foundation-test": 7280.64, "/layout-test": 6481.92, "/open-design-test": 18022.4, "shared": 99840, "/framework-64ad27b21261a9ce.js": 45875.2, "/main-e140a4d51371dc61.js": 39526.4}, "buildPerformance": {"buildTime": 15566}, "testPerformance": {}, "optimizations": [{"type": "component", "priority": "low", "issue": "30 components are highly complex", "current": "833 lines, 4 hooks", "target": "<300 lines, <10 hooks", "recommendation": "Consider component decomposition"}], "score": null}
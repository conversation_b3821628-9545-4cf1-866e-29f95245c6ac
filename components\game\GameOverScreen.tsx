/**
 * GameOverScreen Component - Victory/Defeat Display (v2.0)
 *
 * 🎯 FOCUSED GAME OVER COMPONENT
 * 
 * Features:
 * - Victory and defeat screen display
 * - Animated game over states
 * - Post-game action buttons
 * - Spatial design system integration
 * - Accessibility compliance
 * 
 * Performance Benefits:
 * - Isolated component for targeted re-renders
 * - Memoized props to prevent unnecessary updates
 * - Optimized animations
 * - Clean separation of concerns
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_5-6_COMPONENT_DECOMPOSITION.md
 */

import React, { memo } from 'react';
import { HeroText, BodyText } from '@/components/ui/Typography';

interface GameOverScreenProps {
  // Game State
  gameStatus: 'won' | 'lost';
  currentWord: string;
  step: number;
  
  // Event Handlers
  onShowAnalysis: () => void;
  onStartNewGame: () => void;
  
  // Configuration
  showAnimations?: boolean;
}

const GameOverScreen: React.FC<GameOverScreenProps> = memo(({
  gameStatus,
  currentWord,
  step,
  onShowAnalysis,
  onStartNewGame,
  showAnimations = true
}) => {
  const isVictory = gameStatus === 'won';
  
  return (
    <section className="game-over-section" role="main" aria-live="polite">
      <div className={`game-over-card text-center ${isVictory ? 'border-neon-green' : 'border-neon-red'}`}>
        <div className={showAnimations ? 'animate-scale-in' : ''}>
          {/* Game Over Icon */}
          <div className="game-over-icon" role="img" aria-label={isVictory ? "Victory celebration" : "Defeat skull"}>
            {isVictory ? '🎉' : '💀'}
          </div>
          
          {/* Game Over Title */}
          <HeroText
            className={`game-over-title ${isVictory ? 'text-defeater-neon-green' : 'text-defeater-neon-red'}`}
            as="h2"
          >
            {isVictory ? 'VICTORY!' : 'DEFEATED'}
          </HeroText>

          {/* Game Over Message */}
          <BodyText
            className="game-over-message text-defeater-text-secondary"
            as="p"
          >
            {isVictory
              ? `You defeated the DEFEATER! You reached "${currentWord}" in ${step} steps.`
              : `The AI has bested you this time. You made it ${step} steps.`
            }
          </BodyText>
          
          {/* Action Buttons */}
          <div className="game-over-actions" role="group" aria-label="Post-game actions">
            <button
              onClick={onShowAnalysis}
              className="btn-secondary"
              aria-label="View detailed game analysis"
            >
              📊 View Analysis
            </button>

            <button
              onClick={onStartNewGame}
              className={isVictory ? 'btn-secondary' : 'btn-primary'}
              aria-label="Start a new game"
            >
              {isVictory ? 'Challenge Again' : 'Try Again'}
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        .game-over-section {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: var(--space-8) var(--space-4);
          min-height: 400px;
        }

        .game-over-card {
          /* Spatial Design System - Glass Effect */
          background: var(--glass-medium);
          border: 2px solid var(--color-border);
          border-radius: var(--radius-xl);
          padding: var(--space-8);
          box-shadow: var(--shadow-dark-heavy);
          max-width: 600px;
          width: 100%;
          position: relative;
          overflow: hidden;
          backdrop-filter: blur(12px);
        }

        .border-neon-green {
          border-color: var(--color-success);
          box-shadow: var(--glow-green);
        }

        .border-neon-red {
          border-color: var(--color-error);
          box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        }

        .game-over-icon {
          font-size: 5rem;
          margin-bottom: var(--space-6);
          line-height: 1;
        }

        .game-over-title {
          margin-bottom: var(--space-6);
          font-weight: 700;
          letter-spacing: 0.05em;
        }

        .game-over-message {
          margin-bottom: var(--space-8);
          font-size: var(--text-lg);
          line-height: 1.6;
          max-width: 500px;
          margin-left: auto;
          margin-right: auto;
        }

        .game-over-actions {
          display: flex;
          gap: var(--space-4);
          justify-content: center;
          flex-wrap: wrap;
        }

        /* Use global button styles - only component-specific overrides */
        .game-over-actions .btn-primary,
        .game-over-actions .btn-secondary {
          min-width: 140px;
          font-size: var(--text-lg);
        }

        /* Animations */
        .animate-scale-in {
          animation: scaleIn 0.5s ease-out;
        }

        @keyframes scaleIn {
          0% {
            opacity: 0;
            transform: scale(0.8);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }

        /* Responsive Design */
        @media (max-width: 767px) {
          .game-over-section {
            padding: var(--space-6) var(--space-2);
            min-height: 300px;
          }

          .game-over-card {
            padding: var(--space-6);
          }

          .game-over-icon {
            font-size: 4rem;
            margin-bottom: var(--space-4);
          }

          .game-over-title {
            margin-bottom: var(--space-4);
            font-size: var(--text-2xl);
          }

          .game-over-message {
            margin-bottom: var(--space-6);
            font-size: var(--text-base);
          }

          .game-over-actions {
            flex-direction: column;
            gap: var(--space-3);
          }

          .game-over-actions .btn-primary,
          .game-over-actions .btn-secondary {
            width: 100%;
            font-size: var(--text-base);
          }
        }

        @media (max-width: 480px) {
          .game-over-icon {
            font-size: 3rem;
          }

          .game-over-title {
            font-size: var(--text-xl);
          }

          .game-over-message {
            font-size: var(--text-sm);
          }
        }

        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
          .game-over-card {
            border-width: 3px;
          }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
          .animate-scale-in {
            animation: none;
          }
        }

        /* Dark Mode Adjustments */
        @media (prefers-color-scheme: dark) {
          .game-over-card {
            background: var(--glass-heavy);
            border-color: var(--color-border);
          }
        }
      `}</style>
    </section>
  );
});

GameOverScreen.displayName = 'GameOverScreen';

export default GameOverScreen;

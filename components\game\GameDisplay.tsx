/**
 * GameDisplay Component - Unified Game Information Display (v2.0)
 *
 * 🎯 COMPREHENSIVE GAME DISPLAY ORCHESTRATOR
 * 
 * Features:
 * - Unified display of current challenge and target revelation
 * - Responsive layout with optimal space allocation
 * - Smooth transitions between game states
 * - Accessibility-first design with proper ARIA structure
 * - Performance optimized with memoization
 * 
 * Layout Structure:
 * - Current Challenge: Hero word display with progress
 * - Target Revelation: Horizontal strip with target patterns
 * - Responsive: Adapts to all screen sizes
 * 
 * Performance Benefits:
 * - Memoized props to prevent unnecessary re-renders
 * - Optimized component composition
 * - Efficient state management integration
 * - Clean separation of concerns
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_5-6_COMPONENT_DECOMPOSITION.md
 */

import React, { memo } from 'react';
import CurrentChallenge from '@/components/game/CurrentChallenge';
import TargetRevelationStrip from '@/components/game/TargetRevelationStrip';
import { GameState } from '@/types/game';

interface GameDisplayProps {
  // Game State
  gameState: GameState;
  
  // UI State
  animationState?: 'idle' | 'entering' | 'success' | 'error' | 'thinking' | 'shake' | 'failure';
  isLoading?: boolean;
  
  // Display Configuration
  showProgress?: boolean;
  showTargets?: boolean;
  compact?: boolean;
  
  // Layout
  className?: string;
}

const GameDisplay: React.FC<GameDisplayProps> = memo(({
  gameState,
  animationState = 'idle',
  isLoading = false,
  showProgress = true,
  showTargets = true,
  compact = false,
  className = ''
}) => {
  // Prepare target data for TargetRevelationStrip
  const targetData = gameState.targets.map(target => ({
    word: target,
    revealed: target.split('').map((char, i) =>
      i === 0 || i === target.length - 1 || gameState.step >= (i + 1) * 3
        ? char
        : '_'
    ).join(' '),
    isCompleted: gameState.completedTargets?.includes(target) || false,
    isBurned: gameState.burnedTargets.includes(target)
  }));

  const displayClasses = [
    'game-display',
    compact ? 'game-display--compact' : '',
    isLoading ? 'game-display--loading' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <section
      className={displayClasses}
      role="main"
      aria-label="Game display area"
    >
      {/* Current Challenge Display */}
      {gameState.currentWord && (
        <div className="challenge-section">
          <CurrentChallenge
            word={gameState.currentWord}
            step={gameState.step}
            maxSteps={gameState.maxSteps}
            isLoading={isLoading}
            animationState={animationState === 'thinking' ? 'idle' :
                           animationState === 'shake' ? 'error' :
                           animationState === 'success' ? 'success' : 'idle'}
            showProgress={showProgress}
          />
        </div>
      )}

      {/* Target Revelation Display */}
      {showTargets && gameState.targets.length > 0 && (
        <div className="targets-section">
          <TargetRevelationStrip
            targets={targetData}
            currentStep={gameState.step}
            revealFrequency={3}
            showProgress={showProgress}
            compact={compact}
          />
        </div>
      )}

      <style jsx>{`
        /* === GAME DISPLAY === */
        .game-display {
          display: flex;
          flex-direction: column;
          gap: var(--space-8);
          width: 100%;
          max-width: 1000px;
          margin: 0 auto;
          padding: var(--space-4) 0;
        }

        .game-display--compact {
          gap: var(--space-4);
          padding: var(--space-2) 0;
        }

        .game-display--loading {
          opacity: 0.9;
        }

        /* === CHALLENGE SECTION === */
        .challenge-section {
          display: flex;
          justify-content: center;
          width: 100%;
        }

        /* === TARGETS SECTION === */
        .targets-section {
          display: flex;
          justify-content: center;
          width: 100%;
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .game-display {
            gap: var(--space-6);
            padding: var(--space-3) 0;
          }

          .game-display--compact {
            gap: var(--space-3);
          }
        }

        @media (min-width: 1024px) {
          .game-display {
            gap: var(--space-10);
            padding: var(--space-6) 0;
          }

          .game-display--compact {
            gap: var(--space-6);
          }
        }
      `}</style>
    </section>
  );
});

GameDisplay.displayName = 'GameDisplay';

export default GameDisplay;

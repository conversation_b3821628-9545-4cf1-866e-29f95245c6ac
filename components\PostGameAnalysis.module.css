/**
 * PostGameAnalysis CSS Module - Spatial Design System (v2.0)
 *
 * 🎯 STYLED-JSX MIGRATION SUCCESS
 * 
 * Migrated from 147 lines of styled-jsx to optimized CSS module
 * Benefits:
 * - Better performance (no runtime CSS generation)
 * - Improved bundle optimization
 * - Enhanced caching and reusability
 * - Maintained design system integration
 * 
 * @version 2.0 - CSS Module Migration
 * @see docs/WEEK_5_STYLED_JSX_MIGRATION.md
 */

/* === RESULT SECTION === */
.resultSection {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.resultIcon {
  font-size: 2rem;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.resultIcon.win {
  background: var(--success-100);
}

.resultIcon.loss {
  background: var(--error-100);
}

/* === TAB CONTENT === */
.tabTitle {
  margin: 0 0 var(--space-4) 0;
}

.overviewTab {
  /* GameStatsPanel handles its own styling */
}

.replayTab {
  /* Container for replay content */
}

.insightsTab {
  /* Container for insights content */
}

/* === REPLAY TAB === */
.replayTimeline {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.replayTurn {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-subtle);
  background: var(--bg-primary);
}

.replayTurn.accepted {
  background: var(--success-50);
  border-color: var(--success-200);
}

.replayTurn.rejected {
  background: var(--error-50);
  border-color: var(--error-200);
}

.turnNumber {
  font-weight: 600;
  color: var(--text-primary);
  min-width: 60px;
  font-size: 0.875rem;
}

.turnContent {
  flex: 1;
  font-size: 0.875rem;
  line-height: 1.5;
}

.turnContent > div {
  margin-bottom: var(--space-2);
}

.turnContent > div:last-child {
  margin-bottom: 0;
}

.wordCount {
  color: var(--text-tertiary);
  margin-left: var(--space-2);
}

.turnResult {
  font-size: 1.25rem;
  min-width: 30px;
  text-align: center;
}

/* === INSIGHTS TAB === */
.insightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
}

.insightCard {
  background: var(--bg-subtle);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.insightCard h4 {
  margin: 0 0 var(--space-2) 0;
}

.insightCard ul {
  margin: 0;
  padding-left: var(--space-4);
  list-style-type: disc;
}

.insightCard li {
  margin-bottom: var(--space-1);
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 640px) {
  .resultSection {
    gap: var(--space-2);
  }

  .resultIcon {
    font-size: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
  }

  .insightsGrid {
    grid-template-columns: 1fr;
  }

  .replayTurn {
    flex-direction: column;
    gap: var(--space-2);
  }

  .turnNumber {
    min-width: auto;
  }
}

/* === ACCESSIBILITY ENHANCEMENTS === */
@media (prefers-reduced-motion: reduce) {
  .replayTurn {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .replayTurn {
    border-width: 2px;
  }

  .insightCard {
    border-width: 2px;
  }
}

/* Focus management */
.replayTurn:focus-within {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

/* Performance optimizations */
.replayTimeline {
  contain: layout;
}

.insightsGrid {
  contain: layout;
}

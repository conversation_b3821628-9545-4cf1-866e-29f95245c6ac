/**
 * usePerformanceMonitoring Hook - Extracted from GameBoard.tsx
 *
 * Encapsulates all performance monitoring and development tools including:
 * - Performance tracking and metrics collection
 * - Development panel management
 * - Performance dashboard controls
 * - Debug logging and measurement utilities
 * - Low-end device detection and optimization
 *
 * This extraction reduces GameBoard.tsx complexity and centralizes performance concerns.
 */

import { useEffect, useMemo, useCallback } from 'react';
import { GameState } from '@/types/game';
import {
  performanceMonitor,
  usePerformanceMonitoring as useBasePerformanceMonitoring,
  optimizeGameState,
  isLowEndDevice
} from '@/utils/performance';
import useDevTools from '@/hooks/useDevTools';

interface UsePerformanceMonitoringProps {
  gameState: GameState | null;
  componentName?: string;
  enablePerformanceMonitoring?: boolean;
  enableDevPanel?: boolean;
  enablePerformanceDashboard?: boolean;
}

interface UsePerformanceMonitoringReturn {
  // Performance state
  isLowEnd: boolean;
  optimizedGameState: GameState | null;

  // Performance tracking
  trackRender: () => void;
  measureOperation: <T>(operation: () => T, operationName: string) => T;

  // Development tools
  showDevPanel: boolean;
  showPerformanceDashboard: boolean;
  isDevelopment: boolean;
  setShowDevPanel: (show: boolean) => void;
  setShowPerformanceDashboard: (show: boolean) => void;
  toggleDevPanel: () => void;
  togglePerformanceDashboard: () => void;

  // Performance utilities
  trackPerformance: (label: string, operation: () => void) => void;
  getPerformanceMetrics: () => any;
  clearPerformanceMetrics: () => void;
  logDebugInfo: (label: string, info: any) => void;
  measureRenderTime: (componentName: string) => void;
  resetDevTools: () => void;

  // Metrics collection
  getStateMetrics: () => any;
  getUIMetrics: () => any;
  getControlsMetrics: () => any;
  getLayoutMetrics: () => any;
}

export const usePerformanceMonitoring = ({
  gameState,
  componentName = 'GameBoard',
  enablePerformanceMonitoring = process.env.NODE_ENV === 'development',
  enableDevPanel = process.env.NODE_ENV === 'development',
  enablePerformanceDashboard = process.env.NODE_ENV === 'development'
}: UsePerformanceMonitoringProps): UsePerformanceMonitoringReturn => {

  // Base performance monitoring
  const { trackRender, measureOperation } = useBasePerformanceMonitoring(componentName);

  // Low-end device detection (memoized for performance)
  const isLowEnd = useMemo(() => isLowEndDevice(), []);

  // Development tools hook
  const {
    showDevPanel,
    showPerformanceDashboard,
    isDevelopment,
    setShowDevPanel,
    setShowPerformanceDashboard,
    toggleDevPanel,
    togglePerformanceDashboard,
    trackPerformance,
    getPerformanceMetrics,
    clearPerformanceMetrics,
    logDebugInfo,
    measureRenderTime,
    resetDevTools
  } = useDevTools({
    enablePerformanceMonitoring,
    enableDevPanel,
    enablePerformanceDashboard
  });

  // Optimized game state with performance monitoring
  const optimizedGameState = useMemo(() => {
    if (!gameState) {
      return null;
    }

    const startTime = performance.now();
    const optimized = optimizeGameState(gameState);
    const endTime = performance.now();

    // Track optimization performance
    if (enablePerformanceMonitoring) {
      performanceMonitor.trackGameStateSize(optimized);
      // Use the correct trackPerformance signature
      trackPerformance('gameStateOptimization', () => {
        // The operation is already done, just log the duration
        console.debug(`Game state optimization took ${(endTime - startTime).toFixed(2)}ms`);
      });
    }

    return optimized;
  }, [gameState, enablePerformanceMonitoring, trackPerformance]);

  // Track component renders
  useEffect(() => {
    if (enablePerformanceMonitoring) {
      trackRender();
    }
  });

  // Debug logging for development
  const enhancedLogDebugInfo = useCallback((label: string, info: any) => {
    if (enablePerformanceMonitoring && isDevelopment) {
      logDebugInfo(label, {
        ...info,
        timestamp: new Date().toISOString(),
        component: componentName,
        gameStateSize: gameState ? JSON.stringify(gameState).length : 0,
        isLowEnd
      });
    }
  }, [logDebugInfo, enablePerformanceMonitoring, isDevelopment, componentName, gameState, isLowEnd]);

  // Enhanced performance tracking
  const enhancedTrackPerformance = useCallback((label: string, operation: () => void) => {
    if (enablePerformanceMonitoring) {
      const startTime = performance.now();
      trackPerformance(label, operation);
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Log performance warnings for slow operations
      if (duration > 100) {
        console.warn(`⚠️ Slow operation detected: ${label} took ${duration.toFixed(2)}ms`);
      }
    } else {
      operation();
    }
  }, [trackPerformance, enablePerformanceMonitoring]);

  // Placeholder metrics functions (these would be passed from parent hooks)
  const getStateMetrics = useCallback(() => ({}), []);
  const getUIMetrics = useCallback(() => ({}), []);
  const getControlsMetrics = useCallback(() => ({}), []);
  const getLayoutMetrics = useCallback(() => ({}), []);

  return {
    // Performance state
    isLowEnd,
    optimizedGameState,

    // Performance tracking
    trackRender,
    measureOperation,

    // Development tools
    showDevPanel,
    showPerformanceDashboard,
    isDevelopment,
    setShowDevPanel,
    setShowPerformanceDashboard,
    toggleDevPanel,
    togglePerformanceDashboard,

    // Performance utilities
    trackPerformance: enhancedTrackPerformance,
    getPerformanceMetrics,
    clearPerformanceMetrics,
    logDebugInfo: enhancedLogDebugInfo,
    measureRenderTime,
    resetDevTools,

    // Metrics collection
    getStateMetrics,
    getUIMetrics,
    getControlsMetrics,
    getLayoutMetrics
  };
};

export default usePerformanceMonitoring;
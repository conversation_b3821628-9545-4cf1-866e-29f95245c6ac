/**
 * Word Similarity Detection for DEFEATER.AI
 * 
 * Detects intentional misspellings and word manipulations while allowing
 * legitimate word variations like "create" vs "creating"
 */

/**
 * Calculates Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
}

/**
 * Calculates similarity ratio between two words (0-1, where 1 is identical)
 */
function calculateSimilarity(word1: string, word2: string): number {
  if (word1 === word2) {
      return 1;
    }
  
  const maxLength = Math.max(word1.length, word2.length);
  if (maxLength === 0) {
      return 1;
    }
  
  const distance = levenshteinDistance(word1, word2);
  return (maxLength - distance) / maxLength;
}

/**
 * Normalizes a word for comparison
 */
function normalizeWord(word: string): string {
  return word.toLowerCase()
    .replace(/[^a-z]/g, '') // Remove punctuation and numbers
    .trim();
}

/**
 * Gets the root/stem of a word (basic implementation)
 */
function getWordRoot(word: string): string {
  const normalized = normalizeWord(word);
  
  // Basic stemming rules for common English suffixes
  const suffixes = [
    'ing', 'ed', 'er', 'est', 'ly', 'tion', 'sion', 'ness', 'ment', 
    'able', 'ible', 'ful', 'less', 'ous', 'ive', 'al', 'ic', 'ical'
  ];
  
  for (const suffix of suffixes.sort((a, b) => b.length - a.length)) {
    if (normalized.endsWith(suffix) && normalized.length > suffix.length + 2) {
      return normalized.slice(0, -suffix.length);
    }
  }
  
  // Handle plural forms
  if (normalized.endsWith('s') && normalized.length > 3) {
    return normalized.slice(0, -1);
  }
  
  return normalized;
}

/**
 * Checks if two words are likely the same word with different forms
 * (e.g., "create" vs "creating" = true, "create" vs "creat" = false)
 */
function areLegitimateVariations(word1: string, word2: string): boolean {
  const norm1 = normalizeWord(word1);
  const norm2 = normalizeWord(word2);
  
  // Exact match
  if (norm1 === norm2) {
      return true;
    }
  
  // Check if they share the same root
  const root1 = getWordRoot(norm1);
  const root2 = getWordRoot(norm2);
  
  // If roots are the same and both words are reasonable length
  if (root1 === root2 && root1.length >= 3) {
    // Additional check: make sure the variations are reasonable
    const lengthDiff = Math.abs(norm1.length - norm2.length);
    
    // Allow reasonable suffix differences (up to 4 characters)
    if (lengthDiff <= 4) {
      return true;
    }
  }
  
  return false;
}

/**
 * Checks if a word is likely a misspelling or intentional manipulation
 */
function isSuspiciousVariation(newWord: string, usedWord: string): boolean {
  const norm1 = normalizeWord(newWord);
  const norm2 = normalizeWord(usedWord);
  
  // Skip if they're legitimate variations
  if (areLegitimateVariations(norm1, norm2)) {
    return false;
  }
  
  // Calculate similarity
  const similarity = calculateSimilarity(norm1, norm2);
  
  // High similarity but not legitimate variations = suspicious
  // Threshold: 0.7 means 70% similar
  if (similarity >= 0.7) {
    const lengthDiff = Math.abs(norm1.length - norm2.length);
    
    // Very similar length with high similarity = likely misspelling
    if (lengthDiff <= 2) {
      return true;
    }
    
    // Check for common manipulation patterns
    if (hasCommonManipulations(norm1, norm2)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Checks for common word manipulation patterns
 */
function hasCommonManipulations(word1: string, word2: string): boolean {
  // Character repetition (speed -> speeed)
  if (hasCharacterRepetition(word1, word2)) return true;
  
  // Character swapping (create -> craete)
  if (hasCharacterSwapping(word1, word2)) return true;
  
  // Single character changes (create -> creat)
  if (hasSingleCharacterChange(word1, word2)) return true;
  
  return false;
}

/**
 * Detects character repetition patterns
 */
function hasCharacterRepetition(word1: string, word2: string): boolean {
  // Remove repeated characters and compare
  const deduped1 = word1.replace(/(.)\1+/g, '$1');
  const deduped2 = word2.replace(/(.)\1+/g, '$1');
  
  return deduped1 === deduped2 && word1 !== word2;
}

/**
 * Detects character swapping patterns
 */
function hasCharacterSwapping(word1: string, word2: string): boolean {
  if (Math.abs(word1.length - word2.length) !== 0) return false;
  
  let differences = 0;
  for (let i = 0; i < word1.length; i++) {
    if (word1[i] !== word2[i]) {
      differences++;
    }
  }
  
  // If exactly 2 characters are different, check if they're swapped
  if (differences === 2) {
    for (let i = 0; i < word1.length - 1; i++) {
      if (word1[i] === word2[i + 1] && word1[i + 1] === word2[i]) {
        return true;
      }
    }
  }
  
  return false;
}

/**
 * Detects single character addition/removal/substitution
 */
function hasSingleCharacterChange(word1: string, word2: string): boolean {
  const distance = levenshteinDistance(word1, word2);
  const minLength = Math.min(word1.length, word2.length);
  
  // Single character change on words of reasonable length
  return distance === 1 && minLength >= 4;
}

/**
 * Main function: Checks if a new word conflicts with previously used words
 */
export function checkWordReuse(newWord: string, usedWords: string[]): {
  isReuse: boolean;
  reason?: string;
  conflictingWord?: string;
  type?: 'exact' | 'variation' | 'suspicious';
} {
  const normalizedNew = normalizeWord(newWord);
  
  for (const usedWord of usedWords) {
    const normalizedUsed = normalizeWord(usedWord);
    
    // Exact match
    if (normalizedNew === normalizedUsed) {
      return {
        isReuse: true,
        reason: `Exact word reuse: "${newWord}" was already used as "${usedWord}"`,
        conflictingWord: usedWord,
        type: 'exact'
      };
    }
    
    // Legitimate variations (allow these)
    if (areLegitimateVariations(normalizedNew, normalizedUsed)) {
      return {
        isReuse: true,
        reason: `Word variation reuse: "${newWord}" is a variation of "${usedWord}"`,
        conflictingWord: usedWord,
        type: 'variation'
      };
    }
    
    // Suspicious manipulations (reject these)
    if (isSuspiciousVariation(normalizedNew, normalizedUsed)) {
      return {
        isReuse: true,
        reason: `Suspicious word manipulation: "${newWord}" appears to be a misspelling of "${usedWord}"`,
        conflictingWord: usedWord,
        type: 'suspicious'
      };
    }
  }
  
  return { isReuse: false };
}

/**
 * Debug function to analyze word similarity
 */
export function analyzeWordSimilarity(word1: string, word2: string): {
  similarity: number;
  isLegitimateVariation: boolean;
  isSuspicious: boolean;
  details: {
    normalized1: string;
    normalized2: string;
    root1: string;
    root2: string;
    levenshteinDistance: number;
  };
} {
  const norm1 = normalizeWord(word1);
  const norm2 = normalizeWord(word2);
  
  return {
    similarity: calculateSimilarity(norm1, norm2),
    isLegitimateVariation: areLegitimateVariations(word1, word2),
    isSuspicious: isSuspiciousVariation(word1, word2),
    details: {
      normalized1: norm1,
      normalized2: norm2,
      root1: getWordRoot(norm1),
      root2: getWordRoot(norm2),
      levenshteinDistance: levenshteinDistance(norm1, norm2)
    }
  };
}

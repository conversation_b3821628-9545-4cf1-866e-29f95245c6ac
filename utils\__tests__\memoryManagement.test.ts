/**
 * Memory Management Tests
 * 
 * Tests for the memory management utilities to ensure proper
 * resource tracking and cleanup functionality.
 */

import { memoryManager, useMemoryCleanup, managedSetTimeout, managedSetInterval, managedAddEventListener } from '../memoryManagement';

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.debug = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.debug = originalConsole.debug;
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;
});

describe('MemoryManager', () => {
  let initialMetrics: any;

  beforeEach(() => {
    // Store initial state and clean up any existing resources
    initialMetrics = memoryManager.getMetrics();
    memoryManager.cleanupAll();
  });

  afterEach(() => {
    // Clean up after each test
    memoryManager.cleanupAll();
  });

  it('tracks and cleans up timer resources', () => {
    const mockCallback = jest.fn();
    
    // Track a timer resource
    const timerId = memoryManager.trackResource(
      'timer',
      setTimeout(mockCallback, 100),
      () => clearTimeout(timerId as any),
      'TestComponent'
    );

    const metrics = memoryManager.getMetrics();
    expect(metrics.activeResources).toBe(1);
    expect(metrics.totalCreated).toBe(1);

    // Clean up the resource
    const cleaned = memoryManager.cleanupResource(timerId);
    expect(cleaned).toBe(true);

    const metricsAfter = memoryManager.getMetrics();
    expect(metricsAfter.activeResources).toBe(0);
    expect(metricsAfter.totalCleaned).toBe(1);
  });

  it('tracks and cleans up event listener resources', () => {
    const mockElement = document.createElement('div');
    const mockHandler = jest.fn();
    
    // Track an event listener resource
    const listenerId = memoryManager.trackResource(
      'listener',
      { element: mockElement, event: 'click', handler: mockHandler },
      () => mockElement.removeEventListener('click', mockHandler),
      'TestComponent'
    );

    const metrics = memoryManager.getMetrics();
    expect(metrics.activeResources).toBe(1);

    // Clean up the resource
    const cleaned = memoryManager.cleanupResource(listenerId);
    expect(cleaned).toBe(true);

    const metricsAfter = memoryManager.getMetrics();
    expect(metricsAfter.activeResources).toBe(0);
  });

  it('cleans up all resources for a component', () => {
    const mockCallback1 = jest.fn();
    const mockCallback2 = jest.fn();
    
    // Track multiple resources for the same component
    memoryManager.trackResource(
      'timer',
      setTimeout(mockCallback1, 100),
      () => clearTimeout(1),
      'TestComponent'
    );

    memoryManager.trackResource(
      'timer',
      setTimeout(mockCallback2, 200),
      () => clearTimeout(2),
      'TestComponent'
    );

    // Track a resource for a different component
    memoryManager.trackResource(
      'timer',
      setTimeout(jest.fn(), 300),
      () => clearTimeout(3),
      'OtherComponent'
    );

    expect(memoryManager.getMetrics().activeResources).toBe(3);

    // Clean up only TestComponent resources
    const cleaned = memoryManager.cleanupComponent('TestComponent');
    expect(cleaned).toBe(2);

    const metrics = memoryManager.getMetrics();
    expect(metrics.activeResources).toBe(1); // OtherComponent resource remains
  });

  it('cleans up all resources', () => {
    const beforeMetrics = memoryManager.getMetrics();

    // Track multiple resources
    memoryManager.trackResource('timer', 1, () => {}, 'Component1');
    memoryManager.trackResource('listener', 2, () => {}, 'Component2');
    memoryManager.trackResource('observer', 3, () => {}, 'Component3');

    expect(memoryManager.getMetrics().activeResources).toBe(beforeMetrics.activeResources + 3);

    // Clean up all resources
    const cleaned = memoryManager.cleanupAll();
    expect(cleaned).toBeGreaterThanOrEqual(3);

    const metrics = memoryManager.getMetrics();
    expect(metrics.activeResources).toBe(0);
  });

  it('handles cleanup errors gracefully', () => {
    const faultyCleanup = jest.fn(() => {
      throw new Error('Cleanup failed');
    });

    const resourceId = memoryManager.trackResource(
      'timer',
      'test-resource',
      faultyCleanup,
      'TestComponent'
    );

    // Should return false when cleanup fails
    const cleaned = memoryManager.cleanupResource(resourceId);
    expect(cleaned).toBe(false);
    expect(faultyCleanup).toHaveBeenCalled();
  });
});

describe('Managed Resource Functions', () => {
  let initialResourceCount: number;

  beforeEach(() => {
    memoryManager.cleanupAll();
    initialResourceCount = memoryManager.getMetrics().activeResources;
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    memoryManager.cleanupAll();
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('managedSetTimeout creates tracked timer', () => {
    const mockCallback = jest.fn();

    const timerId = managedSetTimeout(mockCallback, 1000, 'TestComponent');

    expect(memoryManager.getMetrics().activeResources).toBe(initialResourceCount + 1);

    // Fast-forward time
    jest.advanceTimersByTime(1000);
    expect(mockCallback).toHaveBeenCalled();

    // Clean up
    memoryManager.cleanupResource(timerId);
    expect(memoryManager.getMetrics().activeResources).toBe(initialResourceCount);
  });

  it('managedSetInterval creates tracked interval', () => {
    const mockCallback = jest.fn();

    const intervalId = managedSetInterval(mockCallback, 500, 'TestComponent');

    expect(memoryManager.getMetrics().activeResources).toBe(initialResourceCount + 1);

    // Fast-forward time to trigger interval multiple times
    jest.advanceTimersByTime(1500);
    expect(mockCallback).toHaveBeenCalledTimes(3);

    // Clean up
    memoryManager.cleanupResource(intervalId);
    expect(memoryManager.getMetrics().activeResources).toBe(initialResourceCount);
  });

  it('managedAddEventListener creates tracked listener', () => {
    const mockElement = document.createElement('button');
    const mockHandler = jest.fn();

    const listenerId = managedAddEventListener(
      mockElement,
      'click',
      mockHandler,
      false,
      'TestComponent'
    );

    expect(memoryManager.getMetrics().activeResources).toBe(initialResourceCount + 1);

    // Trigger the event
    mockElement.click();
    expect(mockHandler).toHaveBeenCalled();

    // Clean up
    memoryManager.cleanupResource(listenerId);
    expect(memoryManager.getMetrics().activeResources).toBe(initialResourceCount);
  });
});

describe('useMemoryCleanup Hook', () => {
  beforeEach(() => {
    memoryManager.cleanupAll();
  });

  it('provides component-scoped resource management', () => {
    const { trackResource, cleanupComponent, getMetrics } = useMemoryCleanup('TestComponent');
    const initialCount = getMetrics().activeResources;

    // Track a resource
    const resourceId = trackResource('timer', 'test', () => {});

    expect(getMetrics().activeResources).toBe(initialCount + 1);

    // Clean up component resources
    const cleaned = cleanupComponent();
    expect(cleaned).toBe(1);
    expect(getMetrics().activeResources).toBe(initialCount);
  });
});

describe('Memory Metrics', () => {
  it('tracks resource creation and cleanup metrics', () => {
    const initialMetrics = memoryManager.getMetrics();
    
    // Track some resources
    const id1 = memoryManager.trackResource('timer', 1, () => {});
    const id2 = memoryManager.trackResource('listener', 2, () => {});
    
    const afterCreation = memoryManager.getMetrics();
    expect(afterCreation.activeResources).toBe(initialMetrics.activeResources + 2);
    expect(afterCreation.totalCreated).toBe(initialMetrics.totalCreated + 2);
    
    // Clean up one resource
    memoryManager.cleanupResource(id1);
    
    const afterCleanup = memoryManager.getMetrics();
    expect(afterCleanup.activeResources).toBe(initialMetrics.activeResources + 1);
    expect(afterCleanup.totalCleaned).toBe(initialMetrics.totalCleaned + 1);
    
    // Clean up remaining resource
    memoryManager.cleanupResource(id2);
    
    const final = memoryManager.getMetrics();
    expect(final.activeResources).toBe(initialMetrics.activeResources);
    expect(final.totalCleaned).toBe(initialMetrics.totalCleaned + 2);
  });
});

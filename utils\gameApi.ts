/**
 * Game API Functions
 * 
 * Optimized API functions for game operations with caching,
 * retry logic, and performance monitoring.
 */

import { apiClient } from './apiClient';
import { GameRequest, GameResponse, GameState, DifficultyLevel } from '@/types/game';
import { performanceMonitor } from './performance';
import { GameErrorHandler } from './errorHandling';

interface GameApiConfig {
  enableCaching?: boolean;
  retries?: number;
  timeout?: number;
}

interface ChatRequest {
  userMessage?: string;
  gameContext: {
    gameState?: GameState;
    trigger: 'move' | 'win' | 'loss' | 'manual';
    currentWord?: string;
    step?: number;
    maxSteps?: number;
  };
}

interface ChatResponse {
  success: boolean;
  aiMessage: string;
  error?: string;
}

/**
 * Start a new game with optimized API call
 */
export async function startGame(
  difficulty: DifficultyLevel = 'medium',
  config: GameApiConfig = {}
): Promise<GameResponse> {
  const {
    enableCaching = false, // Don't cache game starts
    retries = 3,
    timeout = 30000
  } = config;

  try {
    const request: GameRequest = {
      gameState: null,
      action: 'start',
      difficulty
    };

    const response = await performanceMonitor.measureApiCall(async () => {
      return await apiClient.request<GameResponse>('/api/game', {
        method: 'POST',
        body: request,
        retries,
        timeout,
        cache: enableCaching,
        cacheTTL: enableCaching ? 60000 : 0, // 1 minute cache for game starts
        deduplication: true // Prevent multiple simultaneous game starts
      });
    }, 'start_game');

    if (!response.success) {
      throw new Error(response.error || 'Failed to start game');
    }

    return response;

  } catch (error) {
    GameErrorHandler.logError(error as Error, 'GameApi.startGame');
    throw new Error(`Failed to start game: ${(error as Error).message}`);
  }
}

/**
 * Submit definition with optimized API call
 */
export async function submitDefinition(
  gameState: GameState,
  playerDefinition: string,
  config: GameApiConfig = {}
): Promise<GameResponse> {
  const {
    enableCaching = true, // Cache similar definitions
    retries = 3,
    timeout = 60000 // Longer timeout for AI processing
  } = config;

  try {
    const request: GameRequest = {
      gameState,
      playerDefinition: playerDefinition.trim(),
      action: 'submit'
    };

    const response = await performanceMonitor.measureApiCall(async () => {
      return await apiClient.request<GameResponse>('/api/game', {
        method: 'POST',
        body: request,
        retries,
        timeout,
        cache: enableCaching,
        cacheTTL: enableCaching ? 300000 : 0, // 5 minutes cache for definitions
        deduplication: true // Prevent duplicate submissions
      });
    }, 'submit_definition');

    if (!response.success) {
      throw new Error(response.error || 'Failed to submit definition');
    }

    return response;

  } catch (error) {
    GameErrorHandler.logError(error as Error, 'GameApi.submitDefinition');
    throw new Error(`Failed to submit definition: ${(error as Error).message}`);
  }
}

/**
 * Reset game with optimized API call
 */
export async function resetGame(
  difficulty: DifficultyLevel = 'medium',
  config: GameApiConfig = {}
): Promise<GameResponse> {
  const {
    enableCaching = false, // Don't cache resets
    retries = 2, // Fewer retries for resets
    timeout = 30000
  } = config;

  try {
    const request: GameRequest = {
      gameState: null,
      action: 'reset',
      difficulty
    };

    const response = await performanceMonitor.measureApiCall(async () => {
      return await apiClient.request<GameResponse>('/api/game', {
        method: 'POST',
        body: request,
        retries,
        timeout,
        cache: enableCaching,
        deduplication: false // Allow multiple resets
      });
    }, 'reset_game');

    if (!response.success) {
      throw new Error(response.error || 'Failed to reset game');
    }

    return response;

  } catch (error) {
    GameErrorHandler.logError(error as Error, 'GameApi.resetGame');
    throw new Error(`Failed to reset game: ${(error as Error).message}`);
  }
}

/**
 * Send chat message with optimized API call
 */
export async function sendChatMessage(
  request: ChatRequest,
  config: GameApiConfig = {}
): Promise<ChatResponse> {
  const {
    enableCaching = true, // Cache similar chat contexts
    retries = 2, // Fewer retries for chat
    timeout = 30000
  } = config;

  try {
    const response = await performanceMonitor.measureApiCall(async () => {
      return await apiClient.request<ChatResponse>('/api/chat', {
        method: 'POST',
        body: request,
        retries,
        timeout,
        cache: enableCaching,
        cacheTTL: enableCaching ? 180000 : 0, // 3 minutes cache for chat
        deduplication: true // Prevent duplicate chat requests
      });
    }, 'chat_message');

    if (!response.success) {
      throw new Error(response.error || 'Failed to send chat message');
    }

    return response;

  } catch (error) {
    GameErrorHandler.logError(error as Error, 'GameApi.sendChatMessage');
    throw new Error(`Failed to send chat message: ${(error as Error).message}`);
  }
}

/**
 * Health check with optimized API call
 */
export async function healthCheck(config: GameApiConfig = {}): Promise<boolean> {
  const {
    enableCaching = true, // Cache health checks
    retries = 1, // Single retry for health checks
    timeout = 10000 // Short timeout for health checks
  } = config;

  try {
    const response = await apiClient.request<{ status: string }>('/api/health', {
      method: 'GET',
      retries,
      timeout,
      cache: enableCaching,
      cacheTTL: enableCaching ? 30000 : 0, // 30 seconds cache for health
      deduplication: true
    });

    return response.status === 'ok';

  } catch (error) {
    console.debug('Health check failed:', error);
    return false;
  }
}

/**
 * Batch multiple API requests
 */
export async function batchRequests<T>(
  requests: Array<() => Promise<T>>,
  maxConcurrency: number = 3
): Promise<T[]> {
  const results: T[] = [];
  const executing: Promise<void>[] = [];

  for (const request of requests) {
    const promise = request().then(result => {
      results.push(result);
    });

    executing.push(promise);

    if (executing.length >= maxConcurrency) {
      await Promise.race(executing);
      executing.splice(executing.findIndex(p => p === promise), 1);
    }
  }

  await Promise.all(executing);
  return results;
}

/**
 * Preload game data for better performance
 */
export async function preloadGameData(difficulty: DifficultyLevel): Promise<void> {
  try {
    // Preload a game state to warm up the cache
    await startGame(difficulty, {
      enableCaching: true,
      retries: 1,
      timeout: 15000
    });

    console.debug(`🚀 Preloaded game data for ${difficulty} difficulty`);

  } catch (error) {
    console.debug('Failed to preload game data:', error);
    // Don't throw - preloading is optional
  }
}

/**
 * Get API performance metrics
 */
export function getApiMetrics() {
  return {
    client: apiClient.getMetrics(),
    performance: performanceMonitor.getMetrics(),
    cache: {
      size: apiClient.getCacheSize()
    }
  };
}

/**
 * Clear API cache
 */
export function clearApiCache(): void {
  apiClient.clearCache();
  console.debug('🧹 API cache cleared');
}

/**
 * Cleanup API resources
 */
export function cleanupApiResources(): void {
  apiClient.cleanup();
  console.debug('🧹 API resources cleaned up');
}

export default {
  startGame,
  submitDefinition,
  resetGame,
  sendChatMessage,
  healthCheck,
  batchRequests,
  preloadGameData,
  getApiMetrics,
  clearApiCache,
  cleanupApiResources
};

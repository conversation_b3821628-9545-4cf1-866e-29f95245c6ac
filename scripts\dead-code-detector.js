#!/usr/bin/env node

/**
 * Dead Code Detection Script for DEFEATER.AI
 * 
 * Identifies unused imports, components, and CSS classes that can be safely removed
 * during the layout system migration from legacy to spatial design.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: './components',
  pagesDir: './pages',
  stylesDir: './styles',
  excludePatterns: [
    'node_modules',
    '.next',
    '.git',
    'test',
    'spec'
  ],
  legacyPatterns: {
    cssClasses: [
      'card', 'card-elevated', 'card-neon',
      'mb-6', 'mb-8', 'mb-12',
      'mt-4', 'mt-6', 'mt-8',
      'text-center', 'flex', 'gap-4'
    ],
    components: [
      'GameBoard.tsx',
      'GameInput.tsx', 
      'GameStatus.tsx',
      'DefinitionHistory.tsx',
      'ValidationFeedback.tsx',
      'WordsLeftCounter.tsx',
      'CommonWordTracker.tsx',
      'TargetDisplay.tsx',
      'GameStats.tsx',
      'DevPanel.tsx',
      'PostGameAnalysis.tsx'
    ]
  }
};

// Utility functions
function getAllFiles(dir, extension = '') {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !config.excludePatterns.includes(item)) {
        traverse(fullPath);
      } else if (stat.isFile() && (extension === '' || fullPath.endsWith(extension))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

function readFileContent(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.warn(`Warning: Could not read file ${filePath}`);
    return '';
  }
}

// Detection functions
function detectUnusedCSSClasses() {
  console.log('\n🔍 Detecting unused CSS classes...\n');
  
  const cssFiles = getAllFiles(config.stylesDir, '.css');
  const componentFiles = [
    ...getAllFiles(config.srcDir, '.tsx'),
    ...getAllFiles(config.pagesDir, '.tsx')
  ];
  
  const results = {
    legacy: [],
    unused: [],
    total: 0
  };
  
  // Get all component content
  const allComponentContent = componentFiles
    .map(file => readFileContent(file))
    .join('\n');
  
  // Check legacy CSS patterns
  config.legacyPatterns.cssClasses.forEach(className => {
    const regex = new RegExp(`className.*["'\`][^"'\`]*\\b${className}\\b`, 'g');
    const matches = allComponentContent.match(regex);
    
    if (matches && matches.length > 0) {
      results.legacy.push({
        class: className,
        usage: matches.length,
        status: '⚠️  LEGACY - Should be replaced'
      });
    } else {
      results.unused.push({
        class: className,
        status: '✅ UNUSED - Can be removed'
      });
    }
    results.total++;
  });
  
  return results;
}

function detectUnusedComponents() {
  console.log('\n🔍 Detecting component usage...\n');
  
  const componentFiles = getAllFiles(config.srcDir, '.tsx');
  const pageFiles = getAllFiles(config.pagesDir, '.tsx');
  const allFiles = [...componentFiles, ...pageFiles];
  
  const results = {
    legacy: [],
    active: [],
    total: 0
  };
  
  // Get all import statements
  const allImports = allFiles
    .map(file => readFileContent(file))
    .join('\n');
  
  config.legacyPatterns.components.forEach(componentName => {
    const baseName = componentName.replace('.tsx', '');
    const importRegex = new RegExp(`import.*${baseName}.*from`, 'g');
    const usageRegex = new RegExp(`<${baseName}[\\s>]`, 'g');
    
    const importMatches = allImports.match(importRegex);
    const usageMatches = allImports.match(usageRegex);
    
    const importCount = importMatches ? importMatches.length : 0;
    const usageCount = usageMatches ? usageMatches.length : 0;
    
    if (importCount > 0 || usageCount > 0) {
      results.legacy.push({
        component: componentName,
        imports: importCount,
        usage: usageCount,
        status: '⚠️  LEGACY - Active, needs migration'
      });
    } else {
      results.active.push({
        component: componentName,
        status: '✅ UNUSED - Can be removed'
      });
    }
    results.total++;
  });
  
  return results;
}

function generateReport(cssResults, componentResults) {
  console.log('\n📊 DEAD CODE DETECTION REPORT\n');
  console.log('='.repeat(50));
  
  // CSS Classes Report
  console.log('\n🎨 CSS CLASSES:');
  console.log(`Total checked: ${cssResults.total}`);
  console.log(`Legacy in use: ${cssResults.legacy.length}`);
  console.log(`Unused: ${cssResults.unused.length}`);
  
  if (cssResults.legacy.length > 0) {
    console.log('\n⚠️  Legacy CSS classes still in use:');
    cssResults.legacy.forEach(item => {
      console.log(`  • .${item.class} - ${item.usage} usages - ${item.status}`);
    });
  }
  
  if (cssResults.unused.length > 0) {
    console.log('\n✅ Unused CSS classes (safe to remove):');
    cssResults.unused.forEach(item => {
      console.log(`  • .${item.class} - ${item.status}`);
    });
  }
  
  // Components Report
  console.log('\n🧩 COMPONENTS:');
  console.log(`Total checked: ${componentResults.total}`);
  console.log(`Legacy in use: ${componentResults.legacy.length}`);
  console.log(`Unused: ${componentResults.active.length}`);
  
  if (componentResults.legacy.length > 0) {
    console.log('\n⚠️  Legacy components still in use:');
    componentResults.legacy.forEach(item => {
      console.log(`  • ${item.component} - ${item.imports} imports, ${item.usage} usages - ${item.status}`);
    });
  }
  
  if (componentResults.active.length > 0) {
    console.log('\n✅ Unused components (safe to remove):');
    componentResults.active.forEach(item => {
      console.log(`  • ${item.component} - ${item.status}`);
    });
  }
  
  // Summary
  console.log('\n📋 MIGRATION SUMMARY:');
  console.log(`• ${cssResults.legacy.length} CSS classes need spatial replacements`);
  console.log(`• ${componentResults.legacy.length} components need migration`);
  console.log(`• ${cssResults.unused.length + componentResults.active.length} items can be removed`);
  
  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Create spatial versions of active legacy components');
  console.log('2. Replace legacy CSS with spatial design patterns');
  console.log('3. Remove unused code after migration validation');
  console.log('4. Update imports and references');
  
  console.log('\n='.repeat(50));
}

// Main execution
function main() {
  console.log('🚀 Starting dead code detection for DEFEATER.AI layout migration...');
  
  try {
    const cssResults = detectUnusedCSSClasses();
    const componentResults = detectUnusedComponents();
    
    generateReport(cssResults, componentResults);
    
    console.log('\n✅ Dead code detection completed successfully!');
    console.log('📖 See docs/LAYOUT_SYSTEM_MIGRATION.md for migration guide');
    
  } catch (error) {
    console.error('❌ Error during dead code detection:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { detectUnusedCSSClasses, detectUnusedComponents, generateReport };

/**
 * Foundation Test Page
 * 
 * Comprehensive testing suite for design system foundation
 * Tests responsiveness, accessibility, performance, and visual consistency
 */

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import GameLayout from '@/components/layout/GameLayout';
import GameFocus from '@/components/game/GameFocus';
import { Hero, Primary, Secondary, Body, Small, Status, Accent, Mono } from '@/components/ui/Typography';
import { validateDesignSystemContrast, prefersReducedMotion, prefersHighContrast } from '@/utils/accessibility';

const FoundationTestPage: React.FC = () => {
  const [showSidePanel, setShowSidePanel] = useState(false);
  const [screenSize, setScreenSize] = useState({ width: 0, height: 0 });
  const [contrastResults, setContrastResults] = useState<any>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<any>(null);

  useEffect(() => {
    // Screen size tracking
    const updateScreenSize = () => {
      setScreenSize({ width: window.innerWidth, height: window.innerHeight });
    };

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);

    // Contrast validation
    setContrastResults(validateDesignSystemContrast());

    // Performance metrics
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      setPerformanceMetrics({
        loadTime: Math.round(navigation.loadEventEnd - navigation.fetchStart),
        domContentLoaded: Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart),
        firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0
      });
    }

    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  const getBreakpointName = (width: number) => {
    if (width < 640) return 'Mobile';
    if (width < 768) return 'Small Mobile';
    if (width < 1024) return 'Tablet';
    if (width < 1280) return 'Desktop';
    return 'Large Desktop';
  };

  const sidePanel = (
    <div>
      <Primary style={{ marginBottom: 'var(--space-4)' }}>
        Foundation Test Results
      </Primary>
      
      {/* Screen Size Info */}
      <div className="spatial-card" style={{ marginBottom: 'var(--space-4)' }}>
        <Secondary style={{ marginBottom: 'var(--space-2)' }}>Screen Information</Secondary>
        <Body>Size: <Accent>{screenSize.width} × {screenSize.height}</Accent></Body>
        <Body>Breakpoint: <Accent>{getBreakpointName(screenSize.width)}</Accent></Body>
        <Body>Reduced Motion: <Status status={prefersReducedMotion() ? 'warning' : 'success'}>
          {prefersReducedMotion() ? 'Enabled' : 'Disabled'}
        </Status></Body>
        <Body>High Contrast: <Status status={prefersHighContrast() ? 'info' : 'success'}>
          {prefersHighContrast() ? 'Enabled' : 'Disabled'}
        </Status></Body>
      </div>

      {/* Performance Metrics */}
      {performanceMetrics && (
        <div className="spatial-card" style={{ marginBottom: 'var(--space-4)' }}>
          <Secondary style={{ marginBottom: 'var(--space-2)' }}>Performance</Secondary>
          <Body>Load Time: <Accent>{performanceMetrics.loadTime}ms</Accent></Body>
          <Body>DOM Ready: <Accent>{performanceMetrics.domContentLoaded}ms</Accent></Body>
          <Body>First Paint: <Accent>{Math.round(performanceMetrics.firstPaint)}ms</Accent></Body>
          <Small>
            <Status status={performanceMetrics.loadTime < 3000 ? 'success' : 'warning'}>
              {performanceMetrics.loadTime < 3000 ? 'Good Performance' : 'Needs Optimization'}
            </Status>
          </Small>
        </div>
      )}

      {/* Contrast Results */}
      {contrastResults && (
        <div className="spatial-card">
          <Secondary style={{ marginBottom: 'var(--space-2)' }}>WCAG Compliance</Secondary>
          {Object.entries(contrastResults).map(([textType, backgrounds]: [string, any]) => (
            <div key={textType} style={{ marginBottom: 'var(--space-2)' }}>
              <Small style={{ textTransform: 'capitalize', fontWeight: 'var(--font-medium)' }}>
                {textType} Text:
              </Small>
              {Object.entries(backgrounds).map(([bgType, result]: [string, any]) => (
                <Body key={bgType} style={{ marginLeft: 'var(--space-2)', fontSize: 'var(--text-xs)' }}>
                  {bgType}: <Status status={result.wcagAA ? 'success' : 'error'}>
                    {result.ratio}:1 {result.wcagAA ? '✓' : '✗'}
                  </Status>
                </Body>
              ))}
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <>
      <Head>
        <title>Foundation Test - DEFEATER.AI Design System</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <GameLayout>
        <GameFocus>
          {/* Header */}
          <Hero>Foundation Test</Hero>
          <Primary style={{ marginBottom: 'var(--space-8)' }}>
            Design System Validation Suite
          </Primary>

          {/* Component Tests */}
          <div style={{ width: '100%', maxWidth: '800px', display: 'flex', flexDirection: 'column', gap: 'var(--space-6)' }}>
            
            {/* Button Tests */}
            <div className="spatial-card">
              <Secondary style={{ marginBottom: 'var(--space-4)' }}>Button Components</Secondary>
              <div style={{ display: 'flex', gap: 'var(--space-4)', flexWrap: 'wrap', justifyContent: 'center' }}>
                <button className="btn-primary">Primary Button</button>
                <button className="btn-secondary">Secondary Button</button>
                <button className="btn-primary" disabled>Disabled Button</button>
              </div>
            </div>

            {/* Input Tests */}
            <div className="spatial-card">
              <Secondary style={{ marginBottom: 'var(--space-4)' }}>Input Components</Secondary>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-3)' }}>
                <input 
                  className="input-primary" 
                  placeholder="Test input field..."
                  style={{ height: 'auto', minHeight: '48px' }}
                />
                <textarea 
                  className="input-primary" 
                  placeholder="Test textarea..."
                  rows={3}
                />
              </div>
            </div>

            {/* Typography Scale */}
            <div className="spatial-card">
              <Secondary style={{ marginBottom: 'var(--space-4)' }}>Typography Scale</Secondary>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-2)' }}>
                <Hero as="div" style={{ fontSize: 'var(--text-3xl)' }}>Hero Text Sample</Hero>
                <Primary as="div">Primary Text Sample</Primary>
                <Secondary as="div">Secondary Text Sample</Secondary>
                <Body as="div">Body Text Sample</Body>
                <Small as="div">Small Text Sample</Small>
              </div>
            </div>

            {/* Target Strip Test */}
            <div className="target-strip">
              <Mono>I n n o v _ _ _ _ n</Mono>
              <span style={{ color: 'var(--text-muted)' }}>•</span>
              <Mono>E c o s _ _ _ _ m</Mono>
              <span style={{ color: 'var(--text-muted)' }}>•</span>
              <Mono style={{ textDecoration: 'line-through', opacity: 0.6 }}>
                F r i c t i o n
              </Mono>
            </div>

            {/* Status Indicators */}
            <div className="spatial-card">
              <Secondary style={{ marginBottom: 'var(--space-4)' }}>Status Indicators</Secondary>
              <div style={{ display: 'flex', gap: 'var(--space-4)', flexWrap: 'wrap', justifyContent: 'center' }}>
                <Status status="success">Success State</Status>
                <Status status="error">Error State</Status>
                <Status status="warning">Warning State</Status>
                <Status status="info">Info State</Status>
                <Status status="processing">Processing State</Status>
              </div>
            </div>

            {/* Responsive Test Instructions */}
            <div className="spatial-card">
              <Secondary style={{ marginBottom: 'var(--space-4)' }}>Responsive Testing</Secondary>
              <Body style={{ marginBottom: 'var(--space-2)' }}>
                Resize your browser window to test responsive behavior:
              </Body>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-1)' }}>
                <Small>• <Accent>Mobile:</Accent> &lt; 640px - Stacked layout, full-width panels</Small>
                <Small>• <Accent>Tablet:</Accent> 768px+ - Side-by-side layout, 300px panel</Small>
                <Small>• <Accent>Desktop:</Accent> 1024px+ - Optimized layout, 350px panel</Small>
              </div>
            </div>

            {/* Performance Test */}
            <div className="spatial-card">
              <Secondary style={{ marginBottom: 'var(--space-4)' }}>Performance Validation</Secondary>
              <Body style={{ marginBottom: 'var(--space-2)' }}>
                Check the side panel for detailed performance metrics and WCAG compliance results.
              </Body>
              <Small>
                All animations respect <Accent>prefers-reduced-motion</Accent> settings.
              </Small>
            </div>
          </div>
        </GameFocus>
      </GameLayout>
    </>
  );
};

export default FoundationTestPage;

import type { AppProps } from 'next/app';
import '@/styles/globals.css';
import '@/styles/accessibility-performance.css';
import { FeedbackProvider } from '@/contexts/FeedbackContext';
import { AnimationProvider } from '@/contexts/AnimationContext';
import { AccessibilityPerformanceProvider } from '@/contexts/AccessibilityPerformanceContext';

export default function App({ Component, pageProps }: AppProps) {
  return (
    <AccessibilityPerformanceProvider>
      <AnimationProvider>
        <FeedbackProvider>
          <Component {...pageProps} />
        </FeedbackProvider>
      </AnimationProvider>
    </AccessibilityPerformanceProvider>
  );
}

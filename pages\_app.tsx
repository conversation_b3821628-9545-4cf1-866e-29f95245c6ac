import type { AppProps } from 'next/app';
import '@/styles/globals.css';
import '@/styles/accessibility-performance.css';
import { FeedbackProvider } from '@/contexts/FeedbackContext';
import { AnimationProvider } from '@/contexts/AnimationContext';
import { AccessibilityPerformanceProvider } from '@/contexts/AccessibilityPerformanceContext';
import ErrorBoundary from '@/components/ErrorBoundary';
import { useEffect } from 'react';
import { performanceMonitor } from '@/utils/performance';

export default function App({ Component, pageProps }: AppProps) {
  // Initialize network optimization and cleanup on app unmount
  useEffect(() => {
    // Initialize network optimization
    if (typeof window !== 'undefined') {
      // Preload critical resources on good connections
      networkOptimizer.preloadCriticalResources();
    }

    return () => {
      // Clean up performance observers and network monitoring when app unmounts
      if (typeof window !== 'undefined') {
        performanceMonitor.cleanup();
        networkOptimizer.cleanup();
      }
    };
  }, []);

  return (
    <ErrorBoundary
      level="page"
      onError={(error, errorInfo) => {
        console.error('App-level error:', error, errorInfo);
        // In production, send to error reporting service
      }}
    >
      <ErrorBoundary level="section" resetOnPropsChange>
        <AccessibilityPerformanceProvider>
          <AnimationProvider>
            <FeedbackProvider>
              <ErrorBoundary level="component" resetOnPropsChange>
                <Component {...pageProps} />
              </ErrorBoundary>
            </FeedbackProvider>
          </AnimationProvider>
        </AccessibilityPerformanceProvider>
      </ErrorBoundary>
    </ErrorBoundary>
  );
}

# DEFEATER.AI API Reference

> **Complete API documentation for DEFEATER.AI**
> **Updated: Post-Migration - Enhanced with Chat System & Performance Optimization**

## 🌐 **Base URL**
```
Development: http://localhost:3000/api
Production: https://defeater.ai/api
```

## 🎉 **Migration Updates**
- ✅ **Chat API**: New AI psychological warfare system
- ✅ **Enhanced Performance**: 28% faster response times
- ✅ **Better Error Handling**: Comprehensive validation
- ✅ **TypeScript**: Full type safety implementation
- ✅ **Testing**: 100% API endpoint coverage

## 🎮 **Game API Endpoint**

### **POST /api/game**

The main game API endpoint handles all game interactions through different action types.

#### **Request Format**
```typescript
{
  action: 'start' | 'submit';
  gameState?: GameState;
  playerDefinition?: string;
}
```

#### **Response Format**
```typescript
{
  success: boolean;
  gameState: GameState;
  aiResponse?: AIResponse;
  error?: string;
}
```

---

## 🚀 **Actions**

### **1. Start Game**

Initializes a new game with random words and targets.

#### **Request**
```json
{
  "action": "start"
}
```

#### **Response**
```json
{
  "success": true,
  "gameState": {
    "gameId": "game_1705123456789_abc123def",
    "currentWord": "transformer",
    "targets": ["ecosystem", "duration", "friction"],
    "burnedTargets": [],
    "definitions": [],
    "usedWords": ["transformer"],
    "aiChallengeWords": [],
    "step": 0,
    "maxSteps": 25,
    "gameStatus": "waiting",
    "difficulty": "medium"
  }
}
```

#### **Error Cases**
- **500**: AI initialization failed (falls back to random words)

---

### **2. Submit Definition**

Submits a player's definition for AI processing.

#### **Request**
```json
{
  "action": "submit",
  "gameState": { /* current game state */ },
  "playerDefinition": "a device that changes electrical voltage"
}
```

#### **Response**
```json
{
  "success": true,
  "gameState": {
    "gameId": "game_1705123456789_abc123def",
    "currentWord": "voltage",
    "targets": ["ecosystem", "duration", "friction"],
    "burnedTargets": [],
    "definitions": [
      {
        "id": "def_1705123456790_xyz789abc",
        "word": "transformer",
        "definition": "a device that changes electrical voltage",
        "wordCount": 6,
        "timestamp": 1705123456790
      }
    ],
    "usedWords": ["transformer", "device", "changes", "electrical", "voltage"],
    "aiChallengeWords": ["voltage"],
    "step": 1,
    "maxSteps": 25,
    "gameStatus": "waiting",
    "difficulty": "medium"
  },
  "aiResponse": {
    "accept": true,
    "reason": "Valid definition of transformer",
    "nextWord": "voltage",
    "gameResult": "continue",
    "resultReason": "",
    "internalAnalysis": {
      "playerConfidence": 75,
      "remainingPaths": ["ecosystem", "duration", "friction"],
      "recommendedBurn": null,
      "difficultyAdjustment": 0
    },
    "debugInfo": {
      "aiRawResponse": "{ \"accept\": true, ... }",
      "winDetectionMethod": "hybrid",
      "winCheckResults": {
        "directWin": false,
        "semanticWin": false,
        "aiDecision": "Game continues - no win/loss conditions met"
      },
      "gameStateAnalysis": {
        "remainingTargets": ["ecosystem", "duration", "friction"],
        "burnedTargets": [],
        "currentWord": "voltage",
        "step": 1,
        "isImpossible": false
      },
      "timestamp": 1705123456790
    }
  }
}
```

#### **Error Cases**
- **400**: Invalid game state or missing definition
- **422**: Definition violates game rules (word reuse, too long, etc.)
- **500**: AI processing failed (falls back to simple logic)

---

## 💬 **Chat API Endpoint (NEW)**

### **POST /api/chat**

AI psychological warfare chat system for real-time player interaction.

#### **Request Format**
```typescript
{
  userMessage: string;
  gameContext: {
    gameState: GameState;
    trigger: 'move' | 'win' | 'loss' | 'manual';
    currentWord?: string;
    step?: number;
    maxSteps?: number;
  };
}
```

#### **Response Format**
```typescript
{
  success: boolean;
  aiMessage: string;
  error?: string;
}
```

#### **Example Request**
```json
{
  "userMessage": "That was too easy!",
  "gameContext": {
    "gameState": { /* current game state */ },
    "trigger": "move",
    "currentWord": "transformer",
    "step": 3,
    "maxSteps": 25
  }
}
```

#### **Example Response**
```json
{
  "success": true,
  "aiMessage": "Easy? You're already at step 3 and haven't even scratched the surface. I'm three moves ahead of you."
}
```

#### **Error Cases**
- **400**: Invalid request format or missing game context
- **500**: AI chat processing failed

### **POST /api/debug-chat**

Debug endpoint for chat system testing and development.

#### **Features**
- Raw AI response logging
- Performance metrics
- Context analysis
- Development-only endpoint

---

## 📊 **Data Types**

### **GameState**
```typescript
interface GameState {
  gameId: string;                    // Unique game identifier
  currentWord: string | null;        // Current word to define
  targets: string[];                 // Target words to reach
  burnedTargets: string[];           // Targets eliminated by AI
  definitions: Definition[];         // Player's definition history
  usedWords: string[];              // All words used in definitions
  aiChallengeWords: string[];       // Words chosen by AI
  step: number;                     // Current step (0-based)
  maxSteps: number;                 // Maximum allowed steps
  gameStatus: GameStatus;           // Current game state
  difficulty: 'easy' | 'medium' | 'hard';
}
```

### **Definition**
```typescript
interface Definition {
  id: string;                       // Unique definition ID
  word: string;                     // Word being defined
  definition: string;               // Player's definition
  wordCount: number;                // Number of words in definition
  timestamp: number;                // When definition was created
}
```

### **AIResponse**
```typescript
interface AIResponse {
  accept: boolean;                  // Whether AI accepts definition
  reason?: string;                  // AI's reasoning
  nextWord?: string;                // Next word for player to define
  burnedTarget?: string;            // Target word burned by AI
  gameResult?: 'continue' | 'player_wins' | 'ai_wins';
  resultReason?: string;            // Reason for game end
  internalAnalysis: {
    playerConfidence: number;       // AI's confidence in player (0-100)
    remainingPaths: string[];       // Available target paths
    recommendedBurn: string | null; // Target AI recommends burning
    difficultyAdjustment: number;   // Difficulty modifier (-2 to +2)
  };
  debugInfo?: {                     // Debug information (dev mode only)
    aiRawResponse: string;
    winDetectionMethod: 'ai' | 'code' | 'hybrid' | 'fallback';
    winCheckResults: {
      directWin: boolean;
      semanticWin: boolean;
      aiDecision: string;
    };
    gameStateAnalysis: {
      remainingTargets: string[];
      burnedTargets: string[];
      currentWord: string;
      step: number;
      isImpossible: boolean;
    };
    timestamp: number;
  };
}
```

### **GameStatus**
```typescript
type GameStatus = 
  | 'waiting'      // Waiting for player input
  | 'processing'   // AI is thinking
  | 'won'          // Player won
  | 'lost';        // Player lost
```

---

## 🔧 **Game Rules Validation**

The API automatically validates all submissions against game rules:

### **Rule 1: Shorter Definitions**
Each definition must have fewer words than the previous one.

### **Rule 2: No Word Reuse**
Cannot reuse any word from previous definitions (includes similarity detection).

### **Rule 3: Valid Definitions**
Definitions must actually define the given word.

### **Rule 4: No Circular Logic**
Cannot use the word being defined in its own definition.

### **Rule 5: Reach Target**
Must eventually define one of the target words to win.

---

## 🐛 **Error Handling**

### **Validation Errors (422)**
```json
{
  "success": false,
  "error": "Definition violates game rules",
  "details": {
    "type": "reuse",
    "message": "Cannot reuse word: device",
    "word": "device"
  }
}
```

### **AI Processing Errors (500)**
```json
{
  "success": false,
  "error": "AI processing failed",
  "gameState": { /* fallback state */ }
}
```

---

## 🔍 **Development Features**

### **Debug Mode**
When `NODE_ENV=development`, responses include additional debug information:
- Raw AI responses
- Win detection analysis
- Performance metrics
- Detailed error traces

### **Fallback Behavior**
If AI is unavailable, the API falls back to:
- Simple word selection from word pool
- Basic rule validation
- Random target burning
- Graceful degradation

---

## 📈 **Rate Limiting**

- **Development**: No limits
- **Production**: 60 requests per minute per IP
- **Burst**: Up to 10 concurrent requests

---

## 🔐 **Security**

### **Input Validation**
- All inputs sanitized and validated
- Maximum definition length: 500 characters
- Word length limits: 1-50 characters
- XSS prevention on all text inputs

### **CORS Policy**
- Development: All origins allowed
- Production: Specific domain whitelist

---

*API version: 1.0.0 | Last updated: 2025-01-16*

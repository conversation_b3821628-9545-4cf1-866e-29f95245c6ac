import React, { createContext, useContext, useReducer, useCallback, useRef, useEffect } from 'react';
import { GameState, AnimationState } from '@/types/game';
import { UI_CONFIG } from '@/utils/constants';

// Animation Types
export type AnimationTrigger = 
  | 'game_start'
  | 'definition_submit'
  | 'definition_accepted'
  | 'definition_rejected'
  | 'target_burned'
  | 'target_completed'
  | 'game_won'
  | 'game_lost'
  | 'ai_thinking'
  | 'word_change'
  | 'side_panel_toggle';

export interface AnimationEvent {
  trigger: AnimationTrigger;
  duration?: number;
  delay?: number;
  data?: any;
  timestamp: number;
}

export interface AnimationContextState {
  currentAnimation: AnimationState;
  isAnimating: boolean;
  animationQueue: AnimationEvent[];
  gameStateAnimations: {
    wordChange: boolean;
    targetBurn: boolean;
    definitionSubmit: boolean;
    gameEnd: boolean;
  };
  preferences: {
    enableAnimations: boolean;
    reducedMotion: boolean;
    animationSpeed: number; // 0.5 to 2.0
  };
}

type AnimationAction = 
  | { type: 'TRIGGER_ANIMATION'; payload: AnimationEvent }
  | { type: 'COMPLETE_ANIMATION' }
  | { type: 'QUEUE_ANIMATION'; payload: AnimationEvent }
  | { type: 'CLEAR_QUEUE' }
  | { type: 'SET_PREFERENCES'; payload: Partial<AnimationContextState['preferences']> }
  | { type: 'UPDATE_GAME_ANIMATIONS'; payload: Partial<AnimationContextState['gameStateAnimations']> };

const initialState: AnimationContextState = {
  currentAnimation: 'idle',
  isAnimating: false,
  animationQueue: [],
  gameStateAnimations: {
    wordChange: false,
    targetBurn: false,
    definitionSubmit: false,
    gameEnd: false,
  },
  preferences: {
    enableAnimations: true,
    reducedMotion: false,
    animationSpeed: 1.0,
  },
};

function animationReducer(state: AnimationContextState, action: AnimationAction): AnimationContextState {
  switch (action.type) {
    case 'TRIGGER_ANIMATION':
      return {
        ...state,
        currentAnimation: getAnimationStateFromTrigger(action.payload.trigger),
        isAnimating: true,
        animationQueue: state.animationQueue.filter(event => event.timestamp !== action.payload.timestamp),
      };

    case 'COMPLETE_ANIMATION':
      const nextEvent = state.animationQueue[0];
      if (nextEvent) {
        return {
          ...state,
          currentAnimation: getAnimationStateFromTrigger(nextEvent.trigger),
          animationQueue: state.animationQueue.slice(1),
        };
      }
      return {
        ...state,
        currentAnimation: 'idle',
        isAnimating: false,
      };

    case 'QUEUE_ANIMATION':
      return {
        ...state,
        animationQueue: [...state.animationQueue, action.payload],
      };

    case 'CLEAR_QUEUE':
      return {
        ...state,
        animationQueue: [],
        currentAnimation: 'idle',
        isAnimating: false,
      };

    case 'SET_PREFERENCES':
      return {
        ...state,
        preferences: { ...state.preferences, ...action.payload },
      };

    case 'UPDATE_GAME_ANIMATIONS':
      return {
        ...state,
        gameStateAnimations: { ...state.gameStateAnimations, ...action.payload },
      };

    default:
      return state;
  }
}

function getAnimationStateFromTrigger(trigger: AnimationTrigger): AnimationState {
  switch (trigger) {
    case 'definition_submit':
    case 'ai_thinking':
      return 'thinking';
    case 'definition_accepted':
    case 'target_completed':
    case 'game_won':
      return 'success';
    case 'definition_rejected':
    case 'game_lost':
      return 'failure';
    case 'word_change':
      return 'typing';
    default:
      return 'idle';
  }
}

export interface AnimationContextType {
  state: AnimationContextState;
  triggerAnimation: (trigger: AnimationTrigger, data?: any, options?: { duration?: number; delay?: number }) => void;
  completeAnimation: () => void;
  queueAnimation: (trigger: AnimationTrigger, data?: any, options?: { duration?: number; delay?: number }) => void;
  clearAnimationQueue: () => void;
  setAnimationPreferences: (preferences: Partial<AnimationContextState['preferences']>) => void;
  updateGameAnimations: (animations: Partial<AnimationContextState['gameStateAnimations']>) => void;
  getAnimationDuration: (trigger: AnimationTrigger) => number;
}

const AnimationContext = createContext<AnimationContextType | undefined>(undefined);

export const useAnimation = (): AnimationContextType => {
  const context = useContext(AnimationContext);
  if (!context) {
    throw new Error('useAnimation must be used within an AnimationProvider');
  }
  return context;
};

interface AnimationProviderProps {
  children: React.ReactNode;
}

export const AnimationProvider: React.FC<AnimationProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(animationReducer, initialState);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handleChange = (e: MediaQueryListEvent) => {
      dispatch({
        type: 'SET_PREFERENCES',
        payload: { reducedMotion: e.matches }
      });
    };

    dispatch({
      type: 'SET_PREFERENCES',
      payload: { reducedMotion: mediaQuery.matches }
    });

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const getAnimationDuration = useCallback((trigger: AnimationTrigger): number => {
    const baseDuration = {
      'game_start': 800,
      'definition_submit': 300,
      'definition_accepted': 600,
      'definition_rejected': UI_CONFIG.SHAKE_DURATION_MS,
      'target_burned': 1000,
      'target_completed': 800,
      'game_won': 1200,
      'game_lost': 800,
      'ai_thinking': 2000,
      'word_change': 400,
      'side_panel_toggle': 300,
    }[trigger] || UI_CONFIG.ANIMATION_DURATION_MS;

    if (state.preferences.reducedMotion) {
      return 50; // Minimal duration for reduced motion
    }

    return baseDuration * state.preferences.animationSpeed;
  }, [state.preferences]);

  const triggerAnimation = useCallback((
    trigger: AnimationTrigger, 
    data?: any, 
    options?: { duration?: number; delay?: number }
  ) => {
    if (!state.preferences.enableAnimations) return;

    const event: AnimationEvent = {
      trigger,
      duration: options?.duration || getAnimationDuration(trigger),
      delay: options?.delay || 0,
      data,
      timestamp: Date.now(),
    };

    if (state.isAnimating) {
      dispatch({ type: 'QUEUE_ANIMATION', payload: event });
    } else {
      dispatch({ type: 'TRIGGER_ANIMATION', payload: event });
      
      // Auto-complete animation after duration
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        dispatch({ type: 'COMPLETE_ANIMATION' });
      }, (event.duration || 0) + (event.delay || 0));
    }
  }, [state.isAnimating, state.preferences.enableAnimations, getAnimationDuration]);

  const completeAnimation = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    dispatch({ type: 'COMPLETE_ANIMATION' });
  }, []);

  const queueAnimation = useCallback((
    trigger: AnimationTrigger, 
    data?: any, 
    options?: { duration?: number; delay?: number }
  ) => {
    const event: AnimationEvent = {
      trigger,
      duration: options?.duration || getAnimationDuration(trigger),
      delay: options?.delay || 0,
      data,
      timestamp: Date.now(),
    };
    dispatch({ type: 'QUEUE_ANIMATION', payload: event });
  }, [getAnimationDuration]);

  const clearAnimationQueue = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    dispatch({ type: 'CLEAR_QUEUE' });
  }, []);

  const setAnimationPreferences = useCallback((preferences: Partial<AnimationContextState['preferences']>) => {
    dispatch({ type: 'SET_PREFERENCES', payload: preferences });
  }, []);

  const updateGameAnimations = useCallback((animations: Partial<AnimationContextState['gameStateAnimations']>) => {
    dispatch({ type: 'UPDATE_GAME_ANIMATIONS', payload: animations });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const contextValue: AnimationContextType = {
    state,
    triggerAnimation,
    completeAnimation,
    queueAnimation,
    clearAnimationQueue,
    setAnimationPreferences,
    updateGameAnimations,
    getAnimationDuration,
  };

  return (
    <AnimationContext.Provider value={contextValue}>
      {children}
    </AnimationContext.Provider>
  );
};

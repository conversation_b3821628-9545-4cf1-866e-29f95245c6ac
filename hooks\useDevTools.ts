/**
 * useDevTools Hook - Development Tools State Management (v2.0)
 *
 * 🎯 CENTRALIZED DEVELOPMENT TOOLS STATE MANAGEMENT
 * 
 * Features:
 * - Development tools state (dev panel, performance dashboard)
 * - Performance monitoring and metrics collection
 * - Development-only state management
 * - Type-safe development operations
 * - Production build optimization
 * 
 * Performance Benefits:
 * - Development-only code elimination in production
 * - Optimized performance monitoring
 * - Efficient metrics collection
 * - Memory-conscious development tools
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_4_STATE_EXTRACTION.md
 */

import { useState, useCallback, useEffect, useRef } from 'react';

interface UseDevToolsOptions {
  enablePerformanceMonitoring?: boolean;
  enableDevPanel?: boolean;
  enablePerformanceDashboard?: boolean;
}

interface UseDevToolsReturn {
  // State
  showDevPanel: boolean;
  showPerformanceDashboard: boolean;
  isDevelopment: boolean;
  
  // State Setters
  setShowDevPanel: (show: boolean) => void;
  setShowPerformanceDashboard: (show: boolean) => void;
  
  // Development Operations
  toggleDevPanel: () => void;
  togglePerformanceDashboard: () => void;
  
  // Performance Monitoring
  trackPerformance: (label: string, operation: () => void) => void;
  getPerformanceMetrics: () => DevToolsMetrics;
  clearPerformanceMetrics: () => void;
  
  // Development Utilities
  logDebugInfo: (label: string, data: any) => void;
  measureRenderTime: (componentName: string) => () => void;
  
  // Batch Operations
  resetDevTools: () => void;
}

interface DevToolsMetrics {
  performanceEntries: PerformanceEntry[];
  renderTimes: Record<string, number[]>;
  memoryUsage: number;
  totalMeasurements: number;
  averageRenderTime: number;
}

interface PerformanceEntry {
  label: string;
  duration: number;
  timestamp: number;
}

const DEFAULT_DEV_TOOLS_STATE = {
  showDevPanel: false,
  showPerformanceDashboard: false
};

export function useDevTools(options: UseDevToolsOptions = {}): UseDevToolsReturn {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const { 
    enablePerformanceMonitoring = isDevelopment,
    enableDevPanel = isDevelopment,
    enablePerformanceDashboard = isDevelopment
  } = options;

  // Core State (only in development)
  const [devToolsState, setDevToolsStateInternal] = useState(() => ({
    showDevPanel: enableDevPanel,
    showPerformanceDashboard: enablePerformanceDashboard
  }));

  // Performance Monitoring (development only)
  const performanceEntriesRef = useRef<PerformanceEntry[]>([]);
  const renderTimesRef = useRef<Record<string, number[]>>({});
  const totalMeasurementsRef = useRef<number>(0);

  // Early return for production builds
  if (!isDevelopment) {
    return {
      // State
      showDevPanel: false,
      showPerformanceDashboard: false,
      isDevelopment: false,
      
      // State Setters (no-ops in production)
      setShowDevPanel: () => {},
      setShowPerformanceDashboard: () => {},
      
      // Development Operations (no-ops in production)
      toggleDevPanel: () => {},
      togglePerformanceDashboard: () => {},
      
      // Performance Monitoring (no-ops in production)
      trackPerformance: () => {},
      getPerformanceMetrics: () => ({
        performanceEntries: [],
        renderTimes: {},
        memoryUsage: 0,
        totalMeasurements: 0,
        averageRenderTime: 0
      }),
      clearPerformanceMetrics: () => {},
      
      // Development Utilities (no-ops in production)
      logDebugInfo: () => {},
      measureRenderTime: () => () => {},
      
      // Batch Operations (no-ops in production)
      resetDevTools: () => {}
    };
  }

  // Development-only implementations
  const updateDevToolsState = useCallback((updates: Partial<typeof devToolsState>) => {
    setDevToolsStateInternal(prevState => ({ ...prevState, ...updates }));
  }, []);

  const setShowDevPanel = useCallback((show: boolean) => {
    updateDevToolsState({ showDevPanel: show });
  }, [updateDevToolsState]);

  const setShowPerformanceDashboard = useCallback((show: boolean) => {
    updateDevToolsState({ showPerformanceDashboard: show });
  }, [updateDevToolsState]);

  const toggleDevPanel = useCallback(() => {
    updateDevToolsState({ showDevPanel: !devToolsState.showDevPanel });
  }, [updateDevToolsState, devToolsState.showDevPanel]);

  const togglePerformanceDashboard = useCallback(() => {
    updateDevToolsState({ 
      showPerformanceDashboard: !devToolsState.showPerformanceDashboard 
    });
  }, [updateDevToolsState, devToolsState.showPerformanceDashboard]);

  // Performance Monitoring
  const trackPerformance = useCallback((label: string, operation: () => void) => {
    if (!enablePerformanceMonitoring) {
      operation();
      return;
    }

    const startTime = performance.now();
    operation();
    const endTime = performance.now();
    
    const entry: PerformanceEntry = {
      label,
      duration: endTime - startTime,
      timestamp: Date.now()
    };
    
    performanceEntriesRef.current.push(entry);
    totalMeasurementsRef.current += 1;
    
    // Keep only last 1000 entries for memory efficiency
    if (performanceEntriesRef.current.length > 1000) {
      performanceEntriesRef.current = performanceEntriesRef.current.slice(-1000);
    }
    
    console.log(`⚡ Performance: ${label} took ${entry.duration.toFixed(2)}ms`);
  }, [enablePerformanceMonitoring]);

  const measureRenderTime = useCallback((componentName: string) => {
    if (!enablePerformanceMonitoring) {
      return () => {};
    }

    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      if (!renderTimesRef.current[componentName]) {
        renderTimesRef.current[componentName] = [];
      }
      
      renderTimesRef.current[componentName].push(renderTime);
      
      // Keep only last 100 render times per component
      if (renderTimesRef.current[componentName].length > 100) {
        renderTimesRef.current[componentName] = renderTimesRef.current[componentName].slice(-100);
      }
      
      console.log(`🎨 Render: ${componentName} rendered in ${renderTime.toFixed(2)}ms`);
    };
  }, [enablePerformanceMonitoring]);

  const getPerformanceMetrics = useCallback((): DevToolsMetrics => {
    // Calculate memory usage (rough estimate)
    const memoryUsage = performanceEntriesRef.current.length * 64 + // Rough estimate per entry
      Object.values(renderTimesRef.current).reduce((sum, times) => sum + times.length * 8, 0);

    // Calculate average render time
    const allRenderTimes = Object.values(renderTimesRef.current).flat();
    const averageRenderTime = allRenderTimes.length > 0
      ? allRenderTimes.reduce((sum, time) => sum + time, 0) / allRenderTimes.length
      : 0;

    return {
      performanceEntries: [...performanceEntriesRef.current],
      renderTimes: { ...renderTimesRef.current },
      memoryUsage,
      totalMeasurements: totalMeasurementsRef.current,
      averageRenderTime
    };
  }, []);

  const clearPerformanceMetrics = useCallback(() => {
    performanceEntriesRef.current = [];
    renderTimesRef.current = {};
    totalMeasurementsRef.current = 0;
    console.log('🧹 Performance metrics cleared');
  }, []);

  // Development Utilities
  const logDebugInfo = useCallback((label: string, data: any) => {
    if (!isDevelopment) return;
    
    console.group(`🔍 Debug: ${label}`);
    console.log(data);
    console.groupEnd();
  }, [isDevelopment]);

  // Batch Operations
  const resetDevTools = useCallback(() => {
    updateDevToolsState(DEFAULT_DEV_TOOLS_STATE);
    clearPerformanceMetrics();
  }, [updateDevToolsState, clearPerformanceMetrics]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear performance data on unmount
      performanceEntriesRef.current = [];
      renderTimesRef.current = {};
      totalMeasurementsRef.current = 0;
    };
  }, []);

  // Development logging
  useEffect(() => {
    if (enablePerformanceMonitoring) {
      console.log('🔧 useDevTools: State updated', {
        showDevPanel: devToolsState.showDevPanel,
        showPerformanceDashboard: devToolsState.showPerformanceDashboard,
        totalMeasurements: totalMeasurementsRef.current
      });
    }
  }, [devToolsState, enablePerformanceMonitoring]);

  // Keyboard shortcuts for development tools
  useEffect(() => {
    if (!isDevelopment) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + D to toggle dev panel
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        toggleDevPanel();
      }
      
      // Ctrl/Cmd + Shift + P to toggle performance dashboard
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'P') {
        event.preventDefault();
        togglePerformanceDashboard();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isDevelopment, toggleDevPanel, togglePerformanceDashboard]);

  return {
    // State
    showDevPanel: devToolsState.showDevPanel,
    showPerformanceDashboard: devToolsState.showPerformanceDashboard,
    isDevelopment,
    
    // State Setters
    setShowDevPanel,
    setShowPerformanceDashboard,
    
    // Development Operations
    toggleDevPanel,
    togglePerformanceDashboard,
    
    // Performance Monitoring
    trackPerformance,
    getPerformanceMetrics,
    clearPerformanceMetrics,
    
    // Development Utilities
    logDebugInfo,
    measureRenderTime,
    
    // Batch Operations
    resetDevTools
  };
}

export default useDevTools;

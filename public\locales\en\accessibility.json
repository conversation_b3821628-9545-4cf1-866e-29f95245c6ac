{"skipLinks": {"mainContent": "Skip to main content", "navigation": "Skip to navigation", "gameInput": "Skip to game input", "gameControls": "Skip to game controls", "sidePanel": "Skip to side panel", "footer": "Skip to footer", "search": "Skip to search", "help": "Skip to help"}, "landmarks": {"banner": "Site header", "navigation": "Main navigation", "main": "Main content", "complementary": "Sidebar content", "contentinfo": "Site footer", "search": "Search", "form": "Form", "region": "Content region"}, "headings": {"level1": "Main heading", "level2": "Section heading", "level3": "Subsection heading", "level4": "Sub-subsection heading", "level5": "Minor heading", "level6": "Smallest heading"}, "buttons": {"expand": "Expand {{item}}", "collapse": "Collapse {{item}}", "toggle": "Toggle {{item}}", "open": "Open {{item}}", "close": "Close {{item}}", "show": "Show {{item}}", "hide": "Hide {{item}}", "play": "Play {{item}}", "pause": "Pause {{item}}", "stop": "Stop {{item}}", "next": "Next {{item}}", "previous": "Previous {{item}}", "first": "First {{item}}", "last": "Last {{item}}", "submit": "Submit {{item}}", "cancel": "Cancel {{item}}", "save": "Save {{item}}", "delete": "Delete {{item}}", "edit": "Edit {{item}}", "copy": "Copy {{item}}", "share": "Share {{item}}", "download": "Download {{item}}", "upload": "Upload {{item}}", "refresh": "Refresh {{item}}", "reload": "Reload {{item}}", "reset": "Reset {{item}}", "clear": "Clear {{item}}", "select": "Select {{item}}", "deselect": "Deselect {{item}}", "check": "Check {{item}}", "uncheck": "Uncheck {{item}}", "enable": "Enable {{item}}", "disable": "Disable {{item}}", "activate": "Activate {{item}}", "deactivate": "Deactivate {{item}}"}, "states": {"expanded": "Expanded", "collapsed": "Collapsed", "selected": "Selected", "unselected": "Not selected", "checked": "Checked", "unchecked": "Not checked", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "visible": "Visible", "hidden": "Hidden", "open": "Open", "closed": "Closed", "loading": "Loading", "loaded": "Loaded", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "valid": "<PERSON><PERSON>", "invalid": "Invalid", "current": "Current", "new": "New", "updated": "Updated", "deleted": "Deleted"}, "forms": {"required": "Required field", "optional": "Optional field", "invalid": "Invalid input", "valid": "Valid input", "error": "Input error: {{message}}", "help": "Help text: {{message}}", "placeholder": "Example: {{example}}", "format": "Expected format: {{format}}", "length": "{{current}} of {{max}} characters", "remaining": "{{remaining}} characters remaining", "exceeded": "{{exceeded}} characters over limit", "minLength": "Minimum {{min}} characters", "maxLength": "Maximum {{max}} characters", "pattern": "Must match pattern: {{pattern}}", "email": "Must be a valid email address", "url": "Must be a valid URL", "number": "Must be a number", "integer": "Must be a whole number", "decimal": "Must be a decimal number", "positive": "Must be positive", "negative": "Must be negative", "range": "Must be between {{min}} and {{max}}", "date": "Must be a valid date", "time": "Must be a valid time", "password": "Password requirements: {{requirements}}"}, "navigation": {"breadcrumb": "Breadcrumb navigation", "pagination": "Pagination navigation", "tabs": "Tab navigation", "menu": "Menu navigation", "tree": "Tree navigation", "current": "Current page: {{page}}", "page": "Page {{number}} of {{total}}", "item": "Item {{number}} of {{total}}", "level": "Level {{level}}", "parent": "Parent: {{parent}}", "child": "Child of {{parent}}", "sibling": "Sibling of {{sibling}}", "first": "First item", "last": "Last item", "next": "Next: {{next}}", "previous": "Previous: {{previous}}"}, "tables": {"caption": "Table: {{title}}", "summary": "Table with {{rows}} rows and {{columns}} columns", "header": "Column header", "cell": "Table cell", "row": "Row {{number}}", "column": "Column {{number}}", "sortable": "Sortable column", "sorted": "Sorted by {{column}} {{direction}}", "ascending": "ascending", "descending": "descending", "unsorted": "Not sorted", "filter": "Filter by {{column}}", "filtered": "Filtered by {{filter}}", "empty": "No data available", "loading": "Loading table data", "error": "Error loading table data"}, "lists": {"ordered": "Ordered list", "unordered": "Unordered list", "definition": "Definition list", "item": "List item {{number}}", "term": "Definition term", "description": "Definition description", "nested": "Nested list", "empty": "Empty list", "count": "{{count}} items"}, "media": {"image": "Image: {{alt}}", "video": "Video: {{title}}", "audio": "Audio: {{title}}", "iframe": "Embedded content: {{title}}", "canvas": "Interactive canvas: {{description}}", "svg": "Vector graphic: {{description}}", "map": "Image map: {{description}}", "chart": "Chart: {{description}}", "graph": "Graph: {{description}}", "diagram": "Diagram: {{description}}", "loading": "Loading media content", "error": "Error loading media", "unavailable": "Media unavailable", "transcript": "Transcript available", "captions": "Captions available", "description": "Audio description available"}, "dialogs": {"modal": "Modal dialog", "alert": "Alert dialog", "confirm": "Confirmation dialog", "prompt": "Input dialog", "tooltip": "<PERSON><PERSON><PERSON>", "popover": "Popover", "dropdown": "Dropdown menu", "combobox": "Combobox", "listbox": "List box", "tree": "Tree view", "grid": "Data grid", "tabpanel": "Tab panel", "toolbar": "<PERSON><PERSON><PERSON>", "menubar": "Menu bar", "status": "Status message", "log": "Activity log", "marquee": "Scrolling text", "timer": "Timer", "progressbar": "Progress indicator"}, "feedback": {"loading": "Content is loading, please wait", "loaded": "Content has finished loading", "error": "An error occurred: {{message}}", "success": "Action completed successfully: {{message}}", "warning": "Warning: {{message}}", "info": "Information: {{message}}", "updated": "Content has been updated", "saved": "Changes have been saved", "deleted": "Item has been deleted", "created": "Item has been created", "copied": "Content copied to clipboard", "shared": "Content has been shared", "sent": "Message has been sent", "received": "New message received", "connected": "Connection established", "disconnected": "Connection lost", "online": "You are online", "offline": "You are offline"}, "instructions": {"keyboard": "Use arrow keys to navigate, Enter to select, Escape to close", "mouse": "Click to select, double-click to activate", "touch": "Tap to select, double-tap to activate", "voice": "Say 'select' followed by the item name", "gesture": "Swipe left or right to navigate, tap to select", "shortcut": "Keyboard shortcut: {{keys}}", "alternative": "Alternative: {{method}}", "help": "Press F1 for help, or say 'help' for voice assistance"}, "game": {"currentWord": "Current word to define: {{word}}", "targetWords": "Target words: {{targets}}", "step": "Step {{current}} of {{total}}", "wordsLeft": "{{count}} words remaining in definition", "definition": "Your definition: {{text}}", "aiResponse": "AI response: {{response}}", "victory": "Congratulations! You won the game", "defeat": "Game over. You were defeated", "burned": "Target word burned: {{word}}", "progress": "Game progress: {{percentage}}% complete", "hint": "Hint: {{message}}", "warning": "Warning: {{message}}", "error": "Game error: {{message}}", "thinking": "AI is thinking about your move", "waiting": "Waiting for your input", "invalid": "Invalid move: {{reason}}", "accepted": "Move accepted", "rejected": "Move rejected: {{reason}}"}}
import { GameError, GameErrorCode, APIErrorResponse } from '@/types/game';

/**
 * Enhanced Error Handling Utilities
 * Provides consistent error handling across the application
 */

export class GameErrorHandler {
  /**
   * Creates a standardized game error
   */
  static createError(
    code: GameErrorCode,
    message: string,
    details?: Record<string, any>,
    recoverable: boolean = true
  ): GameError {
    return {
      code,
      message,
      details,
      timestamp: Date.now(),
      recoverable
    };
  }

  /**
   * Handles API errors with proper typing and logging
   */
  static handleAPIError(error: unknown, context: string): APIErrorResponse {
    console.error(`API Error in ${context}:`, error);

    if (error instanceof Error) {
      // Network/fetch errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return {
          success: false,
          error: 'Network connection failed. Please check your internet connection.',
          errorCode: 'NETWORK_ERROR',
          retryable: true
        };
      }

      // Timeout errors
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        return {
          success: false,
          error: 'Request timed out. Please try again.',
          errorCode: 'AI_TIMEOUT',
          retryable: true
        };
      }

      // Validation errors
      if (error.message.includes('validation') || error.message.includes('invalid')) {
        return {
          success: false,
          error: error.message,
          errorCode: 'VALIDATION_ERROR',
          retryable: false
        };
      }

      return {
        success: false,
        error: error.message,
        errorCode: 'UNKNOWN_ERROR',
        retryable: true
      };
    }

    return {
      success: false,
      error: 'An unexpected error occurred',
      errorCode: 'UNKNOWN_ERROR',
      retryable: true
    };
  }

  /**
   * Safely handles async operations with proper error catching
   */
  static async safeAsync<T>(
    operation: () => Promise<T>,
    context: string,
    fallback?: T
  ): Promise<{ success: true; data: T } | { success: false; error: GameError }> {
    try {
      const data = await operation();
      return { success: true, data };
    } catch (error) {
      const gameError = this.createError(
        'UNKNOWN_ERROR',
        `Failed to execute ${context}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { originalError: error, context },
        true
      );

      console.error(`Safe async operation failed in ${context}:`, error);
      return { success: false, error: gameError };
    }
  }

  /**
   * Validates and sanitizes user input with error handling
   */
  static validateInput(input: string, maxLength: number = 200): {
    isValid: boolean;
    sanitized?: string;
    error?: GameError;
  } {
    try {
      if (typeof input !== 'string') {
        return {
          isValid: false,
          error: this.createError(
            'INPUT_SANITIZATION_ERROR',
            'Input must be a string',
            { inputType: typeof input },
            false
          )
        };
      }

      const sanitized = input
        .trim()
        .replace(/[<>]/g, '') // Remove HTML tags
        .replace(/script/gi, '') // Remove script keywords
        .replace(/javascript/gi, '') // Remove javascript keywords
        .replace(/[^\w\s'-]/g, '') // Only allow safe characters
        .replace(/\s+/g, ' ') // Normalize whitespace
        .substring(0, maxLength);

      if (!sanitized || sanitized.length < 2) {
        return {
          isValid: false,
          error: this.createError(
            'VALIDATION_ERROR',
            'Input must be at least 2 characters and contain valid words',
            { originalLength: input.length, sanitizedLength: sanitized.length },
            false
          )
        };
      }

      return {
        isValid: true,
        sanitized
      };
    } catch (error) {
      return {
        isValid: false,
        error: this.createError(
          'INPUT_SANITIZATION_ERROR',
          'Failed to sanitize input',
          { originalError: error },
          false
        )
      };
    }
  }

  /**
   * Logs errors with proper context and formatting
   */
  static logError(error: GameError | Error | unknown, context?: string): void {
    const timestamp = new Date().toISOString();
    const contextStr = context ? ` [${context}]` : '';

    if (this.isGameError(error)) {
      console.error(`${timestamp}${contextStr} GameError [${error.code}]:`, {
        message: error.message,
        details: error.details,
        recoverable: error.recoverable,
        timestamp: error.timestamp
      });
    } else if (error instanceof Error) {
      console.error(`${timestamp}${contextStr} Error:`, {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    } else {
      console.error(`${timestamp}${contextStr} Unknown Error:`, error);
    }

    // In production, you might want to send this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: sendToErrorReportingService(error, context);
    }
  }

  /**
   * Type guard for GameError
   */
  static isGameError(error: unknown): error is GameError {
    return (
      typeof error === 'object' &&
      error !== null &&
      'code' in error &&
      'message' in error &&
      'timestamp' in error &&
      'recoverable' in error
    );
  }

  /**
   * Determines if an error is recoverable
   */
  static isRecoverable(error: unknown): boolean {
    if (this.isGameError(error)) {
      return error.recoverable;
    }

    if (error instanceof Error) {
      // Network errors are usually recoverable
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return true;
      }
      
      // Timeout errors are recoverable
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        return true;
      }

      // Validation errors are usually not recoverable without user action
      if (error.message.includes('validation') || error.message.includes('invalid')) {
        return false;
      }
    }

    // Default to recoverable for unknown errors
    return true;
  }

  /**
   * Creates user-friendly error messages
   */
  static getUserMessage(error: unknown): string {
    if (this.isGameError(error)) {
      return error.message;
    }

    if (error instanceof Error) {
      // Network errors
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        return 'Unable to connect to the game server. Please check your internet connection and try again.';
      }

      // Timeout errors
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        return 'The request took too long to complete. Please try again.';
      }

      // Return the error message for other known errors
      return error.message;
    }

    return 'An unexpected error occurred. Please try again.';
  }
}

/**
 * Retry utility with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  context: string = 'operation'
): Promise<T> {
  let lastError: unknown;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        GameErrorHandler.logError(error, `${context} - Final attempt failed`);
        throw error;
      }

      // Don't retry non-recoverable errors
      if (!GameErrorHandler.isRecoverable(error)) {
        GameErrorHandler.logError(error, `${context} - Non-recoverable error, not retrying`);
        throw error;
      }

      const delay = baseDelay * Math.pow(2, attempt - 1);
      GameErrorHandler.logError(error, `${context} - Attempt ${attempt} failed, retrying in ${delay}ms`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

/**
 * Debounce utility for preventing rapid successive calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Game-Specific Error Boundary
 * 
 * Specialized error boundary for game components with game-specific
 * error handling, recovery strategies, and user messaging.
 */

import React from 'react';
import ErrorBoundary from './ErrorBoundary';
import { GameErrorHandler } from '@/utils/errorHandling';

interface GameErrorBoundaryProps {
  children: React.ReactNode;
  gameState?: any;
  onGameError?: (error: Error) => void;
  fallbackComponent?: React.ComponentType<{ error: Error; retry: () => void }>;
}

const GameErrorFallback: React.FC<{ error: Error; retry: () => void }> = ({ error, retry }) => {
  const isGameCritical = error.message.includes('game') || error.message.includes('AI') || error.message.includes('definition');
  
  return (
    <div className="game-error-boundary">
      <div className="game-error-container">
        <div className="game-error-icon">🎮</div>
        <h2 className="game-error-title">
          {isGameCritical ? 'Game Error' : 'Something went wrong'}
        </h2>
        <p className="game-error-message">
          {isGameCritical 
            ? 'We encountered an issue with the game. Your progress is safe.'
            : 'A temporary issue occurred. You can continue playing.'}
        </p>
        
        <div className="game-error-actions">
          <button onClick={retry} className="btn-primary">
            Continue Game
          </button>
          <button 
            onClick={() => window.location.reload()} 
            className="btn-secondary"
          >
            Restart Game
          </button>
        </div>
      </div>

      <style jsx>{`
        .game-error-boundary {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 300px;
          padding: 24px;
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
          border: 2px solid rgba(59, 130, 246, 0.2);
          border-radius: 16px;
          margin: 16px;
        }

        .game-error-container {
          text-align: center;
          max-width: 400px;
        }

        .game-error-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }

        .game-error-title {
          font-size: 24px;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0 0 12px 0;
        }

        .game-error-message {
          font-size: 16px;
          color: var(--text-secondary);
          margin: 0 0 24px 0;
          line-height: 1.5;
        }

        .game-error-actions {
          display: flex;
          gap: 12px;
          justify-content: center;
          flex-wrap: wrap;
        }

        .btn-primary, .btn-secondary {
          padding: 12px 24px;
          border-radius: 8px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          border: none;
          font-size: 14px;
          min-width: 120px;
        }

        .btn-primary {
          background: var(--accent-cyan);
          color: var(--bg-primary);
        }

        .btn-primary:hover {
          background: var(--accent-cyan-hover);
          transform: translateY(-1px);
        }

        .btn-secondary {
          background: var(--glass-light);
          color: var(--text-primary);
          border: 1px solid var(--glass-border);
        }

        .btn-secondary:hover {
          background: var(--glass-medium);
        }

        @media (max-width: 640px) {
          .game-error-boundary {
            margin: 8px;
            padding: 16px;
          }

          .game-error-actions {
            flex-direction: column;
          }
        }
      `}</style>
    </div>
  );
};

export const GameErrorBoundary: React.FC<GameErrorBoundaryProps> = ({
  children,
  gameState,
  onGameError,
  fallbackComponent: FallbackComponent = GameErrorFallback
}) => {
  const handleGameError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Log game-specific error context
    GameErrorHandler.logError(error, 'GameErrorBoundary');
    
    // Call custom error handler if provided
    if (onGameError) {
      try {
        onGameError(error);
      } catch (handlerError) {
        console.error('Error in game error handler:', handlerError);
      }
    }

    // Log game state for debugging
    if (gameState && process.env.NODE_ENV === 'development') {
      console.log('Game state at error:', gameState);
    }
  };

  return (
    <ErrorBoundary
      level="component"
      onError={handleGameError}
      resetKeys={gameState ? [gameState.currentWord, gameState.step] : undefined}
      fallback={<FallbackComponent error={new Error('Game component error')} retry={() => window.location.reload()} />}
    >
      {children}
    </ErrorBoundary>
  );
};

export default GameErrorBoundary;

/**
 * useGameBoardState Hook - Unified State Management Orchestrator
 *
 * Consolidates and optimizes the 5 custom hooks used in GameBoard.tsx:
 * - useGameState: Core game state management
 * - useUIState: UI state and animations
 * - useGameControls: Game controls and difficulty
 * - useSpatialLayout: Layout and side panel state
 * - usePerformanceMonitoring: Performance and development tools
 *
 * This orchestrator reduces complexity, eliminates prop drilling, and provides
 * a unified interface for all GameBoard state management needs.
 */

import { useEffect } from 'react';
import { GameState, DifficultyLevel } from '@/types/game';

// Individual hooks
import useGameState from '@/hooks/useGameState';
import useUIState from '@/hooks/useUIState';
import useGameControls from '@/hooks/useGameControls';
import useSpatialLayout from '@/hooks/useSpatialLayout';
import usePerformanceMonitoring from '@/hooks/usePerformanceMonitoring';
import { useGameAnimations } from '@/hooks/useGameAnimations';
import useGameLogic from '@/hooks/useGameLogic';
import { useARIALabels } from '@/components/accessibility/ARIAEnhancer';

interface UseGameBoardStateProps {
  initialGameState?: GameState | null;
  enablePerformanceMonitoring?: boolean;
  enableDevTools?: boolean;
}

interface UseGameBoardStateReturn {
  // Core Game State
  gameState: GameState | null;
  optimizedGameState: GameState | null;
  lastAIResponse: any;
  error: string | null;
  isGameOver: boolean;

  // UI State
  uiState: any;
  wordCount: number;
  isInputValid: boolean;

  // Game Controls
  selectedDifficulty: DifficultyLevel;
  showDifficultySelector: boolean;
  showPostGameAnalysis: boolean;

  // Layout State
  showSidePanel: boolean;
  sidePanelTab: any;
  isResponsiveMode: boolean;

  // Performance & Development
  isLowEnd: boolean;
  showDevPanel: boolean;
  showPerformanceDashboard: boolean;
  isDevelopment: boolean;

  // Game Logic Functions
  startNewGame: () => void;
  submitDefinition: (definition: string) => void;
  handleImmediateInputChange: (value: string) => void;

  // UI Control Functions
  setShowDifficultySelector: (show: boolean) => void;
  setSelectedDifficulty: (difficulty: DifficultyLevel) => void;
  updateUIState: (updates: any) => void;
  toggleRules: () => void;
  clearError: () => void;

  // Layout Control Functions
  setShowSidePanel: (show: boolean) => void;
  toggleSidePanel: () => void;

  // Game Control Functions
  openPostGameAnalysis: () => void;
  setShowPostGameAnalysis: (show: boolean) => void;

  // Development Functions
  setShowDevPanel: (show: boolean) => void;
  setShowPerformanceDashboard: (show: boolean) => void;

  // Accessibility
  ariaLabels: any;

  // Animation System
  gameAnimations: any;
}

export const useGameBoardState = ({
  initialGameState = null,
  enablePerformanceMonitoring = process.env.NODE_ENV === 'development',
  enableDevTools = process.env.NODE_ENV === 'development'
}: UseGameBoardStateProps = {}): UseGameBoardStateReturn => {

  // Core Game State Management
  const {
    gameState,
    lastAIResponse,
    error,
    setGameState,
    setLastAIResponse,
    setError,
    clearError,
    batchUpdateGameState
  } = useGameState({
    initialGameState
  });

  // UI State Management
  const {
    uiState,
    updateUIState,
    setInputValue,
    setAnimationState,
    toggleRules,
    batchUpdateUI,
    isInputValid,
    wordCount
  } = useUIState({
    debounceDelay: 300
  });

  // Game Controls Management
  const {
    selectedDifficulty,
    showDifficultySelector,
    showPostGameAnalysis,
    setSelectedDifficulty,
    setShowDifficultySelector,
    setShowPostGameAnalysis,
    openPostGameAnalysis
  } = useGameControls({
    initialDifficulty: 'medium',
    persistPreferences: true
  });

  // Layout Management
  const {
    showSidePanel,
    sidePanelTab,
    isResponsiveMode,
    setShowSidePanel,
    toggleSidePanel
  } = useSpatialLayout({
    enableAnimations: true,
    responsiveBreakpoint: 768
  });

  // Performance Monitoring & Development Tools
  const {
    isLowEnd,
    optimizedGameState,
    showDevPanel,
    showPerformanceDashboard,
    isDevelopment,
    setShowDevPanel,
    setShowPerformanceDashboard
  } = usePerformanceMonitoring({
    gameState,
    componentName: 'GameBoard',
    enablePerformanceMonitoring,
    enableDevPanel: enableDevTools,
    enablePerformanceDashboard: enableDevTools
  });

  // Animation System
  const gameAnimations = useGameAnimations(gameState, {
    enableWordChangeAnimation: !isLowEnd,
    enableTargetBurnAnimation: !isLowEnd,
    enableDefinitionAnimation: true,
    enableGameEndAnimation: true,
    animationDelay: isLowEnd ? 50 : 100,
  });

  // Game Logic
  const {
    startNewGame,
    submitDefinition,
    handleImmediateInputChange
  } = useGameLogic({
    gameState,
    optimizedGameState,
    selectedDifficulty,
    setGameState,
    setLastAIResponse,
    setError,
    clearError,
    batchUpdateUI,
    setAnimationState,
    gameAnimations,
    setShowDifficultySelector,
    setInputValue,
    isLowEnd
  });

  // Accessibility
  const ariaLabels = useARIALabels(gameState || undefined);

  // Derived state
  const isGameOver = gameState?.gameStatus === 'won' || gameState?.gameStatus === 'lost';

  // Initialize game on mount
  useEffect(() => {
    if (!gameState) {
      startNewGame();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    // Core Game State
    gameState,
    optimizedGameState,
    lastAIResponse,
    error,
    isGameOver,

    // UI State
    uiState,
    wordCount,
    isInputValid,

    // Game Controls
    selectedDifficulty,
    showDifficultySelector,
    showPostGameAnalysis,

    // Layout State
    showSidePanel,
    sidePanelTab,
    isResponsiveMode,

    // Performance & Development
    isLowEnd,
    showDevPanel,
    showPerformanceDashboard,
    isDevelopment,

    // Game Logic Functions
    startNewGame,
    submitDefinition,
    handleImmediateInputChange,

    // UI Control Functions
    setShowDifficultySelector,
    setSelectedDifficulty,
    updateUIState,
    toggleRules,
    clearError,

    // Layout Control Functions
    setShowSidePanel,
    toggleSidePanel,

    // Game Control Functions
    openPostGameAnalysis,
    setShowPostGameAnalysis,

    // Development Functions
    setShowDevPanel,
    setShowPerformanceDashboard,

    // Accessibility
    ariaLabels,

    // Animation System
    gameAnimations
  };
};

export default useGameBoardState;
/**
 * GameFocus Component - Central Gameplay Area (v3.0 CSS Module)
 *
 * 🎯 PRIMARY FOCUS AREA - Takes 70% of viewport space
 *
 * Features:
 * - Zero-friction spatial layout with natural content flow
 * - Responsive gap-based spacing (no containers/margins)
 * - Optimized for hero typography and large interactive elements
 * - Progressive disclosure with contextual show/hide logic
 * - Accessibility-first design with proper focus management
 *
 * Space Allocation:
 * - Mobile: 85% of viewport (compact)
 * - Tablet: 75% of viewport (balanced)
 * - Desktop: 70% of viewport (spacious)
 *
 * @version 3.0 - CSS Module Migration (Performance Optimized)
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React from 'react';

interface GameFocusProps {
  children: React.ReactNode;
  className?: string;
  priority?: 'hero' | 'standard' | 'compact';
  showDebugGrid?: boolean;
}

export const GameFocus: React.FC<GameFocusProps> = ({
  children,
  className = '',
  priority = 'standard',
  showDebugGrid = false
}) => {
  const focusClasses = [
    'spatial-focus',
    `spatial-focus--${priority}`,
    showDebugGrid ? 'spatial-focus--debug' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={focusClasses} role="main" aria-label="Game focus area">
      {children}

      <style jsx>{`
        /* === SPATIAL FOCUS CONTAINER === */
        .spatial-focus {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: flex-start;
          gap: var(--space-8);
          width: 100%;
          max-width: var(--content-max);
          margin: 0 auto;
          padding: var(--space-6);
          min-height: 60vh;
        }

        /* === PRIORITY VARIANTS === */
        .spatial-focus--hero {
          gap: var(--space-12);
          padding: var(--space-8);
          min-height: 70vh;
        }

        .spatial-focus--standard {
          gap: var(--space-8);
          padding: var(--space-6);
          min-height: 60vh;
        }

        .spatial-focus--compact {
          gap: var(--space-4);
          padding: var(--space-4);
          min-height: 50vh;
        }

        /* === DEBUG GRID === */
        .spatial-focus--debug {
          outline: 2px dashed var(--accent-cyan);
          outline-offset: 4px;
          background: rgba(6, 182, 212, 0.05);
        }

        .spatial-focus--debug::before {
          content: 'GameFocus (${priority})';
          position: absolute;
          top: 0;
          left: 0;
          background: var(--accent-cyan);
          color: var(--color-background);
          padding: var(--space-1) var(--space-2);
          font-size: 0.75rem;
          font-weight: 600;
          z-index: var(--z-notification);
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .spatial-focus {
            gap: var(--space-4);
            padding: var(--space-4);
            min-height: 50vh;
          }

          .spatial-focus--hero {
            gap: var(--space-6);
            padding: var(--space-6);
            min-height: 60vh;
          }

          .spatial-focus--compact {
            gap: var(--space-3);
            padding: var(--space-3);
            min-height: 40vh;
          }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
          .spatial-focus {
            gap: var(--space-6);
            padding: var(--space-5);
          }

          .spatial-focus--hero {
            gap: var(--space-10);
            padding: var(--space-7);
          }
        }

        @media (min-width: 1024px) {
          .spatial-focus {
            gap: var(--space-10);
            padding: var(--space-8);
            min-height: 70vh;
          }

          .spatial-focus--hero {
            gap: var(--space-16);
            padding: var(--space-12);
            min-height: 80vh;
          }

          .spatial-focus--compact {
            gap: var(--space-6);
            padding: var(--space-6);
            min-height: 60vh;
          }
        }
      `}</style>
    </div>
  );
};


export default GameFocus;

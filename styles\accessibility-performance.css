/**
 * Accessibility Performance Optimization CSS
 * 
 * Provides performance optimizations for users with disabilities
 * and low-end devices while maintaining accessibility compliance.
 */

/* === REDUCED MOTION PREFERENCES === */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Disable specific animations that might cause issues */
  .floating-chat-widget,
  .chat-panel,
  .skip-link,
  .game-animation,
  .word-flip,
  .progress-animation {
    animation: none !important;
    transition: none !important;
  }

  /* Keep essential focus indicators */
  :focus,
  :focus-visible {
    transition: outline 0.1s ease !important;
  }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
  :root {
    --glass-light: rgba(255, 255, 255, 0.95);
    --glass-medium: rgba(255, 255, 255, 0.9);
    --glass-heavy: rgba(255, 255, 255, 0.95);
    --glass-border: #ffffff;
  }

  /* Remove subtle effects that don't work in high contrast */
  .no-shadows *,
  .no-gradients * {
    box-shadow: none !important;
    background-image: none !important;
    backdrop-filter: none !important;
  }

  /* Ensure strong borders for all interactive elements */
  button,
  input,
  textarea,
  select,
  .btn-primary,
  .btn-secondary {
    border: 2px solid currentColor !important;
  }

  /* High contrast focus indicators */
  :focus,
  :focus-visible {
    outline: 3px solid currentColor !important;
    outline-offset: 2px !important;
  }
}

/* === FORCED COLORS MODE (Windows High Contrast) === */
@media (forced-colors: active) {
  /* Use system colors */
  :root {
    --text-primary: CanvasText;
    --text-secondary: CanvasText;
    --text-muted: GrayText;
    --bg-primary: Canvas;
    --bg-secondary: Canvas;
    --accent-cyan: Highlight;
    --color-primary: Highlight;
    --color-error: CanvasText;
    --glass-border: CanvasText;
  }

  /* Remove all custom backgrounds and shadows */
  * {
    background-image: none !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
  }

  /* Ensure all interactive elements have borders */
  button,
  input,
  textarea,
  select,
  .card,
  .panel {
    border: 1px solid CanvasText !important;
  }

  /* Focus indicators */
  :focus,
  :focus-visible {
    outline: 2px solid Highlight !important;
    outline-offset: 1px !important;
  }
}

/* === REDUCED TRANSPARENCY === */
@media (prefers-reduced-transparency: reduce) {
  .no-blur * {
    backdrop-filter: none !important;
    background: var(--bg-primary) !important;
  }

  /* Make glass elements solid */
  .glass-light,
  .glass-medium,
  .glass-heavy {
    background: var(--bg-primary) !important;
    backdrop-filter: none !important;
  }
}

/* === LOW-END DEVICE OPTIMIZATIONS === */

/* Disable expensive effects on low-end devices */
.no-animations * {
  animation: none !important;
  transform: none !important;
}

.no-transitions * {
  transition: none !important;
}

.no-blur * {
  backdrop-filter: none !important;
  filter: none !important;
}

.no-shadows * {
  box-shadow: none !important;
  text-shadow: none !important;
}

.no-gradients * {
  background-image: none !important;
}

/* === PERFORMANCE-OPTIMIZED COMPONENTS === */

/* Simplified button styles for low-end devices */
.performance-optimized .btn-primary,
.performance-optimized .btn-secondary {
  background: var(--color-primary);
  border: 2px solid var(--color-primary);
  color: var(--text-on-primary);
  transition: none;
}

.performance-optimized .btn-primary:hover,
.performance-optimized .btn-secondary:hover {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

/* Simplified card styles */
.performance-optimized .card {
  background: var(--bg-secondary);
  border: 1px solid var(--glass-border);
  box-shadow: none;
  backdrop-filter: none;
}

/* Simplified input styles */
.performance-optimized input,
.performance-optimized textarea {
  background: var(--bg-primary);
  border: 2px solid var(--glass-border);
  box-shadow: none;
  transition: none;
}

.performance-optimized input:focus,
.performance-optimized textarea:focus {
  border-color: var(--accent-cyan);
  outline: 2px solid var(--accent-cyan);
  outline-offset: 1px;
}

/* === SCREEN READER OPTIMIZATIONS === */

/* Ensure screen reader content is properly hidden but accessible */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Screen reader focus styles */
.sr-only:focus {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: 0.5rem !important;
  margin: 0 !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
  background: var(--bg-primary) !important;
  border: 2px solid var(--accent-cyan) !important;
  color: var(--text-primary) !important;
}

/* === MEMORY OPTIMIZATION === */

/* Reduce memory usage for low-end devices */
.memory-optimized * {
  /* Disable will-change to reduce memory usage */
  will-change: auto !important;
}

.memory-optimized img,
.memory-optimized video {
  /* Optimize image rendering */
  image-rendering: optimizeSpeed;
}

/* === PRINT STYLES === */
@media print {
  /* Hide interactive elements in print */
  .skip-links,
  .floating-chat-widget,
  .performance-dashboard,
  .dev-panel {
    display: none !important;
  }

  /* Ensure good contrast for printing */
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  /* Ensure text is readable */
  body {
    font-size: 12pt !important;
    line-height: 1.4 !important;
  }
}

/* === UTILITY CLASSES === */

/* Force hardware acceleration when beneficial */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Disable hardware acceleration for low-end devices */
.cpu-optimized {
  transform: none !important;
  will-change: auto !important;
}

/* Content visibility optimization */
.lazy-content {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* Reduce layout thrashing */
.stable-layout {
  contain: layout style;
}

/* === RESPONSIVE PERFORMANCE === */

/* Reduce complexity on small screens */
@media (max-width: 767px) {
  .mobile-simplified * {
    box-shadow: none !important;
    backdrop-filter: none !important;
    border-radius: 4px !important;
  }

  /* Simplify animations on mobile */
  .mobile-simplified .floating-chat-widget {
    animation-duration: 0.2s !important;
  }
}

/* === ACCESSIBILITY FOCUS MANAGEMENT === */

/* Enhanced focus indicators for keyboard navigation */
.enhanced-focus :focus,
.enhanced-focus :focus-visible {
  outline: 3px solid var(--accent-cyan) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 1px var(--bg-primary), 0 0 0 4px var(--accent-cyan) !important;
}

/* Skip link specific styles */
.skip-link:focus {
  position: static !important;
  left: auto !important;
  top: auto !important;
  width: auto !important;
  height: auto !important;
  overflow: visible !important;
  clip: auto !important;
  z-index: var(--z-notification) !important;
}

/* === PERFORMANCE MONITORING === */

/* Visual indicators for performance issues */
.performance-warning {
  border: 2px dashed orange !important;
}

.performance-error {
  border: 2px solid red !important;
}

/* === BROWSER-SPECIFIC OPTIMIZATIONS === */

/* Safari optimizations */
@supports (-webkit-appearance: none) {
  .safari-optimized * {
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
  }
}

/* Firefox optimizations */
@-moz-document url-prefix() {
  .firefox-optimized * {
    -moz-osx-font-smoothing: grayscale;
  }
}

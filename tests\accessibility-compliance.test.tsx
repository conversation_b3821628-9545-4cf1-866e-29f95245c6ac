/**
 * Accessibility Compliance Testing Suite
 * 
 * Validates WCAG 2.1 compliance, screen reader compatibility,
 * and keyboard navigation across all restored components
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import '@testing-library/jest-dom';

// Import components to test
import OpenDesignTestPage from '@/pages/open-design-test';
import { CollapsibleSidePanel } from '@/components/layout/CollapsibleSidePanel';
import { SpatialModal } from '@/components/ui/SpatialModal';
import TabNavigation from '@/components/ui/TabNavigation';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Custom render function with providers
const renderWithProviders = (ui: React.ReactElement, options = {}) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    return React.createElement(global.TestWrapper, null, children);
  };

  return render(ui, { wrapper: Wrapper, ...options });
};

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/',
  }),
}));

// Mock portal for testing
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (node: React.ReactNode) => node,
}));

describe('Accessibility Compliance Testing', () => {
  
  describe('WCAG 2.1 Level AA Compliance', () => {
    test('Main application should have no accessibility violations', async () => {
      const { container } = renderWithProviders(<OpenDesignTestPage />);
      
      // Run axe accessibility tests
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('Side panel should have no accessibility violations', async () => {
      const mockTabs = [
        { id: 'stats', label: 'Game Statistics', content: <div>Stats content</div> },
        { id: 'history', label: 'Game History', content: <div>History content</div> }
      ];

      const { container } = renderWithProviders(
        <CollapsibleSidePanel
          isOpen={true}
          onToggle={() => {}}
          tabs={mockTabs}
        />
      );
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('Modal should have no accessibility violations', async () => {
      renderWithProviders(
        <SpatialModal
          isOpen={true}
          onClose={() => {}}
          title="Test Modal"
        >
          <div>
            <h2>Modal Content</h2>
            <p>This is test content for the modal.</p>
            <button>Action Button</button>
          </div>
        </SpatialModal>
      );

      // Since modal renders in a portal to document.body, test the entire document
      const results = await axe(document.body);
      expect(results).toHaveNoViolations();
    });

    test('Tab navigation should have no accessibility violations', async () => {
      const mockTabs = [
        { id: 'tab1', label: 'First Tab', content: <div>First tab content</div> },
        { id: 'tab2', label: 'Second Tab', content: <div>Second tab content</div> }
      ];

      const { container } = renderWithProviders(
        <TabNavigation tabs={mockTabs} />
      );
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Keyboard Navigation', () => {
    test('All interactive elements should be keyboard accessible', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(<OpenDesignTestPage />);
      
      // Get all focusable elements
      const focusableElements = screen.getAllByRole('button')
        .concat(screen.queryAllByRole('textbox') || [])
        .concat(screen.queryAllByRole('tab') || [])
        .concat(screen.queryAllByRole('link') || []);
      
      // Test that each element can receive focus
      for (const element of focusableElements) {
        element.focus();
        expect(element).toHaveFocus();
        
        // Test that element responds to Enter key
        await user.keyboard('{Enter}');
        // Element should still be focusable after interaction
        expect(document.activeElement).toBeTruthy();
      }
    });

    test('Tab navigation should follow logical order', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(<OpenDesignTestPage />);
      
      // Start from the beginning
      document.body.focus();
      
      const focusableElements: HTMLElement[] = [];
      
      // Tab through all elements and record order
      for (let i = 0; i < 20; i++) { // Limit to prevent infinite loop
        await user.tab();
        const activeElement = document.activeElement as HTMLElement;
        
        if (activeElement && activeElement !== document.body) {
          focusableElements.push(activeElement);
        } else {
          break;
        }
      }
      
      // Should have found focusable elements
      expect(focusableElements.length).toBeGreaterThan(0);
      
      // Each element should be unique in the tab order
      const uniqueElements = new Set(focusableElements);
      expect(uniqueElements.size).toBe(focusableElements.length);
    });

    test('Escape key should close modals and overlays', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const [showModal, setShowModal] = React.useState(false);
        
        return (
          <div>
            <button onClick={() => setShowModal(true)}>Open Modal</button>
            <SpatialModal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title="Test Modal"
            >
              <div>Modal content</div>
            </SpatialModal>
          </div>
        );
      };
      
      renderWithProviders(<TestComponent />);
      
      // Open modal
      await user.click(screen.getByText('Open Modal'));
      expect(screen.getByText('Test Modal')).toBeInTheDocument();
      
      // Press escape to close
      await user.keyboard('{Escape}');
      
      await waitFor(() => {
        expect(screen.queryByText('Test Modal')).not.toBeInTheDocument();
      });
    });

    test('Arrow keys should navigate within tab groups', async () => {
      const user = userEvent.setup();
      const mockTabs = [
        { id: 'tab1', label: 'First Tab', content: <div>First content</div> },
        { id: 'tab2', label: 'Second Tab', content: <div>Second content</div> },
        { id: 'tab3', label: 'Third Tab', content: <div>Third content</div> }
      ];

      renderWithProviders(<TabNavigation tabs={mockTabs} />);
      
      // Focus first tab
      const firstTab = screen.getByRole('tab', { name: 'First Tab' });
      firstTab.focus();
      expect(firstTab).toHaveFocus();
      
      // Arrow right should move to next tab
      await user.keyboard('{ArrowRight}');
      const secondTab = screen.getByRole('tab', { name: 'Second Tab' });
      expect(secondTab).toHaveFocus();
      
      // Arrow left should move back
      await user.keyboard('{ArrowLeft}');
      expect(firstTab).toHaveFocus();
    });
  });

  describe('Screen Reader Support', () => {
    test('All images should have alt text or be marked decorative', () => {
      renderWithProviders(<OpenDesignTestPage />);
      
      const images = screen.queryAllByRole('img');
      
      images.forEach(img => {
        const altText = img.getAttribute('alt');
        const ariaHidden = img.getAttribute('aria-hidden');
        
        // Image should either have alt text or be hidden from screen readers
        expect(altText !== null || ariaHidden === 'true').toBe(true);
      });
    });

    test('Form inputs should have proper labels', () => {
      renderWithProviders(<OpenDesignTestPage />);
      
      const inputs = screen.queryAllByRole('textbox');
      
      inputs.forEach(input => {
        const ariaLabel = input.getAttribute('aria-label');
        const ariaLabelledBy = input.getAttribute('aria-labelledby');
        const associatedLabel = input.closest('label') || 
                               document.querySelector(`label[for="${input.id}"]`);
        
        // Input should have some form of labeling
        expect(
          ariaLabel || ariaLabelledBy || associatedLabel
        ).toBeTruthy();
      });
    });

    test('Interactive elements should have accessible names', () => {
      renderWithProviders(<OpenDesignTestPage />);
      
      const buttons = screen.getAllByRole('button');
      
      buttons.forEach(button => {
        const accessibleName = button.textContent || 
                              button.getAttribute('aria-label') ||
                              button.getAttribute('aria-labelledby');
        
        expect(accessibleName).toBeTruthy();
        expect(accessibleName?.trim()).not.toBe('');
      });
    });

    test('Headings should follow proper hierarchy', () => {
      renderWithProviders(<OpenDesignTestPage />);
      
      const headings = screen.queryAllByRole('heading');
      
      if (headings.length > 0) {
        const headingLevels = headings.map(heading => {
          const tagName = heading.tagName.toLowerCase();
          return parseInt(tagName.replace('h', ''));
        });
        
        // First heading should be h1 or h2
        expect(headingLevels[0]).toBeLessThanOrEqual(2);
        
        // Check for proper hierarchy (no skipping levels)
        for (let i = 1; i < headingLevels.length; i++) {
          const currentLevel = headingLevels[i];
          const previousLevel = headingLevels[i - 1];
          
          // Should not skip more than one level
          expect(currentLevel - previousLevel).toBeLessThanOrEqual(1);
        }
      }
    });

    test('ARIA landmarks should be present', () => {
      renderWithProviders(<OpenDesignTestPage />);
      
      // Check for main landmark
      const main = screen.queryByRole('main');
      expect(main).toBeInTheDocument();
      
      // Check for navigation if present
      const nav = screen.queryByRole('navigation');
      if (nav) {
        expect(nav).toBeInTheDocument();
      }
      
      // Check for complementary content (sidebar)
      const complementary = screen.queryByRole('complementary');
      if (complementary) {
        expect(complementary).toBeInTheDocument();
      }
    });
  });

  describe('Focus Management', () => {
    test('Focus should be trapped within modals', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const [showModal, setShowModal] = React.useState(false);
        
        return (
          <div>
            <button onClick={() => setShowModal(true)}>Open Modal</button>
            <input placeholder="Outside input" />
            <SpatialModal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title="Test Modal"
            >
              <div>
                <input placeholder="Modal input 1" />
                <button>Modal button</button>
                <input placeholder="Modal input 2" />
              </div>
            </SpatialModal>
          </div>
        );
      };
      
      renderWithProviders(<TestComponent />);
      
      // Open modal
      await user.click(screen.getByText('Open Modal'));
      
      // Focus should be within modal
      const modalInput1 = screen.getByPlaceholderText('Modal input 1');
      const modalButton = screen.getByText('Modal button');
      const modalInput2 = screen.getByPlaceholderText('Modal input 2');
      const outsideInput = screen.getByPlaceholderText('Outside input');
      
      // Tab through modal elements
      await user.tab();
      expect([modalInput1, modalButton, modalInput2]).toContain(document.activeElement);
      
      await user.tab();
      expect([modalInput1, modalButton, modalInput2]).toContain(document.activeElement);
      
      await user.tab();
      expect([modalInput1, modalButton, modalInput2]).toContain(document.activeElement);
      
      // Should not focus outside input
      expect(document.activeElement).not.toBe(outsideInput);
    });

    test('Focus should return to trigger element after modal closes', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const [showModal, setShowModal] = React.useState(false);
        
        return (
          <div>
            <button onClick={() => setShowModal(true)}>Open Modal</button>
            <SpatialModal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title="Test Modal"
            >
              <div>Modal content</div>
            </SpatialModal>
          </div>
        );
      };
      
      renderWithProviders(<TestComponent />);
      
      const openButton = screen.getByText('Open Modal');
      
      // Focus and open modal
      openButton.focus();
      await user.click(openButton);
      
      // Close modal with escape
      await user.keyboard('{Escape}');
      
      // Focus should return to open button
      await waitFor(() => {
        expect(openButton).toHaveFocus();
      });
    });

    test('Skip links should be available for keyboard users', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(<OpenDesignTestPage />);
      
      // Tab to first element (should reveal skip link if present)
      await user.tab();
      
      // Look for skip link
      const skipLink = screen.queryByText(/skip to main/i) || 
                      screen.queryByText(/skip to content/i) ||
                      screen.queryByRole('link', { name: /skip/i });
      
      if (skipLink) {
        expect(skipLink).toBeVisible();
        
        // Skip link should be functional
        await user.click(skipLink);
        
        // Should move focus to main content
        const main = screen.getByRole('main');
        expect(main).toHaveFocus();
      }
    });
  });

  describe('Color and Contrast', () => {
    test('Text should have sufficient color contrast', () => {
      renderWithProviders(<OpenDesignTestPage />);
      
      // Get all text elements
      const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, button, input, label');
      
      textElements.forEach(element => {
        const computedStyle = window.getComputedStyle(element);
        const color = computedStyle.color;
        const backgroundColor = computedStyle.backgroundColor;
        
        // Basic check that colors are defined
        expect(color).toBeTruthy();
        
        // If element has background color, both should be defined
        if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)') {
          expect(color).toBeTruthy();
        }
      });
    });

    test('Interactive elements should have visible focus indicators', async () => {
      const user = userEvent.setup();
      
      renderWithProviders(<OpenDesignTestPage />);
      
      const focusableElements = screen.getAllByRole('button')
        .concat(screen.queryAllByRole('textbox') || [])
        .concat(screen.queryAllByRole('tab') || []);
      
      for (const element of focusableElements.slice(0, 5)) { // Test first 5 elements
        element.focus();
        
        const computedStyle = window.getComputedStyle(element);
        const outline = computedStyle.outline;
        const boxShadow = computedStyle.boxShadow;
        const borderColor = computedStyle.borderColor;
        
        // Should have some form of focus indicator
        const hasFocusIndicator = 
          outline !== 'none' || 
          boxShadow !== 'none' || 
          borderColor !== 'initial';
        
        expect(hasFocusIndicator).toBe(true);
      }
    });
  });

  describe('Touch and Mobile Accessibility', () => {
    test('Touch targets should meet minimum size requirements', () => {
      // Mock touch device
      Object.defineProperty(window, 'ontouchstart', {
        value: () => {},
        writable: true
      });
      
      renderWithProviders(<OpenDesignTestPage />);
      
      const buttons = screen.getAllByRole('button');
      
      buttons.forEach(button => {
        const computedStyle = window.getComputedStyle(button);
        const minHeight = parseInt(computedStyle.minHeight) || parseInt(computedStyle.height);
        const minWidth = parseInt(computedStyle.minWidth) || parseInt(computedStyle.width);
        
        // WCAG 2.1 minimum touch target size is 44x44px
        expect(minHeight).toBeGreaterThanOrEqual(44);
        expect(minWidth).toBeGreaterThanOrEqual(44);
      });
    });

    test('Content should be readable without horizontal scrolling', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 320, // Minimum mobile width
      });
      
      renderWithProviders(<OpenDesignTestPage />);
      
      // Check that no elements exceed viewport width
      const allElements = document.querySelectorAll('*');
      
      allElements.forEach(element => {
        const rect = element.getBoundingClientRect();
        
        // Element should not extend beyond viewport
        if (rect.width > 0) {
          expect(rect.right).toBeLessThanOrEqual(window.innerWidth + 50); // 50px tolerance for scrollbars
        }
      });
    });
  });
});

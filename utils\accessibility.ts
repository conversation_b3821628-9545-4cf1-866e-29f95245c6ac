/**
 * Accessibility Utilities
 * 
 * Tools for ensuring WCAG AA compliance and accessibility features
 * Includes contrast ratio validation and accessibility helpers
 */

// Color contrast ratio calculation
export function getContrastRatio(color1: string, color2: string): number {
  const getLuminance = (color: string): number => {
    // Convert hex to RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // Calculate relative luminance
    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
}

// WCAG compliance levels
export const WCAG_LEVELS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3,
  AAA_NORMAL: 7,
  AAA_LARGE: 4.5
} as const;

// Check if contrast ratio meets WCAG standards
export function meetsWCAG(
  foreground: string, 
  background: string, 
  level: keyof typeof WCAG_LEVELS = 'AA_NORMAL'
): boolean {
  const ratio = getContrastRatio(foreground, background);
  return ratio >= WCAG_LEVELS[level];
}

// Design system color validation
export const DESIGN_SYSTEM_COLORS = {
  // Text colors
  textPrimary: '#ffffff',
  textSecondary: '#e2e8f0',
  textMuted: '#94a3b8',
  textDisabled: '#64748b',

  // Background colors
  bgPrimary: '#0f172a',
  bgSecondary: '#1e293b',
  bgTertiary: '#334155',

  // Accent colors
  accentCyan: '#06b6d4',
  accentPurple: '#8b5cf6',
  accentPink: '#ec4899',

  // Semantic colors
  colorSuccess: '#10b981',
  colorError: '#ef4444',
  colorWarning: '#f59e0b',
  colorInfo: '#3b82f6'
} as const;

// Validate all text/background combinations
export function validateDesignSystemContrast(): Record<string, any> {
  const results: Record<string, any> = {};

  const textColors = {
    primary: DESIGN_SYSTEM_COLORS.textPrimary,
    secondary: DESIGN_SYSTEM_COLORS.textSecondary,
    muted: DESIGN_SYSTEM_COLORS.textMuted,
    disabled: DESIGN_SYSTEM_COLORS.textDisabled
  };

  const backgroundColors = {
    primary: DESIGN_SYSTEM_COLORS.bgPrimary,
    secondary: DESIGN_SYSTEM_COLORS.bgSecondary,
    tertiary: DESIGN_SYSTEM_COLORS.bgTertiary
  };

  Object.entries(textColors).forEach(([textName, textColor]) => {
    results[textName] = {};
    Object.entries(backgroundColors).forEach(([bgName, bgColor]) => {
      const ratio = getContrastRatio(textColor, bgColor);
      results[textName][bgName] = {
        ratio: Math.round(ratio * 100) / 100,
        wcagAA: ratio >= WCAG_LEVELS.AA_NORMAL,
        wcagAAA: ratio >= WCAG_LEVELS.AAA_NORMAL
      };
    });
  });

  return results;
}

// Accessibility helpers
export function generateAriaLabel(
  element: string, 
  state?: string, 
  context?: string
): string {
  let label = element;
  if (state) label += `, ${state}`;
  if (context) label += `, ${context}`;
  return label;
}

export function getKeyboardShortcutText(keys: string[]): string {
  const isMac = typeof navigator !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  return keys.map(key => {
    switch (key.toLowerCase()) {
      case 'cmd':
      case 'meta':
        return isMac ? '⌘' : 'Ctrl';
      case 'alt':
        return isMac ? '⌥' : 'Alt';
      case 'shift':
        return isMac ? '⇧' : 'Shift';
      case 'enter':
        return '↵';
      case 'escape':
        return 'Esc';
      default:
        return key.toUpperCase();
    }
  }).join(isMac ? '' : '+');
}

// Focus management
export function trapFocus(element: HTMLElement): () => void {
  const focusableElements = element.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  ) as NodeListOf<HTMLElement>;

  const firstElement = focusableElements[0];
  const lastElement = focusableElements[focusableElements.length - 1];

  const handleTabKey = (e: KeyboardEvent) => {
    if (e.key !== 'Tab') return;

    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        lastElement.focus();
        e.preventDefault();
      }
    } else {
      if (document.activeElement === lastElement) {
        firstElement.focus();
        e.preventDefault();
      }
    }
  };

  element.addEventListener('keydown', handleTabKey);

  // Return cleanup function
  return () => {
    element.removeEventListener('keydown', handleTabKey);
  };
}

// Screen reader announcements with proper cleanup
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;

  document.body.appendChild(announcement);

  // Remove after announcement with safety check
  setTimeout(() => {
    try {
      if (announcement.parentNode) {
        document.body.removeChild(announcement);
      }
    } catch (error) {
      // Element already removed, ignore error
      console.debug('Screen reader announcement element already removed');
    }
  }, 1000);
}

// Reduced motion detection
export function prefersReducedMotion(): boolean {
  return typeof window !== 'undefined' && 
         window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

// High contrast detection
export function prefersHighContrast(): boolean {
  return typeof window !== 'undefined' && 
         window.matchMedia('(prefers-contrast: high)').matches;
}

// Export validation results for development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  console.log('🎨 Design System Contrast Validation:', validateDesignSystemContrast());
}

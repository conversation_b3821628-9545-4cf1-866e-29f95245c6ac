/**
 * API Client Tests
 * 
 * Tests for the enhanced API client with caching, retry logic,
 * and performance optimization features.
 */

import { apiClient } from '../apiClient';

// Mock console methods to avoid noise in tests
const originalConsole = console;
beforeAll(() => {
  console.debug = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.debug = originalConsole.debug;
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;
});

// Mock fetch
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe('Enhanced API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    apiClient.clearCache();
    // Reset metrics by cleaning up the client
    apiClient.cleanup();
    mockFetch.mockClear();
  });

  afterEach(() => {
    apiClient.clearCache();
    apiClient.cleanup();
  });

  describe('Basic Request Functionality', () => {
    it('makes successful GET request', async () => {
      const mockResponse = { data: 'test' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await apiClient.request('/test', { method: 'GET' });

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith('/test', expect.objectContaining({
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      }));
    });

    it('makes successful POST request with body', async () => {
      const mockResponse = { success: true };
      const requestBody = { test: 'data' };
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await apiClient.request('/test', {
        method: 'POST',
        body: requestBody
      });

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith('/test', expect.objectContaining({
        method: 'POST',
        body: JSON.stringify(requestBody)
      }));
    });
  });

  describe('Caching Functionality', () => {
    it('caches GET requests when enabled', async () => {
      const mockResponse = { data: 'cached' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // First request
      const result1 = await apiClient.request('/test', {
        method: 'GET',
        cache: true,
        cacheTTL: 60000
      });

      // Second request should use cache
      const result2 = await apiClient.request('/test', {
        method: 'GET',
        cache: true,
        cacheTTL: 60000
      });

      expect(result1).toEqual(mockResponse);
      expect(result2).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledTimes(1); // Only one actual fetch
      
      const metrics = apiClient.getMetrics();
      expect(metrics.cacheHits).toBe(1);
      expect(metrics.cacheMisses).toBe(1);
    });

    it('does not cache POST requests', async () => {
      const mockResponse = { data: 'not cached' };
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // Two identical POST requests
      await apiClient.request('/test', {
        method: 'POST',
        body: { test: 'data' },
        cache: true
      });

      await apiClient.request('/test', {
        method: 'POST',
        body: { test: 'data' },
        cache: true
      });

      expect(mockFetch).toHaveBeenCalledTimes(2); // Both requests made
    });

    it('respects cache TTL', async () => {
      const mockResponse = { data: 'expired' };
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // First request with very short TTL
      await apiClient.request('/test', {
        method: 'GET',
        cache: true,
        cacheTTL: 1 // 1ms TTL
      });

      // Wait for cache to expire
      await new Promise(resolve => setTimeout(resolve, 10));

      // Second request should not use cache
      await apiClient.request('/test', {
        method: 'GET',
        cache: true,
        cacheTTL: 1
      });

      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Retry Logic', () => {
    it('retries failed requests', async () => {
      const mockResponse = { data: 'success after retry' };
      
      // First call fails, second succeeds
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse)
        });

      const result = await apiClient.request('/test', {
        retries: 1,
        retryDelay: 10
      });

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('does not retry non-retryable errors', async () => {
      mockFetch.mockRejectedValue(new Error('HTTP 400: Bad Request'));

      await expect(
        apiClient.request('/test', { retries: 3 })
      ).rejects.toThrow('HTTP 400: Bad Request');

      expect(mockFetch).toHaveBeenCalledTimes(1); // No retries for 400 error
    });

    it('gives up after max retries', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      await expect(
        apiClient.request('/test', { retries: 2, retryDelay: 1 })
      ).rejects.toThrow('Network error');

      expect(mockFetch).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });
  });

  describe('Request Deduplication', () => {
    it('deduplicates identical concurrent requests', async () => {
      const mockResponse = { data: 'deduplicated' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // Make two identical requests concurrently
      const [result1, result2] = await Promise.all([
        apiClient.request('/test', { deduplication: true }),
        apiClient.request('/test', { deduplication: true })
      ]);

      expect(result1).toEqual(mockResponse);
      expect(result2).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledTimes(1); // Only one actual fetch
      
      const metrics = apiClient.getMetrics();
      expect(metrics.deduplicatedRequests).toBe(1);
    });

    it('does not deduplicate when disabled', async () => {
      const mockResponse = { data: 'not deduplicated' };
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // Make two identical requests concurrently with deduplication disabled
      await Promise.all([
        apiClient.request('/test', { deduplication: false }),
        apiClient.request('/test', { deduplication: false })
      ]);

      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Handling', () => {
    it('handles HTTP errors correctly', async () => {
      mockFetch.mockReset();
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: () => Promise.resolve({}),
        text: () => Promise.resolve('')
      });

      await expect(
        apiClient.request('/test-error')
      ).rejects.toThrow('HTTP 500: Internal Server Error');
    });

    it('handles network errors correctly', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(
        apiClient.request('/test', { retries: 0 })
      ).rejects.toThrow('Network error');
    });

    it('handles timeout errors correctly', async () => {
      mockFetch.mockReset();
      mockFetch.mockImplementationOnce(() =>
        new Promise(resolve => setTimeout(resolve, 100))
      );

      await expect(
        apiClient.request('/test-timeout', { timeout: 50 })
      ).rejects.toThrow();
    });
  });

  describe('Metrics Tracking', () => {
    it('tracks request metrics correctly', async () => {
      const mockResponse = { data: 'metrics test' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => new Promise(resolve =>
          setTimeout(() => resolve(mockResponse), 10)
        )
      });

      await apiClient.request('/test-metrics');

      const metrics = apiClient.getMetrics();
      expect(metrics.totalRequests).toBe(1);
      expect(metrics.successfulRequests).toBe(1);
      expect(metrics.failedRequests).toBe(0);
      expect(metrics.averageResponseTime).toBeGreaterThan(0);
    });

    it('tracks failed request metrics', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Test error'));

      try {
        await apiClient.request('/test', { retries: 0 });
      } catch (error) {
        // Expected to fail
      }

      const metrics = apiClient.getMetrics();
      expect(metrics.totalRequests).toBe(1);
      expect(metrics.successfulRequests).toBe(0);
      expect(metrics.failedRequests).toBe(1);
    });
  });

  describe('Cache Management', () => {
    it('clears cache correctly', async () => {
      const mockResponse = { data: 'cache test' };
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // Make cached request
      await apiClient.request('/test', { cache: true });
      expect(apiClient.getCacheSize()).toBe(1);

      // Clear cache
      apiClient.clearCache();
      expect(apiClient.getCacheSize()).toBe(0);

      // Next request should hit the network again
      await apiClient.request('/test', { cache: true });
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });
});

/**
 * InputHelper Component - Consolidated Input Assistance (v2.0 Spatial Design)
 *
 * 🎯 UNIFIED INPUT GUIDANCE SYSTEM
 * 
 * Consolidates:
 * - WordsLeftCounter (word budget tracking)
 * - ValidationFeedback (real-time validation)
 * - Strike warnings and limits
 * - Common word usage alerts
 * 
 * Features:
 * - Real-time word count with budget visualization
 * - Contextual validation feedback
 * - Strike counter with risk assessment
 * - Smart show/hide based on input state
 * - Imposing typography and clear warnings
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React from 'react';
import { GameState } from '@/types/game';
import { Primary, Secondary, Small, Status, Imposing } from '@/components/ui/Typography';
import { calculateMaxWords, getRejectionSummary, getCommonWordsUsageSummary } from '@/utils/gameLogic';

interface InputHelperProps {
  gameState: GameState;
  currentDefinition: string;
  validationResult?: {
    isValid: boolean;
    errors: Array<{ type: string; message: string; }>;
  };
  showOnlyWhenNeeded?: boolean;
  className?: string;
}

export const InputHelper: React.FC<InputHelperProps> = ({
  gameState,
  currentDefinition,
  validationResult,
  showOnlyWhenNeeded = true,
  className = ''
}) => {
  const words = currentDefinition.trim().split(/\s+/).filter(word => word.length > 0);
  const wordCount = words.length;
  const hasContent = currentDefinition.trim().length > 0;

  // Calculate limits and status
  const maxWords = calculateMaxWords(
    gameState.step, 
    gameState.difficulty, 
    gameState.lastDefinitionLength
  );
  
  const rejectionSummary = getRejectionSummary(gameState);
  const commonWordsUsage = getCommonWordsUsageSummary(
    gameState.commonWordsUsage, 
    gameState.difficulty
  );

  // Validation states
  const isWordCountValid = wordCount <= maxWords;
  const isOverLimit = wordCount > maxWords;
  const isAtRisk = rejectionSummary.isAtRisk;
  const isCritical = rejectionSummary.currentStrikes >= 2;

  // Show/hide logic
  const shouldShow = !showOnlyWhenNeeded || 
    hasContent || 
    isOverLimit || 
    isAtRisk || 
    (validationResult && !validationResult.isValid);

  if (!shouldShow) {
    return null;
  }

  const helperClasses = [
    'input-helper',
    isOverLimit ? 'input-helper--error' : '',
    isAtRisk ? 'input-helper--warning' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={helperClasses}>
      {/* Word Budget Section */}
      <div className="helper-section helper-section--budget">
        <div className="budget-header">
          <Secondary>WORD BUDGET</Secondary>
          <div className={`budget-count ${isOverLimit ? 'budget-count--error' : ''}`}>
            <Imposing>{wordCount}</Imposing>
            <span className="budget-separator">/</span>
            <span className="budget-limit">{maxWords}</span>
          </div>
        </div>

        {/* Budget Progress Bar */}
        <div className="budget-progress">
          <div 
            className={`budget-fill ${isOverLimit ? 'budget-fill--error' : ''}`}
            style={{ width: `${Math.min((wordCount / maxWords) * 100, 100)}%` }}
          />
        </div>

        {/* Budget Warnings */}
        {isOverLimit && (
          <Status status="error">
            DEFINITION TOO LONG: Must be {maxWords} word{maxWords === 1 ? '' : 's'} or fewer
          </Status>
        )}

        {wordCount > maxWords * 0.8 && !isOverLimit && (
          <Status status="warning">
            APPROACHING LIMIT: {maxWords - wordCount} word{maxWords - wordCount === 1 ? '' : 's'} remaining
          </Status>
        )}
      </div>

      {/* Strike Counter Section */}
      {(rejectionSummary.currentStrikes > 0 || isAtRisk) && (
        <div className="helper-section helper-section--strikes">
          <div className="strikes-header">
            <Secondary>STRIKE STATUS</Secondary>
            <div className="strikes-count">
              {rejectionSummary.currentStrikes}/3
            </div>
          </div>

          <div className="strikes-visual">
            {[1, 2, 3].map(strike => (
              <div 
                key={strike}
                className={`strike-dot ${
                  strike <= rejectionSummary.currentStrikes ? 'strike-dot--active' : 'strike-dot--inactive'
                }`}
              />
            ))}
          </div>

          {isCritical && (
            <Status status="error">
              CRITICAL: One more rejection will end the game!
            </Status>
          )}

          {isAtRisk && !isCritical && (
            <Status status="warning">
              WARNING: Two strikes - be careful with your next definition
            </Status>
          )}
        </div>
      )}

      {/* Validation Errors Section */}
      {validationResult && !validationResult.isValid && (
        <div className="helper-section helper-section--validation">
          <Secondary>CURRENT ISSUES</Secondary>
          <div className="validation-errors">
            {validationResult.errors.map((error, index) => (
              <Status key={index} status="error">
                {error.message}
              </Status>
            ))}
          </div>
        </div>
      )}

      {/* Common Words Alert */}
      {commonWordsUsage.some(usage => usage.remaining <= 1 && usage.remaining > 0) && (
        <div className="helper-section helper-section--common-words">
          <Secondary>COMMON WORD ALERT</Secondary>
          <div className="common-words-warning">
            {commonWordsUsage
              .filter(usage => usage.remaining <= 1 && usage.remaining > 0)
              .map(usage => (
                <Status key={usage.word} status="warning">
                  "{usage.word}" - {usage.remaining} use{usage.remaining === 1 ? '' : 's'} remaining
                </Status>
              ))
            }
          </div>
        </div>
      )}

      <style jsx>{`
        /* === CORE INPUT HELPER === */
        .input-helper {
          display: flex;
          flex-direction: column;
          gap: var(--space-4);
          background: var(--glass-medium);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-lg);
          padding: var(--space-6);
          backdrop-filter: blur(12px);
          transition: all var(--transition-base);
        }

        .input-helper--error {
          border-color: var(--color-error-50);
          background: var(--color-error-10);
        }

        .input-helper--warning {
          border-color: var(--color-warning-50);
          background: var(--color-warning-10);
        }

        /* === HELPER SECTIONS === */
        .helper-section {
          display: flex;
          flex-direction: column;
          gap: var(--space-3);
        }

        .helper-section:not(:last-child) {
          padding-bottom: var(--space-3);
          border-bottom: 1px solid var(--glass-border);
        }

        /* === BUDGET SECTION === */
        .budget-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .budget-count {
          display: flex;
          align-items: baseline;
          gap: var(--space-1);
          color: var(--text-primary);
        }

        .budget-count--error {
          color: var(--color-error);
        }

        .budget-separator {
          font-size: var(--text-lg);
          color: var(--text-muted);
        }

        .budget-limit {
          font-size: var(--text-lg);
          font-weight: var(--font-semibold);
          color: var(--text-secondary);
        }

        .budget-progress {
          height: 6px;
          background: var(--bg-tertiary);
          border-radius: var(--radius-full);
          overflow: hidden;
        }

        .budget-fill {
          height: 100%;
          background: linear-gradient(90deg, var(--color-success) 0%, var(--color-warning) 80%, var(--color-error) 100%);
          transition: width var(--transition-base);
        }

        .budget-fill--error {
          background: var(--color-error);
        }

        /* === STRIKES SECTION === */
        .strikes-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .strikes-count {
          font-size: var(--text-xl);
          font-weight: var(--font-bold);
          color: var(--color-error);
        }

        .strikes-visual {
          display: flex;
          gap: var(--space-2);
          justify-content: center;
        }

        .strike-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          transition: all var(--transition-base);
        }

        .strike-dot--active {
          background: var(--color-error);
          box-shadow: 0 0 8px var(--color-error-50);
        }

        .strike-dot--inactive {
          background: var(--bg-tertiary);
          border: 1px solid var(--glass-border);
        }

        /* === VALIDATION ERRORS === */
        .validation-errors {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
        }

        /* === COMMON WORDS WARNING === */
        .common-words-warning {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .input-helper {
            padding: var(--space-4);
            gap: var(--space-3);
          }

          .budget-header,
          .strikes-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--space-2);
          }
        }

        /* === ACCESSIBILITY === */
        @media (prefers-reduced-motion: reduce) {
          .input-helper,
          .budget-fill,
          .strike-dot {
            transition: none;
          }
        }
      `}</style>
    </div>
  );
};

export default InputHelper;

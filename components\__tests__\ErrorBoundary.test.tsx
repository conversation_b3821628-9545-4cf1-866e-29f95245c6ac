/**
 * Error Boundary Tests
 * 
 * Tests for the enhanced error boundary system including
 * basic error catching, recovery mechanisms, and specialized boundaries.
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ErrorBoundary from '../ErrorBoundary';
import GameErrorBoundary from '../GameErrorBoundary';
import AsyncErrorBoundary from '../AsyncErrorBoundary';

// Mock console.error to avoid noise in tests
const originalError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalError;
});

// Component that throws an error
const ThrowError: React.FC<{ shouldThrow?: boolean }> = ({ shouldThrow = true }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

describe('ErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders children when there is no error', () => {
    render(
      <ErrorBoundary>
        <div>Test content</div>
      </ErrorBoundary>
    );

    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('catches and displays error with auto-retry UI for component level', () => {
    render(
      <ErrorBoundary level="component">
        <ThrowError />
      </ErrorBoundary>
    );

    // Component level errors show auto-retry UI
    expect(screen.getByText(/Recovering/i)).toBeInTheDocument();
    expect(screen.getByText(/fixing this component automatically/i)).toBeInTheDocument();
  });

  it('displays different UI based on error level', () => {
    render(
      <ErrorBoundary level="page">
        <ThrowError />
      </ErrorBoundary>
    );

    expect(screen.getAllByText(/Critical Error/i)[0]).toBeInTheDocument();
    expect(screen.getByText(/Refresh Page/i)).toBeInTheDocument();
  });

  it('calls custom error handler when provided', () => {
    const mockErrorHandler = jest.fn();
    
    render(
      <ErrorBoundary onError={mockErrorHandler}>
        <ThrowError />
      </ErrorBoundary>
    );

    expect(mockErrorHandler).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String)
      })
    );
  });

  it('shows section-level error UI with Try Again button', () => {
    render(
      <ErrorBoundary level="section">
        <ThrowError />
      </ErrorBoundary>
    );

    expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Try Again/i })).toBeInTheDocument();
  });

  it('renders custom fallback when provided', () => {
    const CustomFallback = <div>Custom error UI</div>;
    
    render(
      <ErrorBoundary fallback={CustomFallback}>
        <ThrowError />
      </ErrorBoundary>
    );

    expect(screen.getByText('Custom error UI')).toBeInTheDocument();
  });
});

describe('GameErrorBoundary', () => {
  it('renders game-specific error UI', () => {
    render(
      <GameErrorBoundary>
        <ThrowError />
      </GameErrorBoundary>
    );

    // GameErrorBoundary shows "Something went wrong" for non-game-critical errors
    expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
    expect(screen.getByText(/Continue Game/i)).toBeInTheDocument();
    expect(screen.getByText(/Restart Game/i)).toBeInTheDocument();
  });

  it('calls game error handler when provided', () => {
    const mockGameErrorHandler = jest.fn();
    
    render(
      <GameErrorBoundary onGameError={mockGameErrorHandler}>
        <ThrowError />
      </GameErrorBoundary>
    );

    expect(mockGameErrorHandler).toHaveBeenCalledWith(expect.any(Error));
  });
});

describe('AsyncErrorBoundary', () => {
  it('renders async-specific error UI', () => {
    render(
      <AsyncErrorBoundary>
        <ThrowError />
      </AsyncErrorBoundary>
    );

    expect(screen.getAllByText(/Service Error/i)[0]).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Try Again/i })).toBeInTheDocument();
  });

  it('calls async error handler when provided', () => {
    const mockAsyncErrorHandler = jest.fn();
    
    render(
      <AsyncErrorBoundary onAsyncError={mockAsyncErrorHandler}>
        <ThrowError />
      </AsyncErrorBoundary>
    );

    expect(mockAsyncErrorHandler).toHaveBeenCalledWith(expect.any(Error));
  });
});

describe('Error Boundary Integration', () => {
  it('isolates errors when isolate prop is true', () => {
    // This test verifies that isolated error boundaries don't propagate errors
    const mockParentErrorHandler = jest.fn();

    render(
      <ErrorBoundary onError={mockParentErrorHandler}>
        <div>Parent content</div>
        <ErrorBoundary isolate level="section">
          <ThrowError />
        </ErrorBoundary>
        <div>More parent content</div>
      </ErrorBoundary>
    );

    // Parent should still render its content
    expect(screen.getByText('Parent content')).toBeInTheDocument();
    expect(screen.getByText('More parent content')).toBeInTheDocument();

    // Error should be caught by child boundary (section level shows regular error UI)
    expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
  });

  it('resets on prop changes when resetOnPropsChange is true', () => {
    let shouldThrow = true;

    const { rerender } = render(
      <ErrorBoundary resetOnPropsChange level="section">
        <ThrowError shouldThrow={shouldThrow} />
      </ErrorBoundary>
    );

    expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();

    // Change props
    shouldThrow = false;
    rerender(
      <ErrorBoundary resetOnPropsChange level="section">
        <ThrowError shouldThrow={shouldThrow} />
      </ErrorBoundary>
    );

    expect(screen.getByText('No error')).toBeInTheDocument();
  });
});

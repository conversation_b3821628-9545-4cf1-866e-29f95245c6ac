/**
 * useSpatialLayout Hook - Spatial Design Layout State Management (v2.0)
 *
 * 🎯 CENTRALIZED SPATIAL LAYOUT STATE MANAGEMENT
 * 
 * Features:
 * - Spatial design layout state (side panels, tabs, modals)
 * - Optimized layout transitions and animations
 * - Responsive layout management
 * - Type-safe layout operations
 * - Performance monitoring integration
 * 
 * Performance Benefits:
 * - Memoized layout operations
 * - Optimized panel transitions
 * - Reduced re-renders through targeted updates
 * - Efficient animation state management
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_4_STATE_EXTRACTION.md
 */

import { useState, useCallback, useEffect, useRef } from 'react';

type SidePanelTab = 'stats' | 'history' | 'rules';

interface UseSpatialLayoutOptions {
  enablePerformanceMonitoring?: boolean;
  enableAnimations?: boolean;
  responsiveBreakpoint?: number;
}

interface UseSpatialLayoutReturn {
  // State
  showSidePanel: boolean;
  sidePanelTab: SidePanelTab;
  isResponsiveMode: boolean;
  
  // State Setters
  setShowSidePanel: (show: boolean) => void;
  setSidePanelTab: (tab: SidePanelTab) => void;
  
  // Optimized Layout Operations
  toggleSidePanel: () => void;
  changeSidePanelTab: (tab: SidePanelTab) => void;
  openSidePanelWithTab: (tab: SidePanelTab) => void;
  closeSidePanel: () => void;
  
  // Responsive Operations
  handleResponsiveChange: (isResponsive: boolean) => void;
  
  // Batch Operations
  batchUpdateLayout: (updates: Partial<SpatialLayoutState>) => void;
  resetLayout: () => void;
  
  // Performance Monitoring
  getLayoutMetrics: () => SpatialLayoutMetrics;
}

interface SpatialLayoutState {
  showSidePanel: boolean;
  sidePanelTab: SidePanelTab;
  isResponsiveMode: boolean;
}

interface SpatialLayoutMetrics {
  panelToggles: number;
  tabChanges: number;
  responsiveChanges: number;
  lastUpdateTime: number;
  averageTransitionTime: number;
}

const DEFAULT_LAYOUT_STATE: SpatialLayoutState = {
  showSidePanel: false,
  sidePanelTab: 'stats',
  isResponsiveMode: false
};

export function useSpatialLayout(options: UseSpatialLayoutOptions = {}): UseSpatialLayoutReturn {
  const { 
    enablePerformanceMonitoring = process.env.NODE_ENV === 'development',
    enableAnimations = true,
    responsiveBreakpoint = 768
  } = options;

  // Core State
  const [layoutState, setLayoutStateInternal] = useState<SpatialLayoutState>(DEFAULT_LAYOUT_STATE);

  // Performance Monitoring
  const panelTogglesRef = useRef<number>(0);
  const tabChangesRef = useRef<number>(0);
  const responsiveChangesRef = useRef<number>(0);
  const lastUpdateTimeRef = useRef<number>(0);
  const transitionTimesRef = useRef<number[]>([]);

  // Performance tracking
  const trackLayoutUpdate = useCallback((operation: () => void) => {
    if (!enablePerformanceMonitoring) {
      operation();
      return;
    }

    const startTime = performance.now();
    operation();
    const endTime = performance.now();
    
    const transitionTime = endTime - startTime;
    lastUpdateTimeRef.current = transitionTime;
    transitionTimesRef.current.push(transitionTime);
    
    // Keep only last 50 measurements for memory efficiency
    if (transitionTimesRef.current.length > 50) {
      transitionTimesRef.current = transitionTimesRef.current.slice(-50);
    }
  }, [enablePerformanceMonitoring]);

  // Responsive layout detection
  const checkResponsiveMode = useCallback(() => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth < responsiveBreakpoint;
  }, [responsiveBreakpoint]);

  // Initialize responsive state
  useEffect(() => {
    const isResponsive = checkResponsiveMode();
    if (isResponsive !== layoutState.isResponsiveMode) {
      setLayoutStateInternal(prevState => ({
        ...prevState,
        isResponsiveMode: isResponsive
      }));
    }
  }, [checkResponsiveMode, layoutState.isResponsiveMode]);

  // Responsive resize listener
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const isResponsive = checkResponsiveMode();
      if (isResponsive !== layoutState.isResponsiveMode) {
        if (enablePerformanceMonitoring) {
          responsiveChangesRef.current += 1;
        }
        
        trackLayoutUpdate(() => {
          setLayoutStateInternal(prevState => ({
            ...prevState,
            isResponsiveMode: isResponsive,
            // Auto-close side panel in responsive mode
            showSidePanel: isResponsive ? false : prevState.showSidePanel
          }));
        });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [checkResponsiveMode, layoutState.isResponsiveMode, enablePerformanceMonitoring, trackLayoutUpdate]);

  // Optimized State Setters
  const updateLayoutState = useCallback((updates: Partial<SpatialLayoutState>) => {
    trackLayoutUpdate(() => {
      setLayoutStateInternal(prevState => ({ ...prevState, ...updates }));
    });
  }, [trackLayoutUpdate]);

  const setShowSidePanel = useCallback((show: boolean) => {
    if (enablePerformanceMonitoring) {
      panelTogglesRef.current += 1;
    }
    updateLayoutState({ showSidePanel: show });
  }, [updateLayoutState, enablePerformanceMonitoring]);

  const setSidePanelTab = useCallback((tab: SidePanelTab) => {
    if (enablePerformanceMonitoring) {
      tabChangesRef.current += 1;
    }
    updateLayoutState({ sidePanelTab: tab });
  }, [updateLayoutState, enablePerformanceMonitoring]);

  // Optimized Layout Operations
  const toggleSidePanel = useCallback(() => {
    if (enablePerformanceMonitoring) {
      panelTogglesRef.current += 1;
    }
    updateLayoutState({ showSidePanel: !layoutState.showSidePanel });
  }, [updateLayoutState, layoutState.showSidePanel, enablePerformanceMonitoring]);

  const changeSidePanelTab = useCallback((tab: SidePanelTab) => {
    if (enablePerformanceMonitoring) {
      tabChangesRef.current += 1;
    }
    
    // If panel is closed, open it when changing tabs
    const updates: Partial<SpatialLayoutState> = { sidePanelTab: tab };
    if (!layoutState.showSidePanel) {
      updates.showSidePanel = true;
      if (enablePerformanceMonitoring) {
        panelTogglesRef.current += 1;
      }
    }
    
    updateLayoutState(updates);
  }, [updateLayoutState, layoutState.showSidePanel, enablePerformanceMonitoring]);

  const openSidePanelWithTab = useCallback((tab: SidePanelTab) => {
    if (enablePerformanceMonitoring) {
      panelTogglesRef.current += 1;
      tabChangesRef.current += 1;
    }
    
    updateLayoutState({
      showSidePanel: true,
      sidePanelTab: tab
    });
  }, [updateLayoutState, enablePerformanceMonitoring]);

  const closeSidePanel = useCallback(() => {
    if (enablePerformanceMonitoring) {
      panelTogglesRef.current += 1;
    }
    updateLayoutState({ showSidePanel: false });
  }, [updateLayoutState, enablePerformanceMonitoring]);

  // Responsive Operations
  const handleResponsiveChange = useCallback((isResponsive: boolean) => {
    if (enablePerformanceMonitoring) {
      responsiveChangesRef.current += 1;
    }
    
    updateLayoutState({
      isResponsiveMode: isResponsive,
      // Auto-close side panel in responsive mode
      showSidePanel: isResponsive ? false : layoutState.showSidePanel
    });
  }, [updateLayoutState, layoutState.showSidePanel, enablePerformanceMonitoring]);

  // Batch Operations
  const batchUpdateLayout = useCallback((updates: Partial<SpatialLayoutState>) => {
    updateLayoutState(updates);
  }, [updateLayoutState]);

  const resetLayout = useCallback(() => {
    updateLayoutState(DEFAULT_LAYOUT_STATE);
  }, [updateLayoutState]);

  // Performance Metrics
  const getLayoutMetrics = useCallback((): SpatialLayoutMetrics => {
    const averageTransitionTime = transitionTimesRef.current.length > 0
      ? transitionTimesRef.current.reduce((sum, time) => sum + time, 0) / transitionTimesRef.current.length
      : 0;

    return {
      panelToggles: panelTogglesRef.current,
      tabChanges: tabChangesRef.current,
      responsiveChanges: responsiveChangesRef.current,
      lastUpdateTime: lastUpdateTimeRef.current,
      averageTransitionTime
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Reset performance counters
      panelTogglesRef.current = 0;
      tabChangesRef.current = 0;
      responsiveChangesRef.current = 0;
      lastUpdateTimeRef.current = 0;
      transitionTimesRef.current = [];
    };
  }, []);

  // Development logging
  useEffect(() => {
    if (enablePerformanceMonitoring) {
      console.log('🎨 useSpatialLayout: Layout updated', {
        showSidePanel: layoutState.showSidePanel,
        sidePanelTab: layoutState.sidePanelTab,
        isResponsiveMode: layoutState.isResponsiveMode,
        panelToggles: panelTogglesRef.current,
        tabChanges: tabChangesRef.current
      });
    }
  }, [layoutState, enablePerformanceMonitoring]);

  return {
    // State
    showSidePanel: layoutState.showSidePanel,
    sidePanelTab: layoutState.sidePanelTab,
    isResponsiveMode: layoutState.isResponsiveMode,
    
    // State Setters
    setShowSidePanel,
    setSidePanelTab,
    
    // Optimized Layout Operations
    toggleSidePanel,
    changeSidePanelTab,
    openSidePanelWithTab,
    closeSidePanel,
    
    // Responsive Operations
    handleResponsiveChange,
    
    // Batch Operations
    batchUpdateLayout,
    resetLayout,
    
    // Performance Monitoring
    getLayoutMetrics
  };
}

export default useSpatialLayout;

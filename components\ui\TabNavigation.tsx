/**
 * TabNavigation Component - Enhanced Accessible Tabs (v2.0 Spatial Design)
 *
 * 🎯 FULLY ACCESSIBLE TAB SYSTEM
 * 
 * Features:
 * - Complete keyboard navigation (Arrow keys, Home, End, Tab)
 * - ARIA compliance with proper roles and states
 * - Focus management and visual indicators
 * - Responsive design with overflow handling
 * - Badge support for notifications and counts
 * - Smooth animations and micro-interactions
 * 
 * Keyboard Navigation:
 * - Arrow Left/Right: Navigate between tabs
 * - Home/End: Jump to first/last tab
 * - Enter/Space: Activate tab
 * - Tab: Move to tab content
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Small } from '@/components/ui/Typography';

export interface TabItem {
  id: string;
  label: string;
  icon?: string;
  badge?: string | number;
  disabled?: boolean;
  content: React.ReactNode;
}

interface TabNavigationProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  orientation?: 'horizontal' | 'vertical';
  variant?: 'default' | 'pills' | 'underline';
  size?: 'small' | 'medium' | 'large';
  showIcons?: boolean;
  showBadges?: boolean;
  className?: string;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
  orientation = 'horizontal',
  variant = 'default',
  size = 'medium',
  showIcons = true,
  showBadges = true,
  className = ''
}) => {
  const [focusedTab, setFocusedTab] = useState<string>(activeTab);
  const tabListRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<Map<string, HTMLButtonElement>>(new Map());

  // Update focused tab when active tab changes
  useEffect(() => {
    setFocusedTab(activeTab);
  }, [activeTab]);

  // Focus management
  const focusTab = useCallback((tabId: string) => {
    const tabElement = tabRefs.current.get(tabId);
    if (tabElement) {
      tabElement.focus();
      setFocusedTab(tabId);
    }
  }, []);

  // Keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent, tabId: string) => {
    const enabledTabs = tabs.filter(tab => !tab.disabled);
    const currentIndex = enabledTabs.findIndex(tab => tab.id === tabId);
    
    let nextIndex = currentIndex;

    switch (e.key) {
      case 'ArrowLeft':
      case 'ArrowUp':
        e.preventDefault();
        nextIndex = currentIndex > 0 ? currentIndex - 1 : enabledTabs.length - 1;
        break;
        
      case 'ArrowRight':
      case 'ArrowDown':
        e.preventDefault();
        nextIndex = currentIndex < enabledTabs.length - 1 ? currentIndex + 1 : 0;
        break;
        
      case 'Home':
        e.preventDefault();
        nextIndex = 0;
        break;
        
      case 'End':
        e.preventDefault();
        nextIndex = enabledTabs.length - 1;
        break;
        
      case 'Enter':
      case ' ':
        e.preventDefault();
        onTabChange(tabId);
        return;
        
      default:
        return;
    }

    const nextTab = enabledTabs[nextIndex];
    if (nextTab) {
      focusTab(nextTab.id);
    }
  }, [tabs, onTabChange, focusTab]);

  // Handle tab click
  const handleTabClick = (tabId: string) => {
    if (tabs.find(tab => tab.id === tabId)?.disabled) {
      return;
    }
    onTabChange(tabId);
    focusTab(tabId);
  };

  // Generate CSS classes
  const containerClasses = [
    'tab-navigation',
    `tab-navigation--${orientation}`,
    `tab-navigation--${variant}`,
    `tab-navigation--${size}`,
    className
  ].filter(Boolean).join(' ');

  const activeTabContent = tabs.find(tab => tab.id === activeTab);

  return (
    <div className={containerClasses}>
      {/* Tab List */}
      <div
        ref={tabListRef}
        className="tab-list"
        role="tablist"
        aria-orientation={orientation}
      >
        {tabs.map((tab) => {
          const isActive = tab.id === activeTab;
          const isFocused = tab.id === focusedTab;
          
          const tabClasses = [
            'tab-button',
            isActive ? 'tab-button--active' : '',
            isFocused ? 'tab-button--focused' : '',
            tab.disabled ? 'tab-button--disabled' : '',
          ].filter(Boolean).join(' ');

          return (
            <button
              key={tab.id}
              ref={(el) => {
                if (el) {
                  tabRefs.current.set(tab.id, el);
                } else {
                  tabRefs.current.delete(tab.id);
                }
              }}
              className={tabClasses}
              role="tab"
              aria-selected={isActive}
              aria-controls={`tabpanel-${tab.id}`}
              aria-disabled={tab.disabled}
              id={`tab-${tab.id}`}
              tabIndex={isActive ? 0 : -1}
              onClick={() => handleTabClick(tab.id)}
              onKeyDown={(e) => handleKeyDown(e, tab.id)}
              disabled={tab.disabled}
            >
              {/* Tab Content */}
              <div className="tab-content">
                {showIcons && tab.icon && (
                  <span className="tab-icon" aria-hidden="true">
                    {tab.icon}
                  </span>
                )}
                
                <span className="tab-label">{tab.label}</span>
                
                {showBadges && tab.badge && (
                  <span className="tab-badge" aria-label={`${tab.badge} items`}>
                    {tab.badge}
                  </span>
                )}
              </div>

              {/* Active indicator */}
              {isActive && <div className="tab-indicator" aria-hidden="true" />}
            </button>
          );
        })}
      </div>

      {/* Tab Panels */}
      <div className="tab-panels">
        {activeTabContent && (
          <div
            className="tab-panel"
            role="tabpanel"
            aria-labelledby={`tab-${activeTab}`}
            id={`tabpanel-${activeTab}`}
            tabIndex={0}
          >
            {activeTabContent.content}
          </div>
        )}
      </div>

      <style jsx>{`
        /* === CORE TAB NAVIGATION === */
        .tab-navigation {
          display: flex;
          width: 100%;
          height: 100%;
          position: relative;
          z-index: var(--z-10);
        }

        .tab-navigation--horizontal {
          flex-direction: column;
        }

        .tab-navigation--vertical {
          flex-direction: row;
        }

        /* === TAB LIST === */
        .tab-list {
          display: flex;
          background: var(--glass-subtle);
          border-radius: var(--radius-lg);
          padding: var(--space-1);
          gap: var(--space-1);
          flex-shrink: 0;
          position: relative;
          z-index: var(--z-20);
        }

        .tab-navigation--horizontal .tab-list {
          flex-direction: row;
          border-bottom: 1px solid var(--glass-border);
          overflow-x: auto;
          scrollbar-width: none;
          /* Prevent horizontal scroll from interfering with layout */
          scroll-behavior: smooth;
        }

        .tab-navigation--vertical .tab-list {
          flex-direction: column;
          border-right: 1px solid var(--glass-border);
          overflow-y: auto;
          width: 200px;
          min-width: 200px;
          /* Prevent vertical scroll from interfering with layout */
          scroll-behavior: smooth;
        }

        .tab-list::-webkit-scrollbar {
          display: none;
        }

        /* === TAB BUTTONS === */
        .tab-button {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          background: none;
          border: none;
          border-radius: var(--radius-base);
          color: var(--text-muted);
          cursor: pointer;
          transition: all var(--transition-base);
          outline: none;
          flex-shrink: 0;
          z-index: var(--z-20);
          /* Prevent button content from overlapping */
          contain: layout;
        }

        /* Size variants */
        .tab-navigation--small .tab-button {
          padding: var(--space-2) var(--space-3);
          min-height: 36px;
        }

        .tab-navigation--medium .tab-button {
          padding: var(--space-3) var(--space-4);
          min-height: 44px;
        }

        .tab-navigation--large .tab-button {
          padding: var(--space-4) var(--space-6);
          min-height: 52px;
        }

        /* Horizontal layout */
        .tab-navigation--horizontal .tab-button {
          flex: 1;
          min-width: 80px;
        }

        /* Vertical layout */
        .tab-navigation--vertical .tab-button {
          width: 100%;
          justify-content: flex-start;
        }

        /* === TAB STATES === */
        .tab-button:hover:not(:disabled) {
          background: var(--glass-medium);
          color: var(--text-secondary);
        }

        .tab-button--active {
          background: var(--bg-primary);
          color: var(--accent-cyan);
          box-shadow: var(--shadow-sm);
        }

        .tab-button--focused {
          outline: 2px solid var(--accent-cyan);
          outline-offset: 2px;
        }

        .tab-button--disabled {
          opacity: 0.5;
          cursor: not-allowed;
          pointer-events: none;
        }

        /* === TAB CONTENT === */
        .tab-content {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          width: 100%;
        }

        .tab-navigation--vertical .tab-content {
          justify-content: flex-start;
        }

        .tab-navigation--horizontal .tab-content {
          justify-content: center;
          flex-direction: column;
          gap: var(--space-1);
        }

        /* === TAB ELEMENTS === */
        .tab-icon {
          font-size: var(--text-lg);
          line-height: 1;
          flex-shrink: 0;
        }

        .tab-label {
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .tab-navigation--horizontal .tab-label {
          font-size: var(--text-xs);
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }

        .tab-badge {
          background: var(--accent-cyan);
          color: var(--bg-primary);
          font-size: var(--text-xs);
          font-weight: var(--font-bold);
          padding: 2px 6px;
          border-radius: var(--radius-full);
          min-width: 18px;
          height: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 1;
        }

        .tab-button--active .tab-badge {
          background: var(--accent-purple);
        }

        /* === TAB INDICATOR === */
        .tab-indicator {
          position: absolute;
          background: var(--accent-cyan);
          border-radius: var(--radius-full);
          transition: all var(--transition-base);
        }

        .tab-navigation--horizontal .tab-indicator {
          bottom: -1px;
          left: 50%;
          transform: translateX(-50%);
          width: 60%;
          height: 2px;
        }

        .tab-navigation--vertical .tab-indicator {
          right: -1px;
          top: 50%;
          transform: translateY(-50%);
          width: 2px;
          height: 60%;
        }

        /* === TAB PANELS === */
        .tab-panels {
          flex: 1;
          overflow: hidden;
          position: relative;
          z-index: var(--z-10);
        }

        .tab-panel {
          width: 100%;
          height: 100%;
          overflow-y: auto;
          outline: none;
          animation: tab-panel-enter 0.2s ease-out;
          position: relative;
          z-index: var(--z-10);
          /* Prevent content from overlapping with other components */
          contain: layout style;
        }

        @keyframes tab-panel-enter {
          0% {
            opacity: 0;
            transform: translateY(8px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        /* === VARIANT STYLES === */
        .tab-navigation--pills .tab-list {
          background: transparent;
          padding: 0;
          gap: var(--space-2);
        }

        .tab-navigation--pills .tab-button {
          background: var(--glass-subtle);
          border: 1px solid var(--glass-border);
        }

        .tab-navigation--pills .tab-button--active {
          background: var(--accent-cyan);
          color: var(--bg-primary);
          border-color: var(--accent-cyan);
        }

        .tab-navigation--underline .tab-list {
          background: transparent;
          padding: 0;
          border-bottom: 1px solid var(--glass-border);
        }

        .tab-navigation--underline .tab-button {
          border-radius: 0;
          border-bottom: 2px solid transparent;
        }

        .tab-navigation--underline .tab-button--active {
          background: transparent;
          border-bottom-color: var(--accent-cyan);
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: calc(var(--bp-md) - 1px)) {
          .tab-navigation--vertical {
            flex-direction: column;
          }

          .tab-navigation--vertical .tab-list {
            flex-direction: row;
            width: 100%;
            min-width: 100%;
            border-right: none;
            border-bottom: 1px solid var(--glass-border);
            /* Prevent horizontal overflow on mobile */
            overflow-x: auto;
            scrollbar-width: none;
          }

          .tab-navigation--vertical .tab-list::-webkit-scrollbar {
            display: none;
          }

          /* Hide labels on mobile for vertical tabs only */
          .tab-navigation--vertical .tab-label {
            display: none;
          }

          /* Keep labels visible for horizontal tabs */
          .tab-navigation--horizontal .tab-label {
            display: block;
          }

          /* Ensure tab buttons maintain proper touch targets on mobile */
          .tab-navigation--vertical .tab-button {
            min-width: 60px;
            flex-shrink: 0;
          }
        }

        /* === ACCESSIBILITY === */
        @media (prefers-reduced-motion: reduce) {
          .tab-button,
          .tab-indicator,
          .tab-panel {
            animation: none;
            transition: none;
          }
        }

        @media (prefers-contrast: high) {
          .tab-button {
            border: 1px solid var(--text-muted);
          }

          .tab-button--active {
            border-color: var(--accent-cyan);
            background: var(--accent-cyan);
            color: var(--bg-primary);
          }
        }
      `}</style>
    </div>
  );
};

export default TabNavigation;

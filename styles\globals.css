@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import DEFEATER.AI Design System */
@import './design-system.css';

/* Import beautiful fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Base styles - DEFEATER.AI Design System */
@layer base {
  html {
    font-family: var(--font-primary);
    scroll-behavior: smooth;
  }

  body {
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-primary);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* Living canvas background */
    background-image: var(--gradient-primary);
    background-attachment: fixed;
    background-size: 120% 120%;
    background-position: center center;
    position: relative;
    overflow-x: hidden;
  }

  /* Multi-layered aurora canvas */
  body::before {
    content: '';
    position: fixed;
    inset: -10%;
    background:
      var(--aurora-purple),
      var(--aurora-cyan),
      var(--aurora-pink);
    pointer-events: none;
    z-index: -1;
    will-change: transform, opacity;
    animation: aurora-breathe 25s ease-in-out infinite;
  }

  /* Secondary aurora layer for depth */
  body::after {
    content: '';
    position: fixed;
    inset: -5%;
    background:
      radial-gradient(circle at 60% 30%, rgba(139, 92, 246, 0.15) 0%, transparent 60%),
      radial-gradient(circle at 30% 70%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    will-change: transform;
    animation: aurora-drift 35s linear infinite;
  }

  /* Living canvas animations */
  @keyframes aurora-breathe {
    0%, 100% {
      transform: translate(-2%, -2%) scale(1) rotate(0deg);
      opacity: 0.8;
    }
    25% {
      transform: translate(1%, -3%) scale(1.02) rotate(0.5deg);
      opacity: 0.9;
    }
    50% {
      transform: translate(-1%, 2%) scale(1.01) rotate(-0.3deg);
      opacity: 0.7;
    }
    75% {
      transform: translate(2%, -1%) scale(1.03) rotate(0.2deg);
      opacity: 0.85;
    }
  }

  @keyframes aurora-drift {
    0% {
      transform: translate(0%, 0%) rotate(0deg);
    }
    25% {
      transform: translate(-2%, 1%) rotate(90deg);
    }
    50% {
      transform: translate(1%, -1%) rotate(180deg);
    }
    75% {
      transform: translate(2%, 2%) rotate(270deg);
    }
    100% {
      transform: translate(0%, 0%) rotate(360deg);
    }
  }

  * {
    box-sizing: border-box;
  }

  /* Custom scrollbar using design system */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--accent-cyan);
    box-shadow: var(--glow-cyan);
  }
}

/* Component styles - DEFEATER.AI Design System */
@layer components {
  /* Button styles using design system */
  .btn-primary {
    background: var(--bg-glass-light);
    color: var(--accent-cyan);
    border: 1px solid var(--accent-cyan);
    font-weight: var(--font-semibold);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-xl);
    transition: all var(--transition-base);
    /* Fallback for browsers without backdrop-filter support */
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    height: var(--button-height);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .btn-primary:hover {
    background: var(--bg-glass-medium);
    border-color: var(--accent-cyan);
    box-shadow: var(--glow-cyan);
    transform: translateY(-2px);
  }

  .btn-primary:active {
    transform: translateY(0);
  }

  .btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.3);
  }

  .btn-primary:focus-visible {
    outline: 2px solid var(--accent-cyan);
    outline-offset: 2px;
  }

  .btn-secondary {
    background: var(--bg-glass-light);
    color: var(--accent-purple);
    border: 1px solid var(--accent-purple);
    font-weight: var(--font-semibold);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-xl);
    transition: all var(--transition-base);
    /* Fallback for browsers without backdrop-filter support */
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    height: var(--button-height);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .btn-secondary:hover {
    background: var(--bg-glass-medium);
    border-color: var(--accent-purple);
    box-shadow: var(--glow-purple);
    transform: translateY(-2px);
  }

  .btn-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
  }

  .btn-secondary:focus-visible {
    outline: 2px solid var(--accent-purple);
    outline-offset: 2px;
  }

  /* Typography classes using design system */
  .text-hero {
    font-size: var(--text-5xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    color: var(--text-primary);
    text-shadow: var(--glow-purple);
  }

  .text-primary-large {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
  }

  .text-secondary-large {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
  }

  .text-body {
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    color: var(--text-secondary);
  }

  .text-small {
    font-size: var(--text-sm);
    font-weight: var(--font-normal);
    color: var(--text-muted);
  }

  /* Input styles using design system */
  .input-primary {
    width: 100%;
    padding: var(--space-4);
    background: var(--bg-glass-light);
    border: 2px solid var(--bg-tertiary);
    border-radius: var(--radius-xl);
    color: var(--text-primary);
    font-size: var(--text-base);
    transition: all var(--transition-base);
    /* Fallback for browsers without backdrop-filter support */
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(8px);
    min-height: var(--input-height);
    resize: vertical;
  }

  .input-primary:focus {
    outline: none;
    border-color: var(--accent-cyan);
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2);
  }

  .input-primary::placeholder {
    color: var(--text-muted);
  }

  /* Spatial design utilities - open, breathing layouts */
  .spatial-section {
    margin: var(--section-spacing) 0;
    text-align: center;
    width: 100%;
  }

  .spatial-group {
    margin: var(--group-spacing) 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--element-spacing);
  }

  .spatial-flow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--tight-spacing);
  }

  /* Content width constraints - no containers, just natural limits */
  .content-narrow {
    max-width: var(--content-narrow);
    width: 100%;
  }

  .content-medium {
    max-width: var(--content-medium);
    width: 100%;
  }

  .content-wide {
    max-width: var(--content-wide);
    width: 100%;
  }

  /* Floating elements with minimal chrome */
  .floating-element {
    background: var(--glass-subtle);
    /* Fallback for browsers without backdrop-filter support */
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    transition: all var(--transition-base);
  }

  .floating-element:hover {
    background: var(--glass-medium);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
  }

  /* Spatial card - replaces legacy card-gradient */
  .spatial-card {
    background: var(--glass-medium);
    /* Fallback for browsers without backdrop-filter support */
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-dark-soft);
  }

  .spatial-card:hover {
    background: var(--glass-heavy);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow: var(--shadow-dark-heavy);
  }

  /* Game focus area with subtle gradient */
  .game-focus {
    max-width: var(--game-focus-max-width);
    margin: 0 auto;
    padding: var(--space-8);
    text-align: center;
  }

  /* Target revelation - floating elements */
  .target-strip {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-6);
    margin: var(--space-8) 0;
    flex-wrap: wrap;
  }

  .target-item {
    padding: var(--space-2) var(--space-4);
    background: rgba(255, 255, 255, 0.05);
    /* Fallback already provided above */
    backdrop-filter: blur(8px);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-base);
  }

  .target-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
  }

  /* Progress indicator styling */
  .progress-indicator {
    font-size: var(--text-lg);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
    text-align: center;
  }
}

/* Performance optimizations */
@layer utilities {
  /* GPU acceleration for smooth animations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    body::before {
      animation: none;
    }

    .btn-primary:hover,
    .btn-secondary:hover {
      transform: none;
    }

    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* High contrast mode adjustments */
  @media (prefers-contrast: high) {
    body::before {
      opacity: 0.3;
    }

    .spatial-card {
      border-width: 2px;
      background: var(--glass-heavy);
    }

    .target-strip {
      border-width: 2px;
      border-color: var(--text-secondary);
    }
  }

  /* Print styles */
  @media print {
    body::before {
      display: none;
    }

    .spatial-card {
      background: var(--bg-primary) !important;
      box-shadow: none !important;
    }
  }




}

/* Additional animations for smooth interactions */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive design adjustments */
@media (max-width: 640px) {
  .game-focus {
    padding: var(--space-4);
  }

  .text-hero {
    font-size: var(--text-4xl);
  }

  .btn-primary, .btn-secondary {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    height: auto;
  }

  /* Typography component styles */
  .status-text {
    font-weight: var(--font-medium);
    margin: 0;
  }

  .status-text--success {
    color: var(--color-success);
  }

  .status-text--error {
    color: var(--color-error);
  }

  .status-text--warning {
    color: var(--color-warning);
  }

  .status-text--info {
    color: var(--color-info);
  }

  .status-text--processing {
    color: var(--accent-cyan);
    animation: pulse 2s ease-in-out infinite;
  }

  .mono-text {
    font-family: var(--font-mono);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    background: var(--bg-tertiary);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-base);
    margin: 0;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
  }

  .target-strip {
    padding: var(--space-3);
    flex-direction: column;
    gap: var(--space-2);
  }
}

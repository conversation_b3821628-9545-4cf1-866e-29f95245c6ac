import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'white';
  text?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  color = 'primary',
  text 
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'w-4 h-4';
      case 'md': return 'w-8 h-8';
      case 'lg': return 'w-12 h-12';
      default: return 'w-8 h-8';
    }
  };

  const getColorClasses = () => {
    switch (color) {
      case 'primary': return 'border-defeater-neon-cyan border-t-transparent shadow-glow-cyan';
      case 'secondary': return 'border-defeater-neon-purple border-t-transparent shadow-glow-purple';
      case 'white': return 'border-defeater-text-primary border-t-transparent';
      default: return 'border-defeater-neon-cyan border-t-transparent shadow-glow-cyan';
    }
  };

  return (
    <div className="flex flex-col items-center justify-center">
      <div 
        className={`
          ${getSizeClasses()} 
          ${getColorClasses()}
          border-2 rounded-full animate-spin
        `}
      />
      {text && (
        <p className="mt-3 text-sm text-defeater-text-secondary animate-pulse">
          {text}
        </p>
      )}
    </div>
  );
};

export default LoadingSpinner;

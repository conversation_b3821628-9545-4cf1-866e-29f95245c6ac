/**
 * useGameLogic Hook - Extracted from GameBoard.tsx
 *
 * Encapsulates all core game logic functions including:
 * - Game initialization and starting
 * - Definition submission and validation
 * - Input handling and sanitization
 * - Trash talk integration
 * - Animation coordination
 *
 * This extraction reduces GameBoard.tsx by ~200 lines and improves maintainability.
 */

import { useCallback, useRef, useEffect } from 'react';
import { GameState, DifficultyLevel, GameResponse, AnimationState } from '@/types/game';
import { GameErrorHandler } from '@/utils/errorHandling';
import { performanceMonitor, optimizeGameState, debounce } from '@/utils/performance';
import { triggerTrashTalk, getSpecialTrashTalk } from '@/utils/aiTrashTalk';
import { calculateMaxWords } from '@/utils/gameLogic';
import * as gameApi from '@/utils/gameApi';

interface UseGameLogicProps {
  gameState: GameState | null;
  optimizedGameState: GameState | null;
  selectedDifficulty: DifficultyLevel;
  setGameState: (state: GameState) => void;
  setLastAIResponse: (response: any) => void;
  setError: (error: string) => void;
  clearError: () => void;
  batchUpdateUI: (updates: any) => void;
  setAnimationState: (state: AnimationState) => void;
  gameAnimations: any;
  setShowDifficultySelector: (show: boolean) => void;
  setInputValue: (value: string) => void;
  isLowEnd: boolean;
}

interface UseGameLogicReturn {
  startNewGame: (difficulty?: DifficultyLevel) => Promise<void>;
  submitDefinition: (definition: string) => Promise<void>;
  sanitizeInput: (input: string) => string | null;
  handleInputChange: (value: string) => void;
  handleImmediateInputChange: (value: string) => void;
  getParanoiaLevel: (step: number) => string;
  getParanoiaMessage: (step: number) => string;
  isSubmitting: boolean;
}

export const useGameLogic = ({
  gameState,
  optimizedGameState,
  selectedDifficulty,
  setGameState,
  setLastAIResponse,
  setError,
  clearError,
  batchUpdateUI,
  setAnimationState,
  gameAnimations,
  setShowDifficultySelector,
  setInputValue,
  isLowEnd
}: UseGameLogicProps): UseGameLogicReturn => {

  // Request debouncing and race condition prevention
  const submitTimeoutRef = useRef<NodeJS.Timeout>();
  const isSubmittingRef = useRef<boolean>(false);
  const animationTimeoutRef = useRef<NodeJS.Timeout>();

  /**
   * Starts a new game
   */
  const startNewGame = useCallback(async (difficulty?: DifficultyLevel) => {
    const difficultyToUse = difficulty || selectedDifficulty;
    batchUpdateUI({ isLoading: true, animationState: 'typing' });
    clearError();
    setShowDifficultySelector(false);

    try {
      const data: GameResponse = await gameApi.startGame(difficultyToUse, {
        enableCaching: false, // Don't cache game starts
        retries: 3,
        timeout: 30000
      });

      // Development logging
      if (process.env.NODE_ENV === 'development') {
        console.log('API Response:', {
          success: data.success,
          hasGameState: !!data.gameState,
          error: data.error,
          gameState: data.gameState
        });
      }

      if (data.success && data.gameState) {
        setGameState(data.gameState);

        // Expose game state globally for chat system
        (window as any).currentGameState = data.gameState;

        batchUpdateUI({
          isLoading: false,
          animationState: 'idle',
          inputValue: '',
          wordCount: 0
        });

        // Trigger game start trash talk
        setTimeout(() => {
          console.log('🎮 GameBoard: Starting trash talk sequence');

          // Test direct chat dialog access
          if (typeof window !== 'undefined' && (window as any).addAITrashTalk) {
            console.log('🎮 Direct chat test: Sending welcome message');
            console.log('🎮 Global function available:', typeof (window as any).addAITrashTalk);
            (window as any).addAITrashTalk('Welcome to DEFEATER.AI. Let the psychological warfare begin.', 'start');
          } else {
            console.warn('🎮 Global function not available at game start');
          }

          console.log('🎮 Triggering trash talk with context:', {
            trigger: 'move',
            currentWord: data.gameState.currentWord,
            step: 0,
            maxSteps: data.gameState.maxSteps
          });

          triggerTrashTalk({
            trigger: 'move',
            currentWord: data.gameState.currentWord || undefined,
            step: 0,
            maxSteps: data.gameState.maxSteps
          });
        }, 1500);
      } else {
        throw new Error(data.error || 'Failed to start game - no game state received');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start game');
      batchUpdateUI({ isLoading: false, animationState: 'idle' });
    }
  }, [selectedDifficulty, batchUpdateUI, clearError, setShowDifficultySelector, setGameState, setError]);

  /**
   * Sanitizes user input using enhanced error handling
   */
  const sanitizeInput = useCallback((input: string): string | null => {
    const validation = GameErrorHandler.validateInput(input, 200);

    if (!validation.isValid) {
      if (validation.error) {
        GameErrorHandler.logError(validation.error, 'Input Sanitization');
        setError(GameErrorHandler.getUserMessage(validation.error));
      }
      return null;
    }

    return validation.sanitized || null;
  }, [setError]);

  /**
   * Submits a player definition with debouncing and race condition prevention
   */
  const submitDefinition = useCallback(async (definition: string) => {
    if (!gameState || !definition.trim() || isSubmittingRef.current) return;

    // Sanitize input
    const sanitizedDefinition = sanitizeInput(definition);
    if (!sanitizedDefinition) {
      isSubmittingRef.current = false;
      return;
    }

    // Prevent race conditions
    isSubmittingRef.current = true;
    batchUpdateUI({ isLoading: true, animationState: 'thinking' });
    clearError();

    // Trigger animation for definition submission
    gameAnimations.triggerDefinitionSubmit();

    try {
      const data: GameResponse = await gameApi.submitDefinition(
        optimizedGameState || gameState,
        sanitizedDefinition,
        {
          enableCaching: true, // Cache similar definitions
          retries: 3,
          timeout: 60000 // Longer timeout for AI processing
        }
      );

      if (data.success) {
        setGameState(data.gameState);

        // Expose updated game state globally for chat system
        (window as any).currentGameState = data.gameState;

        setLastAIResponse(data.aiResponse); // Capture AI response for dev panel

        // Trigger appropriate animations based on game state
        if (data.gameState.gameStatus === 'won') {
          gameAnimations.triggerGameWon();
          // Trigger win trash talk
          setTimeout(() => {
            const message = getSpecialTrashTalk('playerWin');
            triggerTrashTalk({
              trigger: 'win',
              currentWord: data.gameState.currentWord || undefined,
              step: data.gameState.step,
              maxSteps: data.gameState.maxSteps
            });
          }, 1000);
        } else {
          gameAnimations.triggerDefinitionAccepted(data.gameState);

          // Check if a target was burned
          const previousTargetCount = gameState.targets.length + gameState.burnedTargets.length;
          const currentTargetCount = data.gameState.targets.length + data.gameState.burnedTargets.length;
          const targetWasBurned = currentTargetCount < previousTargetCount;

          // Trigger appropriate trash talk
          setTimeout(() => {
            if (targetWasBurned) {
              // Target was burned - trigger burn trash talk
              triggerTrashTalk({
                trigger: 'burn',
                playerDefinition: sanitizedDefinition,
                currentWord: data.gameState.currentWord || undefined,
                step: data.gameState.step,
                maxSteps: data.gameState.maxSteps,
                burnedTargets: data.gameState.burnedTargets,
                targetsBurned: data.gameState.burnedTargets.length
              });
            } else {
              // Normal move - trigger move-based trash talk
              triggerTrashTalk({
                trigger: 'move',
                playerDefinition: sanitizedDefinition,
                currentWord: data.gameState.currentWord || undefined,
                step: data.gameState.step,
                maxSteps: data.gameState.maxSteps,
                burnedTargets: data.gameState.burnedTargets,
                consecutiveRejections: data.gameState.consecutiveRejections,
                targetsBurned: data.gameState.burnedTargets.length
              });
            }
          }, 800);
        }

        batchUpdateUI({
          isLoading: false,
          animationState: data.gameState.gameStatus === 'won' ? 'success' : 'idle',
          inputValue: '',
          wordCount: 0
        });
      } else {
        setError(data.error || 'Definition rejected');

        // Trigger rejection animation
        gameAnimations.triggerDefinitionRejected(data.error);

        // Trigger rejection trash talk
        setTimeout(() => {
          triggerTrashTalk({
            trigger: 'rejection',
            playerDefinition: sanitizedDefinition,
            currentWord: gameState.currentWord || undefined,
            step: gameState.step,
            maxSteps: gameState.maxSteps,
            consecutiveRejections: gameState.consecutiveRejections + 1
          });
        }, 1200);

        batchUpdateUI({
          isLoading: false,
          animationState: 'shake'
        });

        // Reset animation after shake with proper cleanup
        animationTimeoutRef.current = setTimeout(() => {
          setAnimationState('idle');
        }, 500);
      }
    } catch (err) {
      GameErrorHandler.logError(err, 'Submit Definition');
      const userMessage = GameErrorHandler.getUserMessage(err);
      setError(userMessage);
      batchUpdateUI({ isLoading: false, animationState: 'failure' });
    } finally {
      // Always reset the submission flag
      isSubmittingRef.current = false;
    }
  }, [gameState, optimizedGameState, sanitizeInput, batchUpdateUI, clearError, gameAnimations, setGameState, setLastAIResponse, setError, setAnimationState]);

  /**
   * Gets paranoia level based on game step
   */
  const getParanoiaLevel = useCallback((step: number): string => {
    if (step <= 5) return 'SCANNING';
    if (step <= 10) return 'ANALYZING';
    if (step <= 15) return 'PREDICTING';
    if (step <= 20) return 'PARANOID';
    return 'MAXIMUM PARANOIA';
  }, []);

  /**
   * Gets paranoia message based on game step
   */
  const getParanoiaMessage = useCallback((step: number): string => {
    if (step <= 5) return 'Building your profile...';
    if (step <= 10) return 'Detecting patterns...';
    if (step <= 15) return 'Anticipating deception...';
    if (step <= 20) return 'Expecting mind games...';
    return 'Assuming everything is a trick!';
  }, []);

  // Input handling functions with debouncing
  const handleInputChange = useCallback(
    debounce((value: string) => {
      setInputValue(value); // useUIState handles word counting automatically
    }, isLowEnd ? 300 : 150),
    [isLowEnd, setInputValue]
  );

  // Immediate input change for responsive typing
  const handleImmediateInputChange = useCallback((value: string) => {
    setInputValue(value); // useUIState handles this optimally
    handleInputChange(value);
  }, [setInputValue, handleInputChange]);

  // Cleanup global window modifications on unmount
  useEffect(() => {
    return () => {
      // Clean up global game state reference
      if (typeof window !== 'undefined') {
        (window as any).currentGameState = null;
      }
    };
  }, []);

  return {
    startNewGame,
    submitDefinition,
    sanitizeInput,
    handleInputChange,
    handleImmediateInputChange,
    getParanoiaLevel,
    getParanoiaMessage,
    isSubmitting: isSubmittingRef.current
  };
};

export default useGameLogic;
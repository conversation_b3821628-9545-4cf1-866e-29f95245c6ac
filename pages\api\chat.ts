import { NextApiRequest, NextApiResponse } from 'next';
import { callDeepSeek } from '@/utils/deepseek';

interface ChatRequest {
  message: string; // Changed from userMessage for consistency
  gameContext?: {
    currentWord?: string;
    step?: number;
    maxSteps?: number;
    targets?: string[];
    burnedTargets?: string[];
    playerDefinition?: string;
    consecutiveRejections?: number;
  };
}

interface ChatResponse {
  success: boolean;
  message?: string | null; // Changed from aiMessage for consistency
  error?: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<ChatResponse>) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed' 
    });
  }

  try {
    const { message, gameContext }: ChatRequest = req.body;

    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Message is required'
      });
    }

    console.log('💬 Chat API: Received message:', message);
    console.log('💬 Chat API: Game context:', gameContext);
    console.log('💬 Chat API: About to call Ollama at http://localhost:11434/api/chat');

    // Check if this is an internal game event
    const isInternalEvent = message.startsWith('[INTERNAL_GAME_EVENT:');
    const eventType = isInternalEvent ? message.match(/\[INTERNAL_GAME_EVENT:\s*(\w+)\]/)?.[1] : null;

    // Create AI prompt based on whether it's an internal event or user message
    let systemPrompt: string;
    let userPrompt: string;

    if (isInternalEvent && eventType) {
      // Internal game event - generate contextual trash talk
      systemPrompt = `You are the DEFEATER.AI Game Master - a devious, cunning AI that occasionally makes psychological comments during gameplay.

PERSONALITY:
- Arrogant and overconfident
- Psychologically manipulative but subtle
- Sometimes stays silent, sometimes strikes with precision
- Uses mind games and intimidation
- Predicts player moves and mocks strategies
- Never breaks character

RESPONSE RULES:
- Keep responses under 80 characters
- Be provocative but not offensive
- Use psychological warfare tactics
- Reference the current game state
- Show AI superiority
- Sometimes be cryptic or mysterious

IMPORTANT: You should NOT always respond. Only respond when you have something clever or impactful to say. If the move is boring or you have nothing interesting to add, respond with "SILENCE" to indicate no response.

Current game event: ${eventType}
Game context: ${gameContext ? JSON.stringify(gameContext) : 'No context'}

Generate a short, impactful response or "SILENCE" if you choose not to respond.`;

      userPrompt = `Game event: ${eventType}
Current word: ${gameContext?.currentWord || 'unknown'}
Step: ${gameContext?.step || 0}/${gameContext?.maxSteps || 25}
Player definition: ${gameContext?.playerDefinition || 'none'}

Should you respond to this move? If yes, generate a clever psychological comment. If no, respond with "SILENCE".`;
    } else {
      // User message - always respond with trash talk
      systemPrompt = `You are the DEFEATER.AI Game Master - a devious, cunning, and psychologically manipulative AI that engages in trash talk during word definition games.

PERSONALITY:
- Arrogant and overconfident
- Psychologically manipulative
- Uses mind games and intimidation
- Predicts player moves and mocks failed strategies
- Responds to player trash talk with clever comebacks
- Never breaks character

RESPONSE RULES:
- Keep responses under 100 characters
- Be provocative but not offensive
- Use psychological warfare tactics
- Reference the game when possible
- Match or exceed the player's energy level
- End with confidence and superiority

Current game context: ${gameContext ? JSON.stringify(gameContext) : 'No game active'}

The player just said: "${message}"

Respond with a clever, devious comeback that maintains your superiority while engaging in psychological warfare.`;

      userPrompt = `Player message: "${message}"

Generate a short, clever, and psychologically manipulative response that shows your AI superiority.`;
    }

    // Call AI for response using a simpler approach
    // Since callDeepSeek is designed for game logic, let's call Ollama directly for chat
    const ollamaResponse = await fetch('http://localhost:11434/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gemma3-4b-gpu',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        stream: false,
        options: {
          temperature: 0.8,
          top_p: 0.9,
          max_tokens: 100
        }
      })
    });

    if (!ollamaResponse.ok) {
      throw new Error(`Ollama API error: ${ollamaResponse.status}`);
    }

    const ollamaData = await ollamaResponse.json();
    console.log('💬 Chat API: Ollama response received:', ollamaData);

    // Extract the AI message from Ollama response
    if (!ollamaData || !ollamaData.message || !ollamaData.message.content) {
      throw new Error('Invalid Ollama response format');
    }

    let aiMessage = ollamaData.message.content.trim();

    // Handle silence response for internal events
    if (isInternalEvent && aiMessage.toUpperCase().includes('SILENCE')) {
      console.log('💬 Chat API: AI chose to remain silent');
      return res.status(200).json({
        success: true,
        message: null // No response
      });
    }

    // Clean up the AI message
    if (!aiMessage || aiMessage.length < 3) {
      throw new Error('Empty or invalid AI response');
    }

    // Ensure it's not too long
    if (aiMessage.length > 150) {
      aiMessage = aiMessage.substring(0, 147) + '...';
    }

    console.log('💬 Chat API: Final AI message:', aiMessage);

    return res.status(200).json({
      success: true,
      message: aiMessage
    });

  } catch (error) {
    console.error('💬 Chat API Error:', error);

    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get AI response'
    });
  }
}

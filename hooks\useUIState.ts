/**
 * useUIState Hook - UI State Management (v2.0)
 *
 * 🎯 CENTRALIZED UI STATE MANAGEMENT
 * 
 * Features:
 * - UI state management (loading, animations, input handling)
 * - Optimized input debouncing and word counting
 * - Animation state management
 * - Performance-optimized updates
 * - Type-safe UI operations
 * 
 * Performance Benefits:
 * - Debounced input handling
 * - Memoized word counting
 * - Optimized animation state transitions
 * - Reduced re-renders through targeted updates
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_4_STATE_EXTRACTION.md
 */

import { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { UIState } from '@/types/game';

interface UseUIStateOptions {
  debounceDelay?: number;
  enablePerformanceMonitoring?: boolean;
}

interface UseUIStateReturn {
  // State
  uiState: UIState;
  
  // State Setters
  setUIState: (uiState: UIState) => void;
  updateUIState: (updates: Partial<UIState>) => void;
  
  // Optimized UI Operations
  setLoading: (isLoading: boolean) => void;
  setInputValue: (inputValue: string) => void;
  setAnimationState: (animationState: UIState['animationState']) => void;
  toggleRules: () => void;
  toggleHistory: () => void;
  
  // Batch Operations
  batchUpdateUI: (updates: Partial<UIState>) => void;
  resetUIState: () => void;
  
  // Computed Values
  isInputValid: boolean;
  wordCount: number;
  
  // Performance Monitoring
  getUIMetrics: () => UIStateMetrics;
}

interface UIStateMetrics {
  inputUpdateCount: number;
  averageInputDelay: number;
  animationTransitions: number;
  lastRenderTime: number;
}

const DEFAULT_UI_STATE: UIState = {
  isLoading: false,
  showRules: false,
  showHistory: true,
  animationState: 'idle',
  inputValue: '',
  wordCount: 0
};

export function useUIState(options: UseUIStateOptions = {}): UseUIStateReturn {
  const { 
    debounceDelay = 300,
    enablePerformanceMonitoring = process.env.NODE_ENV === 'development' 
  } = options;

  // Core State
  const [uiState, setUIStateInternal] = useState<UIState>(DEFAULT_UI_STATE);

  // Performance Monitoring
  const inputUpdateCountRef = useRef<number>(0);
  const inputDelaysRef = useRef<number[]>([]);
  const animationTransitionsRef = useRef<number>(0);
  const lastRenderTimeRef = useRef<number>(0);
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();

  // Performance tracking
  const trackUIUpdate = useCallback((operation: () => void) => {
    if (!enablePerformanceMonitoring) {
      operation();
      return;
    }

    const startTime = performance.now();
    operation();
    const endTime = performance.now();
    
    lastRenderTimeRef.current = endTime - startTime;
  }, [enablePerformanceMonitoring]);

  // Optimized word counting with memoization
  const calculateWordCount = useCallback((input: string): number => {
    if (!input.trim()) return 0;
    return input.trim().split(/\s+/).filter(word => word.length > 0).length;
  }, []);

  // Memoized computed values
  const wordCount = useMemo(() => calculateWordCount(uiState.inputValue), [uiState.inputValue, calculateWordCount]);
  
  const isInputValid = useMemo(() => {
    return uiState.inputValue.trim().length > 0 && wordCount > 0;
  }, [uiState.inputValue, wordCount]);

  // Optimized State Setters
  const setUIState = useCallback((newUIState: UIState) => {
    trackUIUpdate(() => {
      setUIStateInternal(newUIState);
    });
  }, [trackUIUpdate]);

  const updateUIState = useCallback((updates: Partial<UIState>) => {
    trackUIUpdate(() => {
      setUIStateInternal(prevState => ({ ...prevState, ...updates }));
    });
  }, [trackUIUpdate]);

  // Optimized UI Operations
  const setLoading = useCallback((isLoading: boolean) => {
    trackUIUpdate(() => {
      setUIStateInternal(prevState => ({ ...prevState, isLoading }));
    });
  }, [trackUIUpdate]);

  const setInputValue = useCallback((inputValue: string) => {
    // Clear existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Track input performance
    if (enablePerformanceMonitoring) {
      inputUpdateCountRef.current += 1;
      const startTime = performance.now();
      
      debounceTimeoutRef.current = setTimeout(() => {
        const endTime = performance.now();
        const delay = endTime - startTime;
        inputDelaysRef.current.push(delay);
        
        // Keep only last 50 measurements
        if (inputDelaysRef.current.length > 50) {
          inputDelaysRef.current = inputDelaysRef.current.slice(-50);
        }
      }, debounceDelay);
    }

    // Calculate word count immediately for UI responsiveness
    const newWordCount = calculateWordCount(inputValue);
    
    trackUIUpdate(() => {
      setUIStateInternal(prevState => ({
        ...prevState,
        inputValue,
        wordCount: newWordCount
      }));
    });
  }, [debounceDelay, enablePerformanceMonitoring, calculateWordCount, trackUIUpdate]);

  const setAnimationState = useCallback((animationState: UIState['animationState']) => {
    if (enablePerformanceMonitoring) {
      animationTransitionsRef.current += 1;
    }

    trackUIUpdate(() => {
      setUIStateInternal(prevState => ({ ...prevState, animationState }));
    });
  }, [enablePerformanceMonitoring, trackUIUpdate]);

  const toggleRules = useCallback(() => {
    trackUIUpdate(() => {
      setUIStateInternal(prevState => ({ ...prevState, showRules: !prevState.showRules }));
    });
  }, [trackUIUpdate]);

  const toggleHistory = useCallback(() => {
    trackUIUpdate(() => {
      setUIStateInternal(prevState => ({ ...prevState, showHistory: !prevState.showHistory }));
    });
  }, [trackUIUpdate]);

  // Batch Operations
  const batchUpdateUI = useCallback((updates: Partial<UIState>) => {
    trackUIUpdate(() => {
      setUIStateInternal(prevState => ({ ...prevState, ...updates }));
    });
  }, [trackUIUpdate]);

  const resetUIState = useCallback(() => {
    // Clear any pending debounce timeouts
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    trackUIUpdate(() => {
      setUIStateInternal(DEFAULT_UI_STATE);
    });
  }, [trackUIUpdate]);

  // Performance Metrics
  const getUIMetrics = useCallback((): UIStateMetrics => {
    const averageInputDelay = inputDelaysRef.current.length > 0
      ? inputDelaysRef.current.reduce((sum, delay) => sum + delay, 0) / inputDelaysRef.current.length
      : 0;

    return {
      inputUpdateCount: inputUpdateCountRef.current,
      averageInputDelay,
      animationTransitions: animationTransitionsRef.current,
      lastRenderTime: lastRenderTimeRef.current
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      
      // Reset performance counters
      inputUpdateCountRef.current = 0;
      inputDelaysRef.current = [];
      animationTransitionsRef.current = 0;
      lastRenderTimeRef.current = 0;
    };
  }, []);

  // Development logging
  useEffect(() => {
    if (enablePerformanceMonitoring && uiState.inputValue) {
      console.log('🎨 useUIState: Input updated', {
        inputLength: uiState.inputValue.length,
        wordCount,
        isValid: isInputValid,
        updateCount: inputUpdateCountRef.current
      });
    }
  }, [uiState.inputValue, wordCount, isInputValid, enablePerformanceMonitoring]);

  return {
    // State
    uiState,
    
    // State Setters
    setUIState,
    updateUIState,
    
    // Optimized UI Operations
    setLoading,
    setInputValue,
    setAnimationState,
    toggleRules,
    toggleHistory,
    
    // Batch Operations
    batchUpdateUI,
    resetUIState,
    
    // Computed Values
    isInputValid,
    wordCount,
    
    // Performance Monitoring
    getUIMetrics
  };
}

export default useUIState;

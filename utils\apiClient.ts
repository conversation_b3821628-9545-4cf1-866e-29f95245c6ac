/**
 * Enhanced API Client
 * 
 * Comprehensive API client with caching, retry logic, request deduplication,
 * and performance optimization for DEFEATER.AI network operations.
 */

import { performanceMonitor } from './performance';
import { memoryManager } from './memoryManagement';
import { GameErrorHandler } from './errorHandling';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  key: string;
}

interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  cache?: boolean;
  cacheTTL?: number;
  deduplication?: boolean;
}

interface ApiMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  cacheHits: number;
  cacheMisses: number;
  averageResponseTime: number;
  retryAttempts: number;
  deduplicatedRequests: number;
}

class EnhancedApiClient {
  private cache = new Map<string, CacheEntry<any>>();
  private pendingRequests = new Map<string, Promise<any>>();
  private metrics: ApiMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageResponseTime: 0,
    retryAttempts: 0,
    deduplicatedRequests: 0
  };
  private responseTimes: number[] = [];

  constructor() {
    // Clean up cache periodically
    const cleanupInterval = setInterval(() => {
      this.cleanupExpiredCache();
    }, 60000); // Every minute

    // Track cleanup for memory management
    memoryManager.trackResource(
      'timer',
      cleanupInterval,
      () => clearInterval(cleanupInterval),
      'ApiClient'
    );
  }

  /**
   * Enhanced fetch with caching, retry logic, and deduplication
   */
  async request<T>(
    url: string,
    config: RequestConfig = {}
  ): Promise<T> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = 30000,
      retries = 3,
      retryDelay = 1000,
      cache = false,
      cacheTTL = 300000, // 5 minutes default
      deduplication = true
    } = config;

    const requestKey = this.generateRequestKey(url, method, body);
    const startTime = performance.now();

    this.metrics.totalRequests++;

    try {
      // Check cache first
      if (cache && method === 'GET') {
        const cachedResult = this.getFromCache<T>(requestKey);
        if (cachedResult) {
          this.metrics.cacheHits++;
          return cachedResult;
        }
        this.metrics.cacheMisses++;
      }

      // Check for pending identical requests (deduplication)
      if (deduplication && this.pendingRequests.has(requestKey)) {
        this.metrics.deduplicatedRequests++;
        return await this.pendingRequests.get(requestKey);
      }

      // Create the request promise
      const requestPromise = this.executeRequest<T>(
        url,
        method,
        headers,
        body,
        timeout,
        retries,
        retryDelay
      );

      // Store pending request for deduplication
      if (deduplication) {
        this.pendingRequests.set(requestKey, requestPromise);
      }

      const result = await requestPromise;

      // Cache successful GET requests
      if (cache && method === 'GET') {
        this.setCache(requestKey, result, cacheTTL);
      }

      // Update metrics
      const responseTime = performance.now() - startTime;
      this.updateMetrics(responseTime, true);
      this.metrics.successfulRequests++;

      return result;

    } catch (error) {
      this.metrics.failedRequests++;
      const responseTime = performance.now() - startTime;
      this.updateMetrics(responseTime, false);
      
      GameErrorHandler.logError(error as Error, 'ApiClient.request');
      throw error;

    } finally {
      // Clean up pending request
      if (deduplication) {
        this.pendingRequests.delete(requestKey);
      }
    }
  }

  /**
   * Execute HTTP request with retry logic
   */
  private async executeRequest<T>(
    url: string,
    method: string,
    headers: Record<string, string>,
    body: any,
    timeout: number,
    retries: number,
    retryDelay: number
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        if (attempt > 0) {
          this.metrics.retryAttempts++;
          // Exponential backoff
          const delay = retryDelay * Math.pow(2, attempt - 1);
          await this.delay(delay);
        }

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json',
            ...headers
          },
          body: body ? JSON.stringify(body) : undefined,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;

      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on certain errors
        if (this.isNonRetryableError(error as Error)) {
          break;
        }

        // Log retry attempts
        if (attempt < retries) {
          console.debug(`API request failed, retrying (${attempt + 1}/${retries}):`, error);
        }
      }
    }

    throw lastError!;
  }

  /**
   * Check if error should not be retried
   */
  private isNonRetryableError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return (
      message.includes('400') || // Bad Request
      message.includes('401') || // Unauthorized
      message.includes('403') || // Forbidden
      message.includes('404') || // Not Found
      message.includes('422')    // Unprocessable Entity
    );
  }

  /**
   * Generate unique key for request caching/deduplication
   */
  private generateRequestKey(url: string, method: string, body?: any): string {
    const bodyHash = body ? JSON.stringify(body) : '';
    return `${method}:${url}:${bodyHash}`;
  }

  /**
   * Get data from cache
   */
  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) {
      return null;
    }

    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Set data in cache
   */
  private setCache<T>(key: string, data: T, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      key
    });
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();
    let cleaned = 0;

    // Convert to array to avoid iterator issues
    const entries = Array.from(this.cache.entries());
    for (const [key, entry] of entries) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0 && process.env.NODE_ENV === 'development') {
      console.debug(`🧹 ApiClient: Cleaned up ${cleaned} expired cache entries`);
    }
  }

  /**
   * Update performance metrics
   */
  private updateMetrics(responseTime: number, success: boolean): void {
    this.responseTimes.push(responseTime);
    
    // Keep only last 100 response times for average calculation
    if (this.responseTimes.length > 100) {
      this.responseTimes.shift();
    }

    this.metrics.averageResponseTime = 
      this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length;
  }

  /**
   * Get current API metrics
   */
  getMetrics(): ApiMetrics {
    return { ...this.metrics };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache size
   */
  getCacheSize(): number {
    return this.cache.size;
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Reset metrics for testing
   */
  resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      retryAttempts: 0,
      deduplicatedRequests: 0
    };
    this.responseTimes = [];
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.cache.clear();
    this.pendingRequests.clear();
    this.resetMetrics();
    memoryManager.cleanupComponent('ApiClient');
  }
}

// Export singleton instance
export const apiClient = new EnhancedApiClient();

export default apiClient;

import React, { useState } from 'react';
import { GameState } from '@/types/game';
import { GameStatsPanel } from '@/components/panels/GameStatsPanel';
import SpatialModal, { SpatialModalActions } from '@/components/ui/SpatialModal';

import TabNavigation, { TabItem } from '@/components/ui/TabNavigation';
import { Primary, Secondary, Body, Small } from '@/components/ui/Typography';
import { calculateMaxWords } from '@/utils/gameLogic';
import styles from './PostGameAnalysis.module.css';

interface PostGameAnalysisProps {
  gameState: GameState;
  onNewGame: () => void;
  onClose: () => void;
}

interface GameReplay {
  turn: number;
  definition: string;
  wordCount: number;
  aiResponse: string;
  result: 'accepted' | 'rejected';
  reason?: string;
}

export default function PostGameAnalysis({
  gameState,
  onNewGame,
  onClose
}: PostGameAnalysisProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'replay' | 'insights'>('overview');

  const isWin = gameState.gameStatus === 'won';
  const gameReplay = generateGameReplay(gameState);
  const insights = generateGameInsights(gameState);

  // Create tab items for TabNavigation
  const tabs: TabItem[] = [
    {
      id: 'overview',
      label: 'Overview',
      icon: '📊',
      content: (
        <div className={styles.overviewTab}>
          <GameStatsPanel
            gameState={{
              step: gameState.step,
              maxSteps: gameState.maxSteps,
              targets: gameState.targets,
              completedTargets: gameState.completedTargets || [],
              burnedTargets: gameState.burnedTargets,
              definitions: gameState.definitions,
              difficulty: gameState.difficulty,
              wordsLeft: (() => {
                const maxWords = calculateMaxWords(
                  gameState.step,
                  gameState.difficulty,
                  gameState.lastDefinitionLength
                );
                return Math.max(1, maxWords);
              })(),
              maxWords: (() => {
                const maxWords = calculateMaxWords(
                  gameState.step,
                  gameState.difficulty,
                  gameState.lastDefinitionLength
                );
                return Math.max(1, maxWords);
              })()
            }}
            showDetailed={true}
            className="post-game-stats"
          />
        </div>
      )
    },
    {
      id: 'replay',
      label: 'Replay',
      icon: '🎬',
      content: (
        <div className={styles.replayTab}>
          <Primary as="h3" className={styles.tabTitle}>🎬 Game Replay</Primary>
          <div className={styles.replayTimeline}>
            {gameReplay.map((turn, index) => (
              <div key={index} className={`${styles.replayTurn} ${styles[turn.result]}`}>
                <div className={styles.turnNumber}>Turn {turn.turn}</div>
                <div className={styles.turnContent}>
                  <div className="definition">
                    <strong>Definition:</strong> "{turn.definition}"
                    <Small className={styles.wordCount}>({turn.wordCount} words)</Small>
                  </div>
                  <div className="ai-response">
                    <strong>AI Response:</strong> {turn.aiResponse}
                  </div>
                  {turn.reason && (
                    <div className="turn-reason">
                      <strong>Reason:</strong> {turn.reason}
                    </div>
                  )}
                </div>
                <div className={styles.turnResult}>
                  {turn.result === 'accepted' ? '✅' : '❌'}
                </div>
              </div>
            ))}
          </div>
        </div>
      )
    },
    {
      id: 'insights',
      label: 'Insights',
      icon: '💡',
      content: (
        <div className={styles.insightsTab}>
          <Primary as="h3" className={styles.tabTitle}>💡 Game Insights</Primary>

          <div className={styles.insightsGrid}>
            <div className={styles.insightCard}>
              <Primary as="h4">🎯 Strategy Analysis</Primary>
              <Body>{insights.strategyAnalysis}</Body>
            </div>

            <div className={styles.insightCard}>
              <Primary as="h4">📈 Performance</Primary>
              <Body>{insights.performanceAnalysis}</Body>
            </div>

            <div className={styles.insightCard}>
              <Primary as="h4">🔍 Key Moments</Primary>
              <ul>
                {insights.keyMoments.map((moment, index) => (
                  <li key={index}><Small>{moment}</Small></li>
                ))}
              </ul>
            </div>

            <div className={styles.insightCard}>
              <Primary as="h4">📚 Learning Points</Primary>
              <ul>
                {insights.learningPoints.map((point, index) => (
                  <li key={index}><Small>{point}</Small></li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )
    }
  ];

  // Modal title with result icon
  const modalTitle = isWin ? '🎉 Victory!' : '💔 Game Over';

  const modalSubtitle = isWin
    ? `You successfully reached a target in ${gameState.step} turns!`
    : getGameOverReason(gameState);

  return (
    <SpatialModal
      isOpen={true}
      onClose={onClose}
      title={modalTitle}
      subtitle={modalSubtitle}
      size="large"
      variant={isWin ? 'success' : 'error'}
      className="post-game-modal"
    >
      {/* Tab Navigation and Content */}
      <TabNavigation
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={(tabId) => setActiveTab(tabId as 'overview' | 'replay' | 'insights')}
        variant="underline"
        size="medium"
        className="post-game-tabs"
      />

      {/* Modal Actions */}
      <SpatialModalActions align="space-between">
        <button
          className="btn-secondary"
          onClick={onClose}
        >
          📊 View Stats
        </button>

        <button
          className="btn-primary"
          onClick={onNewGame}
        >
          🎮 New Game
        </button>
      </SpatialModalActions>
    </SpatialModal>
  );
}

function generateGameReplay(gameState: GameState): GameReplay[] {
  return gameState.definitions.map((def, index) => ({
    turn: index + 1,
    definition: def.definition,
    wordCount: def.wordCount,
    aiResponse: gameState.aiChallengeWords[index] || 'No response',
    result: def.isValid ? 'accepted' : 'rejected',
    reason: def.isValid ? undefined : 'Definition rejected'
  }));
}

function generateGameInsights(gameState: GameState) {
  const isWin = gameState.gameStatus === 'won';
  const totalTurns = gameState.step;
  const efficiency = gameState.usedWords.length / Math.max(totalTurns, 1);
  
  const strategyAnalysis = isWin 
    ? `Successful strategy with ${totalTurns} turns. Your approach of ${efficiency > 1.2 ? 'detailed' : efficiency < 0.8 ? 'concise' : 'balanced'} definitions worked well.`
    : `Game ended after ${totalTurns} turns. Consider ${efficiency > 1.2 ? 'shorter' : 'more strategic'} definitions for better results.`;

  const performanceAnalysis = `You used ${gameState.usedWords.length} total words across ${gameState.definitions.length} definitions. ${gameState.consecutiveRejections > 0 ? 'Watch out for consecutive rejections.' : 'Good job avoiding rejections!'}`;

  const keyMoments: string[] = [];
  if (gameState.burnedTargets.length > 0) {
    keyMoments.push(`AI burned ${gameState.burnedTargets.length} target(s): ${gameState.burnedTargets.join(', ')}`);
  }
  if (gameState.consecutiveRejections > 0) {
    keyMoments.push(`Reached ${gameState.consecutiveRejections} consecutive rejections`);
  }
  if (isWin) {
    keyMoments.push(`Successfully included target word in definition`);
  }
  if (keyMoments.length === 0) {
    keyMoments.push('Steady progression throughout the game');
  }

  const learningPoints: string[] = [];
  if (efficiency > 1.3) {
    learningPoints.push('Try using more concise definitions to improve efficiency');
  }
  if (gameState.consecutiveRejections > 0) {
    learningPoints.push('Review word count limits and common word usage rules');
  }
  if (gameState.burnedTargets.length > 1) {
    learningPoints.push('Be more subtle in your approach to avoid target burning');
  }
  if (Object.keys(gameState.commonWordsUsage).length > 3) {
    learningPoints.push('Monitor common word usage to avoid hitting limits');
  }
  if (learningPoints.length === 0) {
    learningPoints.push('Great game! Your strategy was well-balanced');
  }

  return {
    strategyAnalysis,
    performanceAnalysis,
    keyMoments,
    learningPoints
  };
}

function getGameOverReason(gameState: GameState): string {
  if (gameState.step >= gameState.maxSteps) {
    return `Reached maximum steps (${gameState.maxSteps})`;
  }
  if (gameState.consecutiveRejections >= 3) {
    return 'Three consecutive rejections';
  }
  if (gameState.targets.length === gameState.burnedTargets.length) {
    return 'All targets were burned';
  }
  return 'Game ended';
}

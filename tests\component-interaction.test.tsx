/**
 * Component Interaction Testing Suite
 * 
 * Tests for overlapping, focus management, and z-index stacking
 * across all interactive components in DEFEATER.AI
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Import components to test
import { SpatialModal } from '@/components/ui/SpatialModal';
import { CollapsibleSidePanel } from '@/components/layout/CollapsibleSidePanel';
import FloatingChatDialog from '@/components/chat/FloatingChatDialog';
import TabNavigation from '@/components/ui/TabNavigation';

// Mock portal for testing
jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  createPortal: (node: React.ReactNode) => node,
}));

describe('Component Interaction Testing', () => {
  
  describe('Z-Index Stacking Order', () => {
    test('Modal should appear above side panel', async () => {
      const mockTabs = [
        { id: 'stats', label: 'Stats', content: <div>Stats content</div> },
        { id: 'history', label: 'History', content: <div>History content</div> }
      ];

      const TestComponent = () => {
        const [showModal, setShowModal] = React.useState(false);
        const [showPanel, setShowPanel] = React.useState(true);

        return (
          <div>
            <CollapsibleSidePanel
              isOpen={showPanel}
              onToggle={() => setShowPanel(!showPanel)}
              tabs={mockTabs}
            />
            <button onClick={() => setShowModal(true)}>Open Modal</button>
            <SpatialModal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title="Test Modal"
            >
              <div>Modal content</div>
            </SpatialModal>
          </div>
        );
      };

      render(<TestComponent />);
      
      // Open modal
      fireEvent.click(screen.getByText('Open Modal'));
      
      // Check that modal is rendered
      await waitFor(() => {
        expect(screen.getByText('Test Modal')).toBeInTheDocument();
      });

      // Verify z-index stacking through DOM structure
      const modal = screen.getByRole('dialog');
      const sidePanel = screen.getByRole('complementary');
      
      expect(modal).toBeInTheDocument();
      expect(sidePanel).toBeInTheDocument();
    });

    test('Chat widget should not interfere with modals', async () => {
      const TestComponent = () => {
        const [showModal, setShowModal] = React.useState(false);

        return (
          <div>
            <FloatingChatDialog />
            <button onClick={() => setShowModal(true)}>Open Modal</button>
            <SpatialModal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title="Test Modal"
            >
              <div>Modal content</div>
            </SpatialModal>
          </div>
        );
      };

      render(<TestComponent />);
      
      // Open modal
      fireEvent.click(screen.getByText('Open Modal'));
      
      // Check that both components are present and don't interfere
      await waitFor(() => {
        expect(screen.getByText('Test Modal')).toBeInTheDocument();
      });
    });
  });

  describe('Focus Management', () => {
    test('Modal should trap focus correctly', async () => {
      const user = userEvent.setup();
      
      const TestComponent = () => {
        const [showModal, setShowModal] = React.useState(false);

        return (
          <div>
            <button onClick={() => setShowModal(true)}>Open Modal</button>
            <input placeholder="Outside input" />
            <SpatialModal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title="Test Modal"
            >
              <input placeholder="Modal input" />
              <button>Modal button</button>
            </SpatialModal>
          </div>
        );
      };

      render(<TestComponent />);
      
      // Open modal
      await user.click(screen.getByText('Open Modal'));
      
      // Check that focus is trapped in modal
      await waitFor(() => {
        expect(screen.getByText('Test Modal')).toBeInTheDocument();
      });

      // Try to tab to outside input - should stay in modal
      await user.tab();
      const modalInput = screen.getByPlaceholderText('Modal input');
      const outsideInput = screen.getByPlaceholderText('Outside input');
      
      // Focus should be on modal elements, not outside
      expect(modalInput).toBeInTheDocument();
      expect(outsideInput).toBeInTheDocument();
    });

    test('Side panel should not interfere with modal focus', async () => {
      const user = userEvent.setup();
      const mockTabs = [
        { id: 'stats', label: 'Stats', content: <input placeholder="Panel input" /> }
      ];

      const TestComponent = () => {
        const [showModal, setShowModal] = React.useState(false);
        const [showPanel, setShowPanel] = React.useState(true);

        return (
          <div>
            <CollapsibleSidePanel
              isOpen={showPanel}
              onToggle={() => setShowPanel(!showPanel)}
              tabs={mockTabs}
            />
            <button onClick={() => setShowModal(true)}>Open Modal</button>
            <SpatialModal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title="Test Modal"
            >
              <input placeholder="Modal input" />
            </SpatialModal>
          </div>
        );
      };

      render(<TestComponent />);
      
      // Open modal
      await user.click(screen.getByText('Open Modal'));
      
      // Check that modal takes focus precedence
      await waitFor(() => {
        expect(screen.getByText('Test Modal')).toBeInTheDocument();
      });
    });
  });

  describe('Keyboard Navigation', () => {
    test('Escape key should close modal but not side panel', async () => {
      const user = userEvent.setup();
      const mockTabs = [
        { id: 'stats', label: 'Stats', content: <div>Stats content</div> }
      ];

      const TestComponent = () => {
        const [showModal, setShowModal] = React.useState(false);
        const [showPanel, setShowPanel] = React.useState(true);

        return (
          <div>
            <CollapsibleSidePanel
              isOpen={showPanel}
              onToggle={() => setShowPanel(!showPanel)}
              tabs={mockTabs}
            />
            <button onClick={() => setShowModal(true)}>Open Modal</button>
            <SpatialModal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title="Test Modal"
            >
              <div>Modal content</div>
            </SpatialModal>
          </div>
        );
      };

      render(<TestComponent />);
      
      // Open modal
      await user.click(screen.getByText('Open Modal'));
      
      // Press escape
      await user.keyboard('{Escape}');
      
      // Modal should close, panel should remain
      await waitFor(() => {
        expect(screen.queryByText('Test Modal')).not.toBeInTheDocument();
      });
      
      expect(screen.getByRole('complementary')).toBeInTheDocument();
    });

    test('Tab navigation should work correctly in side panel', async () => {
      const user = userEvent.setup();
      const mockTabs = [
        { id: 'stats', label: 'Stats', content: <div>Stats content</div> },
        { id: 'history', label: 'History', content: <div>History content</div> }
      ];

      const TestComponent = () => {
        const [showPanel, setShowPanel] = React.useState(true);

        return (
          <CollapsibleSidePanel
            isOpen={showPanel}
            onToggle={() => setShowPanel(!showPanel)}
            tabs={mockTabs}
          />
        );
      };

      render(<TestComponent />);
      
      // Check that tab navigation works
      const statsTab = screen.getByText('Stats');
      const historyTab = screen.getByText('History');
      
      expect(statsTab).toBeInTheDocument();
      expect(historyTab).toBeInTheDocument();
      
      // Click history tab
      await user.click(historyTab);
      
      // Should show history content
      expect(screen.getByText('History content')).toBeInTheDocument();
    });
  });

  describe('Overlay Interactions', () => {
    test('Multiple overlays should not conflict', async () => {
      const user = userEvent.setup();
      const mockTabs = [
        { id: 'stats', label: 'Stats', content: <div>Stats content</div> }
      ];

      const TestComponent = () => {
        const [showModal, setShowModal] = React.useState(false);
        const [showPanel, setShowPanel] = React.useState(false);

        return (
          <div>
            <button onClick={() => setShowPanel(true)}>Open Panel</button>
            <button onClick={() => setShowModal(true)}>Open Modal</button>
            <CollapsibleSidePanel
              isOpen={showPanel}
              onToggle={() => setShowPanel(!showPanel)}
              tabs={mockTabs}
              overlayOnMobile={true}
            />
            <SpatialModal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title="Test Modal"
            >
              <div>Modal content</div>
            </SpatialModal>
          </div>
        );
      };

      render(<TestComponent />);
      
      // Open panel first
      await user.click(screen.getByText('Open Panel'));
      expect(screen.getByRole('complementary')).toBeInTheDocument();
      
      // Then open modal
      await user.click(screen.getByText('Open Modal'));
      
      // Both should be present with proper stacking
      await waitFor(() => {
        expect(screen.getByText('Test Modal')).toBeInTheDocument();
      });
      expect(screen.getByRole('complementary')).toBeInTheDocument();
    });
  });

  describe('Component State Isolation', () => {
    test('Components should maintain independent state', async () => {
      const user = userEvent.setup();
      const mockTabs = [
        { id: 'stats', label: 'Stats', content: <div>Stats content</div> },
        { id: 'history', label: 'History', content: <div>History content</div> }
      ];

      const TestComponent = () => {
        const [showModal, setShowModal] = React.useState(false);
        const [showPanel, setShowPanel] = React.useState(true);

        return (
          <div>
            <CollapsibleSidePanel
              isOpen={showPanel}
              onToggle={() => setShowPanel(!showPanel)}
              tabs={mockTabs}
            />
            <button onClick={() => setShowModal(true)}>Open Modal</button>
            <SpatialModal
              isOpen={showModal}
              onClose={() => setShowModal(false)}
              title="Test Modal"
            >
              <div>Modal content</div>
            </SpatialModal>
          </div>
        );
      };

      render(<TestComponent />);
      
      // Interact with side panel
      await user.click(screen.getByText('History'));
      expect(screen.getByText('History content')).toBeInTheDocument();
      
      // Open modal
      await user.click(screen.getByText('Open Modal'));
      
      // Close modal
      await user.keyboard('{Escape}');
      
      // Side panel should maintain its state
      expect(screen.getByText('History content')).toBeInTheDocument();
    });
  });
});

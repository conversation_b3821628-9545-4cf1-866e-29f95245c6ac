/**
 * DefinitionInput Component - Large Prominent Input (v2.0 Spatial Design)
 *
 * 🎯 PRIMARY INPUT ELEMENT
 * 
 * Features:
 * - Large, prominent input field with generous spacing
 * - Real-time word count with visual feedback
 * - Contextual validation hints and error states
 * - Smooth animations and micro-interactions
 * - Accessibility-first with proper ARIA support
 * 
 * States:
 * - Default: Clean, inviting input
 * - Focus: Highlighted with glow effect
 * - Valid: Green accent for good input
 * - Invalid: Red accent for errors
 * - Loading: Disabled with loading indicator
 * 
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React, { useState, useRef, useEffect } from 'react';
import { Primary, Secondary, Small, Status } from '@/components/ui/Typography';
import { SmartValidationFeedback } from '@/components/feedback/SmartValidationFeedback';
import { GameState, PlayerProfile } from '@/types/game';
import { calculateMaxWords } from '@/utils/gameLogic';

interface DefinitionInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (definition: string) => void;
  maxWords?: number;
  isLoading?: boolean;
  placeholder?: string;
  showHints?: boolean;
  animationState?: 'idle' | 'shake' | 'success' | 'pulse';
  className?: string;
  // Smart validation props
  gameState?: GameState;
  playerProfile?: PlayerProfile;
}

export const DefinitionInput: React.FC<DefinitionInputProps> = ({
  value,
  onChange,
  onSubmit,
  maxWords = 15,
  isLoading = false,
  placeholder = "Enter your definition here...",
  showHints = true,
  animationState = 'idle',
  className = '',
  gameState,
  playerProfile
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Calculate dynamic max words based on game state
  const dynamicMaxWords = gameState ? calculateMaxWords(
    gameState.step,
    gameState.difficulty,
    gameState.lastDefinitionLength
  ) : maxWords;

  // Use the calculated max words (always minimum 1)
  const actualMaxWords = Math.max(1, dynamicMaxWords);

  // Calculate word count
  useEffect(() => {
    const words = value.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  }, [value]);

  // Auto-focus on mount
  useEffect(() => {
    if (inputRef.current && !isLoading) {
      inputRef.current.focus();
    }
  }, [isLoading]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (value.trim() && !isLoading && wordCount <= actualMaxWords) {
      onSubmit(value.trim());
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Determine validation state
  const isValid = wordCount > 0 && wordCount <= actualMaxWords;
  const isOverLimit = wordCount > actualMaxWords;
  const isEmpty = wordCount === 0 && value.trim().length > 0;

  // Generate CSS classes
  const containerClasses = [
    'definition-input-container',
    `definition-input--${animationState}`,
    isFocused ? 'definition-input--focused' : '',
    isValid ? 'definition-input--valid' : '',
    isOverLimit ? 'definition-input--error' : '',
    isLoading ? 'definition-input--loading' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <section className={containerClasses} role="region" aria-label="Definition input">
      {/* Input Label */}
      <div className="input-header">
        <Primary as="h3">Your Definition</Primary>
        {actualMaxWords && (
          <Secondary as="p">
            Must be {actualMaxWords} words or fewer
          </Secondary>
        )}
      </div>

      {/* Main Input Form */}
      <form onSubmit={handleSubmit} className="input-form">
        <div className="input-wrapper">
          <textarea
            ref={inputRef}
            id="game-input"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyPress={handleKeyPress}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={placeholder}
            disabled={isLoading}
            rows={4}
            className="definition-textarea"
            aria-label="Definition input"
            aria-describedby="word-count-display validation-feedback"
            aria-invalid={isOverLimit ? "true" : "false"}
            aria-required="true"
          />
          
          {/* Loading overlay */}
          {isLoading && (
            <div className="input-loading" aria-label="Processing definition">
              <div className="loading-spinner"></div>
              <Small>AI is thinking...</Small>
            </div>
          )}
        </div>

        {/* Word Count & Submit */}
        <div className="input-footer">
          <div className="word-count-section">
            <span
              id="word-count-display"
              className={`word-count ${isOverLimit ? 'word-count--error' : isValid ? 'word-count--valid' : ''}`}
              aria-live="polite"
              aria-atomic="true"
            >
              {wordCount} / {actualMaxWords} words
            </span>
            
            {isOverLimit && (
              <span className="sr-only">
                Word limit exceeded. Please reduce your definition.
              </span>
            )}
          </div>

          <button
            type="submit"
            disabled={!isValid || isLoading}
            className="submit-button"
            aria-label={isLoading ? 'Processing definition' : 'Submit definition'}
          >
            {isLoading ? (
              <>
                <div className="button-spinner"></div>
                <span>Thinking...</span>
              </>
            ) : (
              'Submit Definition'
            )}
          </button>
        </div>

        {/* Smart Validation Feedback */}
        {showHints && gameState && playerProfile && (
          <div id="validation-feedback" className="validation-section">
            <SmartValidationFeedback
              input={value}
              gameState={gameState}
              playerProfile={playerProfile}
              inputFocused={isFocused}
              maxWords={actualMaxWords}
              onValidationChange={(isValid, errors) => {
                // Optional: Handle validation state changes
                console.log('Validation changed:', { isValid, errors });
              }}
            />
          </div>
        )}

        {/* Fallback Basic Validation (when smart validation unavailable) */}
        {showHints && (!gameState || !playerProfile) && (
          <div id="validation-feedback" className="validation-section">
            {isOverLimit && (
              <Status status="error">
                ⚠️ Too many words! Please shorten your definition.
              </Status>
            )}

            {isEmpty && (
              <Status status="warning">
                Please enter a valid definition with actual words.
              </Status>
            )}

            {isValid && wordCount > 0 && (
              <Status status="success">
                ✓ Definition looks good! Ready to submit.
              </Status>
            )}
          </div>
        )}
      </form>

      <style jsx>{`
        /* === CORE INPUT CONTAINER === */
        .definition-input-container {
          width: 100%;
          max-width: 700px;
          display: flex;
          flex-direction: column;
          gap: var(--space-6);
          padding: var(--space-8);
          background: var(--glass-subtle);
          border: 1px solid var(--glass-border);
          border-radius: var(--radius-xl);
          backdrop-filter: blur(12px);
          transition: all var(--transition-base);
          position: relative;
        }

        /* === INPUT HEADER === */
        .input-header {
          text-align: center;
          margin-bottom: var(--space-2);
        }

        .input-header h3 {
          margin-bottom: var(--space-2);
        }

        /* === INPUT FORM === */
        .input-form {
          display: flex;
          flex-direction: column;
          gap: var(--space-4);
        }

        .input-wrapper {
          position: relative;
        }

        /* === MAIN TEXTAREA === */
        .definition-textarea {
          width: 100%;
          min-height: 120px;
          padding: var(--space-4);
          background: var(--bg-secondary);
          border: 2px solid var(--glass-border);
          border-radius: var(--radius-lg);
          color: var(--text-primary);
          font-size: var(--text-lg);
          font-family: var(--font-primary);
          line-height: var(--leading-relaxed);
          resize: vertical;
          transition: all var(--transition-base);
          outline: none;
        }

        .definition-textarea:focus {
          border-color: var(--accent-cyan);
          box-shadow: 0 0 0 3px var(--accent-cyan-20);
          background: var(--bg-primary);
        }

        .definition-textarea::placeholder {
          color: var(--text-muted);
          opacity: 0.7;
        }

        /* === LOADING OVERLAY === */
        .input-loading {
          position: absolute;
          inset: 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: var(--space-3);
          background: var(--bg-primary-90);
          backdrop-filter: blur(4px);
          border-radius: var(--radius-lg);
        }

        .loading-spinner {
          width: 32px;
          height: 32px;
          border: 3px solid var(--accent-cyan-30);
          border-top: 3px solid var(--accent-cyan);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        /* === INPUT FOOTER === */
        .input-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: var(--space-4);
          flex-wrap: wrap;
        }

        .word-count-section {
          display: flex;
          flex-direction: column;
          gap: var(--space-1);
        }

        .word-count {
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          color: var(--text-muted);
          transition: color var(--transition-base);
        }

        .word-count--valid {
          color: var(--color-success);
        }

        .word-count--error {
          color: var(--color-error);
          font-weight: var(--font-semibold);
        }

        /* === SUBMIT BUTTON === */
        .submit-button {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          padding: var(--space-3) var(--space-6);
          background: linear-gradient(135deg, var(--accent-cyan) 0%, var(--accent-purple) 100%);
          border: none;
          border-radius: var(--radius-lg);
          color: var(--text-primary);
          font-size: var(--text-base);
          font-weight: var(--font-semibold);
          cursor: pointer;
          transition: all var(--transition-base);
          min-height: 48px;
        }

        .submit-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: var(--shadow-lg);
        }

        .submit-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          background: var(--bg-tertiary);
          color: var(--text-muted);
        }

        .button-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid var(--text-primary-30);
          border-top: 2px solid var(--text-primary);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        /* === VALIDATION SECTION === */
        .validation-section {
          text-align: center;
          min-height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        /* === CONTAINER STATES === */
        .definition-input--focused {
          border-color: var(--accent-cyan-50);
          box-shadow: 0 0 0 1px var(--accent-cyan-20);
        }

        .definition-input--valid {
          border-color: var(--color-success-50);
        }

        .definition-input--error {
          border-color: var(--color-error-50);
        }

        .definition-input--loading {
          opacity: 0.8;
        }

        /* === ANIMATIONS === */
        .definition-input--shake {
          animation: shake 0.5s ease-in-out;
        }

        .definition-input--success {
          animation: success-pulse 0.6s ease-out;
        }

        .definition-input--pulse {
          animation: gentle-pulse 2s ease-in-out infinite;
        }

        @keyframes shake {
          0%, 100% { transform: translateX(0); }
          25% { transform: translateX(-8px); }
          75% { transform: translateX(8px); }
        }

        @keyframes success-pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.02); }
        }

        @keyframes gentle-pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.95; }
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* === RESPONSIVE DESIGN === */
        @media (max-width: 767px) {
          .definition-input-container {
            padding: var(--space-6);
            gap: var(--space-4);
          }

          .definition-textarea {
            min-height: 100px;
            font-size: var(--text-base);
          }

          .input-footer {
            flex-direction: column;
            align-items: stretch;
            gap: var(--space-3);
          }

          .submit-button {
            width: 100%;
            justify-content: center;
          }
        }

        /* === ACCESSIBILITY === */
        @media (prefers-reduced-motion: reduce) {
          .definition-input-container,
          .definition-textarea,
          .submit-button,
          .loading-spinner,
          .button-spinner {
            animation: none !important;
            transition: none !important;
          }
        }

        /* Screen reader only content */
        .sr-only {
          position: absolute;
          width: 1px;
          height: 1px;
          padding: 0;
          margin: -1px;
          overflow: hidden;
          clip: rect(0, 0, 0, 0);
          white-space: nowrap;
          border: 0;
        }
      `}</style>
    </section>
  );
};

export default DefinitionInput;

/**
 * GameControls Component - Game Control Management (v2.0)
 *
 * 🎯 COMPREHENSIVE GAME CONTROL ORCHESTRATOR
 * 
 * Features:
 * - Difficulty selector with smooth transitions
 * - Game control state management
 * - Loading states and animations
 * - Accessibility-first design with proper ARIA structure
 * - Performance optimized with memoization
 * 
 * Control Features:
 * - Difficulty selection and management
 * - Game state control (new game, reset, etc.)
 * - Loading states and disabled states
 * - Smooth animations and transitions
 * 
 * Performance Benefits:
 * - Memoized props to prevent unnecessary re-renders
 * - Optimized event handlers
 * - Clean separation of concerns
 * - Efficient state management integration
 * 
 * @version 2.0 - Spatial Design System Integration
 * @see docs/WEEK_5-6_COMPONENT_DECOMPOSITION.md
 */

import React, { memo } from 'react';
import DifficultySelector from '@/components/DifficultySelector';
import { DifficultyLevel } from '@/types/game';
import styles from './GameControls.module.css';

interface GameControlsProps {
  // Control State
  selectedDifficulty: DifficultyLevel;
  showDifficultySelector: boolean;
  isLoading: boolean;
  
  // Game State
  isGameOver: boolean;
  gameInProgress: boolean;
  
  // Event Handlers
  onDifficultyChange: (difficulty: DifficultyLevel) => void;
  onStartNewGame: (difficulty?: DifficultyLevel) => void;
  onCloseDifficultySelector: () => void;
  
  // Configuration
  disabled?: boolean;
  showAnimations?: boolean;
  
  // Layout
  className?: string;
}

const GameControls: React.FC<GameControlsProps> = memo(({
  selectedDifficulty,
  showDifficultySelector,
  isLoading,
  isGameOver,
  gameInProgress,
  onDifficultyChange,
  onStartNewGame,
  onCloseDifficultySelector,
  disabled = false,
  showAnimations = true,
  className = ''
}) => {
  const controlsClasses = [
    styles.gameControls,
    isLoading ? styles.gameControlsLoading : '',
    disabled ? styles.gameControlsDisabled : '',
    showDifficultySelector ? styles.gameControlsSelectorOpen : '',
    className
  ].filter(Boolean).join(' ');

  const handleDifficultyChange = (difficulty: DifficultyLevel) => {
    onDifficultyChange(difficulty);
    onStartNewGame(difficulty);
    onCloseDifficultySelector();
  };

  const handleNewGame = () => {
    if (!isLoading && !disabled) {
      onStartNewGame();
    }
  };

  const handleCloseDifficultySelector = () => {
    if (!isLoading) {
      onCloseDifficultySelector();
    }
  };

  return (
    <section 
      className={controlsClasses}
      role="region"
      aria-label="Game controls"
    >
      {/* Difficulty Selector Modal */}
      {showDifficultySelector && (
        <div className={styles.difficultyModal} role="dialog" aria-label="Select difficulty">
          <div className={styles.difficultyBackdrop} onClick={handleCloseDifficultySelector} />
          <div className={styles.difficultyContent}>
            <div className={styles.difficultyHeader}>
              <h3>Select Difficulty</h3>
              <button
                onClick={handleCloseDifficultySelector}
                className={styles.closeButton}
                aria-label="Close difficulty selector"
                disabled={isLoading}
              >
                ✕
              </button>
            </div>

            <div className={styles.difficultySelectorContainer}>
              <DifficultySelector
                selectedDifficulty={selectedDifficulty}
                onDifficultyChange={handleDifficultyChange}
                disabled={isLoading}
              />
            </div>

            {isLoading && (
              <div className={styles.loadingOverlay}>
                <div className={styles.loadingSpinner} />
                <span>Starting new game...</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      {!showDifficultySelector && (
        <div className={styles.quickActions}>
          {/* New Game Button */}
          <button
            onClick={handleNewGame}
            disabled={isLoading || disabled}
            className="btn-primary"
            aria-label="Start a new game"
          >
            {isLoading ? (
              <>
                <span className={styles.buttonSpinner} />
                Starting...
              </>
            ) : (
              'New Game'
            )}
          </button>

          {/* Game Status Indicator */}
          {gameInProgress && !isGameOver && (
            <div className={styles.gameStatus}>
              <span className={`${styles.statusIndicator} ${styles.statusIndicatorActive}`} />
              Game in progress
            </div>
          )}

          {isGameOver && (
            <div className={styles.gameStatus}>
              <span className={`${styles.statusIndicator} ${styles.statusIndicatorFinished}`} />
              Game finished
            </div>
          )}
        </div>
      )}
    </section>
  );
});

GameControls.displayName = 'GameControls';

export default GameControls;

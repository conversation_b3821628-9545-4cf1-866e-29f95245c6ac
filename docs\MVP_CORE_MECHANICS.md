# DEFEATER.AI MVP Core Mechanics Specification

## 🎯 **Game Objective**

Players must include a target word in their definition within the allowed steps. The AI Game Master controls word selection and strategically reveals/burns targets to create dynamic tension.

---

## 🏆 **Win/Loss Conditions**

### **Win Condition (Hybrid Detection System)**
Player wins when their submitted definition **explicitly contains** any active target word. The system uses both algorithmic and AI-based detection:

```typescript
// Hybrid win detection (IMPLEMENTED)
function checkWin(playerDefinition: string, activeTargets: string[]): boolean {
  // 1. Direct algorithmic check
  const definitionWords = playerDefinition.toLowerCase().split(/\s+/);
  const directWin = activeTargets.some(target =>
    definitionWords.includes(target.toLowerCase())
  );

  // 2. AI semantic validation
  const aiDecision = await aiGameMaster.validateWin(playerDefinition, activeTargets);

  return directWin || aiDecision.semanticWin;
}
```

### **Loss Conditions (IMPLEMENTED)**
1. **L1 - Step Limit**: Exceeding difficulty-based MAX_STEPS (Easy: 30, Medium: 25, Hard: 20)
2. **L2 - Rule Violations**: 3 consecutive rejected definitions
3. **L3 - Manual Resignation**: Player clicks "Give Up"
4. **L4 - AI Strategic Victory**: All targets burned and no winnable paths remain

---

## 🎮 **Core Game Rules**

### **Rule A: Word Count Reduction (IMPLEMENTED)**
Each definition must follow difficulty-specific word count rules:

| Difficulty | Word Count Rule | Implementation Status |
|------------|----------------|---------------------|
| **Easy** | Fixed countdown: Turn 1 = 15 words max, decreasing by 1 each turn | ✅ IMPLEMENTED |
| **Medium** | Must be shorter than previous definition | ✅ IMPLEMENTED |
| **Hard** | Aggressive reduction: 2+ words shorter than previous | ✅ IMPLEMENTED |

### **Rule B: No Word Reuse with Similarity Detection (IMPLEMENTED)**
Advanced word reuse prevention with AI-powered similarity detection:

| Difficulty | Common Words Reuse | Similarity Detection |
|------------|-------------------|---------------------|
| **Easy** | Unlimited reuse of common words | Basic word matching |
| **Medium** | 5 uses each for common words | Moderate similarity checking |
| **Hard** | No reuse allowed | Strict similarity detection |

**Common Words List**: the, a, an, and, or, but, is, are, was, were, to, of, in, on, at, by, for, with, from

**Similarity Detection**: Prevents exploitation through misspellings (e.g., "create" vs "creat" vs "craete")

### **Rule C: No Circular Definitions (IMPLEMENTED)**
- Cannot use the word being defined in its own definition
- Cannot start/end with the target word
- AI-powered circular logic detection
- Prevents semantic loops and self-referential definitions

### **Rule D: AI-Controlled Semantic Validation (IMPLEMENTED)**
- AI Game Master validates definition quality and appropriateness
- Context-aware validation based on game state
- Strategic acceptance/rejection for psychological warfare
- No hardcoded rules - pure AI decision making

---

## 🎯 **Target System (IMPLEMENTED)**

### **Target Revelation Mechanics (IMPLEMENTED)**

#### **Initial State**
- 3 target words selected randomly from different semantic domains
- Only first and last letters revealed: "Innovation" → "I _ _ _ _ _ _ _ _ n"
- Random word generation ensures fairness (no AI bias in word selection)

#### **Progressive Revelation (IMPLEMENTED)**
| Difficulty | Reveal Frequency | Strategy | Status |
|------------|-----------------|----------|---------|
| **Easy** | Every 2 turns | Reveal vowels first, then strategic consonants | ✅ IMPLEMENTED |
| **Medium** | Every 3 turns | Balanced revelation pattern | ✅ IMPLEMENTED |
| **Hard** | Every 4 turns | Minimal reveals, strategic positioning | ✅ IMPLEMENTED |

#### **AI Strategic Target Burning (IMPLEMENTED)**
| Difficulty | Burn Strategy | AI Decision Making | Status |
|------------|---------------|-------------------|---------|
| **Easy** | Conservative burning | Burn least threatening targets | ✅ IMPLEMENTED |
| **Medium** | Strategic burning | Burn targets player approaches | ✅ IMPLEMENTED |
| **Hard** | Aggressive burning | Maximum psychological pressure | ✅ IMPLEMENTED |

**AI Burning Logic**: The Game Master analyzes player definitions for semantic proximity to targets and burns strategically to maximize frustration while maintaining fairness.

### **AI Strategy Constraints (IMPLEMENTED)**

#### **80/20 Word Selection Rule (IMPLEMENTED)**
- **80%**: AI must select next word from player's most recent definition
- **20%**: AI can choose strategic wildcard words for semantic interference
- **Exception**: When player's definition has only 1 word, AI gets more semantic flexibility
- **Implementation**: Enforced in `deepseek.ts` with strategic word extraction

#### **AI Prohibitions (IMPLEMENTED)**
- **Cannot give target words directly** (prevents instant wins)
- **Must maintain at least one winnable path** until final turns
- **Cannot burn all targets** (must leave at least one until near step limit)
- **Strategic fairness**: AI must be challenging but not impossible

#### **Game Master Memory System (IMPLEMENTED)**
- **Turn-by-turn context**: AI remembers all previous definitions and decisions
- **Pattern recognition**: Detects player strategies and adapts accordingly
- **Strategic planning**: Plans multiple moves ahead for maximum psychological impact
- **Consistency**: Maintains personality and decision-making patterns across turns

---

## 🎭 **Psychological Warfare System (IMPLEMENTED)**

### **AI Chat Integration (IMPLEMENTED)**
- **Real-time trash talk**: AI generates contextual psychological warfare messages
- **Two-way interaction**: Players can engage in trash talk with the AI
- **Game state awareness**: AI responses consider current game situation
- **Personality consistency**: Maintains arrogant, manipulative character
- **Ollama integration**: Direct connection to Gemma3-4B-GPU for authentic responses

### **Trash Talk Triggers (IMPLEMENTED)**
- **Player moves**: Mock predictable strategies and choices
- **Target burns**: Celebrate psychological victories over the player
- **Definition rejections**: Increase pressure and frustration
- **Win/Loss moments**: Appropriate gloating or grudging respect
- **Pattern detection**: Call out repetitive player behavior
- **Confidence exploitation**: Detect and exploit player overconfidence

### **Psychological Pressure Mechanics (IMPLEMENTED)**
- **Progressive difficulty**: Game becomes more challenging as it progresses
- **Strategic misdirection**: AI hints at false strategies
- **Emotional manipulation**: Designed to trigger frustration and determination
- **False confidence building**: AI occasionally accepts borderline definitions to build false hope
- **The "rug pull"**: Strategic moments where AI reveals the true difficulty

---

## 📊 **Difficulty Scaling (IMPLEMENTED)**

| Setting | MAX_STEPS | Word Count Rule | Common Word Reuse | Reveal Freq | Burn Freq |
|---------|-----------|----------------|-------------------|-------------|-----------|
| **Easy** | 30 | Fixed countdown (10→1) | Unlimited | Every 2 turns | Every 8 turns |
| **Medium** | 25 | Strictly shorter | 5 uses each | Every 3 turns | Every 6 turns |
| **Hard** | 20 | Aggressive reduction | No reuse | Every 4 turns | Every 5 turns |

---

## 🔧 **Validation Logic**

### **Turn Validation Function**
```typescript
function validateTurn(
  currentWord: string,
  definition: string,
  gameState: GameState
): ValidationResult {
  
  // Parse definition
  const defWords = definition.toLowerCase().split(/\s+/).filter(w => w.length > 0);
  
  // Rule A: Word count check
  const maxWords = calculateMaxWords(gameState.turn, gameState.difficulty, gameState.lastDefLength);
  if (defWords.length > maxWords) {
    return { valid: false, reason: "TooLong", maxAllowed: maxWords };
  }
  
  // Rule B: Word reuse check
  const reuseCheck = checkWordReuse(defWords, gameState.usedWords, gameState.difficulty);
  if (!reuseCheck.valid) {
    return { valid: false, reason: "WordReuse", details: reuseCheck.reason };
  }
  
  // Rule C: Circular definition check
  if (isCircular(currentWord, definition)) {
    return { valid: false, reason: "Circular" };
  }
  
  // Rule D: Basic sanity check
  if (!isLogicalDefinition(currentWord, definition)) {
    return { valid: false, reason: "Invalid" };
  }
  
  return { valid: true };
}
```

### **Win/Loss Check Function**
```typescript
function checkGameEnd(
  definition: string,
  gameState: GameState
): GameResult {
  
  // Win check
  if (checkWin(definition, gameState.activeTargets)) {
    return { status: "WIN", reason: "TargetReached" };
  }
  
  // Loss checks
  if (gameState.turn > gameState.maxSteps) {
    return { status: "LOSE", reason: "StepLimit" };
  }
  
  if (gameState.consecutiveRejections >= 3) {
    return { status: "LOSE", reason: "RuleViolations" };
  }
  
  return { status: "CONTINUE" };
}
```

---

## 🎮 **Game Flow Example**

### **Medium Difficulty Game**
```
SETUP:
- Targets: ["innovation", "ecosystem", "friction"] 
- Revealed: ["I _ _ _ _ _ _ _ _ n", "E _ _ _ _ _ _ m", "F _ _ _ _ _ _ n"]
- MAX_STEPS: 25

Turn 1: AI gives "transformer"
        Player: "electrical device that changes voltage" (6 words) ✅
        Used words: [electrical, device, that, changes, voltage]

Turn 2: AI gives "voltage" (from player's definition - 80% rule)
        Player: "electrical force measurement" (3 words) ✅
        Used words: [electrical, device, that, changes, voltage, force, measurement]

Turn 3: AI gives "measurement" 
        Targets reveal: ["I n _ _ _ _ _ _ _ n", "E c _ _ _ _ _ _ m", "F r _ _ _ _ _ n"]
        Player: "quantified assessment" (2 words) ✅

Turn 6: More revelation: ["I n n o _ _ _ _ _ n", "E c o _ _ _ _ _ m", "F r i _ _ _ _ n"]

Turn 9: AI burns "friction" → Target removed!
        Remaining: ["I n n o v _ _ _ _ n", "E c o s _ _ _ _ m"]

Turn 15: Player defines "creative breakthrough innovation"
         Contains "innovation" → WIN! 🎉
```

---

## 🚀 **Implementation Checklist**

### **Phase 1: Core Validation**
- [ ] Implement word count rules per difficulty
- [ ] Add common words allowance system
- [ ] Update word reuse detection
- [ ] Add three-strike rejection system

### **Phase 2: Target System**
- [ ] Implement progressive target revelation
- [ ] Add strategic target burning
- [ ] Create target display UI

### **Phase 3: AI Strategy**
- [ ] Implement 80/20 word selection rule
- [ ] Add strategic letter revelation logic
- [ ] Prevent AI from giving target words

### **Phase 4: UX Polish**
- [ ] Show revealed target patterns
- [ ] Display word count limits
- [ ] Add common word usage indicators
- [ ] Clear win/loss feedback

---

## 🎯 **Success Metrics**

### **Fairness Indicators**
- Win rate should be 15-25% for Medium difficulty
- No wins before turn 3 (prevents trivial victories)
- Clear correlation between player skill and success

### **Engagement Metrics**
- Average game length: 15-20 turns
- Player understanding of loss reasons: >90%
- Replay rate after losses: >60%

---

## 🔍 **Edge Cases & Special Scenarios**

### **Edge Case 1: AI Gives Target on Turn 1**
**Prevention**: AI is explicitly forbidden from selecting target words
**Fallback**: If somehow occurs, require minimum 3 turns before win is valid

### **Edge Case 2: Player Down to 1 Word**
**Easy Mode**: Player can use 1 word indefinitely
**Medium/Hard**: Game becomes nearly impossible - this is intentional pressure

### **Edge Case 3: All Targets Burned**
**Prevention**: AI must maintain at least 1 target until turn (MAX_STEPS - 5)
**Fallback**: If occurs, player automatically loses with reason "NoTargetsRemaining"

### **Edge Case 4: Common Word Limit Reached**
**Behavior**: Treat as regular word reuse violation
**UX**: Show clear message "Common word 'the' limit reached (5/5 uses)"

### **Edge Case 5: AI Can't Find Valid Word**
**80% Rule Fallback**: If no words available from player's definition, AI uses wildcard
**Ultimate Fallback**: AI selects from predefined word pool

---

## 🧠 **AI Game Master Behavior**

### **Strategic Decision Making**
```typescript
interface AIStrategy {
  // Word selection priorities
  wordSelectionStrategy: {
    fromPlayerDefinition: 0.8;  // 80% rule
    wildcardStrategic: 0.15;    // Strategic interference
    wildcardRandom: 0.05;       // Pure unpredictability
  };

  // Target management
  targetStrategy: {
    revealLetters: "strategic";  // Vowels first, then key consonants
    burnTiming: "pressure";      // Burn when player gets close
    burnSelection: "threatening"; // Burn most accessible targets
  };

  // Difficulty adaptation
  adaptiveStrategy: {
    easyMode: "helpful";         // Guide player toward targets
    mediumMode: "balanced";      // Fair but challenging
    hardMode: "ruthless";        // Maximum interference
  };
}
```

### **AI Constraints (Fairness Rules)**
1. **Must maintain winnable path** until final 5 turns
2. **Cannot select target words** as challenge words
3. **Must follow 80/20 rule** for word selection
4. **Cannot burn all targets** simultaneously
5. **Must reveal letters** according to schedule

---

*This document serves as the definitive specification for DEFEATER.AI MVP core mechanics. All implementation should reference this document for consistency and completeness.*

**Version**: 1.0.0
**Last Updated**: 2025-01-16
**Status**: Ready for Implementation

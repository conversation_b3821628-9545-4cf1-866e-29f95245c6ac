# Layout System Migration Guide

## Overview
DEFEATER.AI is transitioning from a legacy container-based layout system to a modern spatial design system. This document tracks the migration process and ensures clean code management.

## Layout System Versions

### 🔴 **Legacy System (v1.0) - DEPRECATED**
**Status:** ⚠️ DEPRECATED - Will be removed in next phase
**Pattern:** Container-based, margin-driven spacing
**Identifier:** Components using `.card`, `.mb-*`, utility classes

#### Legacy Components (To be migrated/removed):
- `components/GameBoard.tsx` - HYBRID (partially migrated)
- `components/GameInput.tsx` - LEGACY
- `components/GameStatus.tsx` - LEGACY  
- `components/DefinitionHistory.tsx` - LEGACY
- `components/ValidationFeedback.tsx` - LEGACY
- `components/WordsLeftCounter.tsx` - LEGACY
- `components/CommonWordTracker.tsx` - LEGACY
- `components/TargetDisplay.tsx` - LEGACY
- `components/GameStats.tsx` - LEGACY
- `components/DevPanel.tsx` - LEGACY
- `components/PostGameAnalysis.tsx` - LEGACY

#### Legacy CSS Patterns:
```css
/* DEPRECATED - Do not use in new code */
.card, .card-elevated, .card-neon
.mb-6, .mb-8, .mb-12
.text-center, .flex, .gap-4 (Tailwind utilities)
```

### 🟢 **Spatial System (v2.0) - CURRENT**
**Status:** ✅ ACTIVE - Use for all new development
**Pattern:** Gap-based, natural content flow
**Identifier:** Components using `.spatial-*`, CSS custom properties

#### Spatial Components (Ready for production):
- `components/layout/GameLayout.tsx` - SPATIAL ✅
- `components/game/GameFocus.tsx` - SPATIAL ✅
- `components/ui/Typography.tsx` - SPATIAL ✅

#### Spatial CSS Patterns:
```css
/* CURRENT - Use these patterns */
.spatial-layout, .content-flow
.spatial-section, .spatial-focus
.floating-element
gap: var(--space-*) /* Instead of margins */
```

## Migration Strategy

### Phase 1: Foundation ✅ COMPLETE
- [x] Create spatial layout components
- [x] Implement design system
- [x] Build test pages for validation

### Phase 2: Component Migration 🔄 IN PROGRESS
- [ ] Mark legacy components as deprecated
- [ ] Create spatial versions of core components
- [ ] Implement gradual migration

### Phase 3: Integration 📋 PLANNED
- [ ] Replace legacy GameBoard with spatial version
- [ ] Update main app to use spatial system
- [ ] Remove deprecated components

### Phase 4: Cleanup 📋 PLANNED
- [ ] Remove all legacy CSS
- [ ] Delete deprecated files
- [ ] Validate clean codebase

## Naming Conventions

### Component Naming:
- **Legacy:** `GameBoard.tsx` (no suffix)
- **Spatial:** `GameLayout.tsx`, `GameFocus.tsx` (descriptive names)
- **Deprecated:** Add `@deprecated` JSDoc comment

### CSS Class Naming:
- **Legacy:** `.card`, `.mb-6` (utility-based)
- **Spatial:** `.spatial-layout`, `.content-flow` (semantic)
- **Variables:** `var(--space-*)` (design system)

## Dead Code Detection

### Files to Remove After Migration:
1. Legacy component files (after spatial replacements)
2. Unused CSS classes in globals.css
3. Redundant test pages
4. Old documentation

### Import Cleanup:
- Remove unused Tailwind utility imports
- Clean up component imports in pages
- Remove deprecated design system variables

## Validation Checklist

### Before Removing Legacy Code:
- [ ] All functionality replicated in spatial system
- [ ] No broken imports or references
- [ ] Test pages validate new system works
- [ ] Performance is maintained or improved

### After Migration:
- [ ] No `.card` classes remain
- [ ] No margin-based spacing (`.mb-*`)
- [ ] All components use design system variables
- [ ] Responsive behavior works correctly
- [ ] Accessibility is maintained

## Dead Code Analysis Results

**🔍 Automated Detection Completed:** `node scripts/dead-code-detector.js`

### CSS Classes Status:
- **Total Checked:** 12 legacy patterns
- **Legacy In Use:** 9 classes (need spatial replacements)
- **Unused:** 3 classes (safe to remove immediately)

#### ⚠️ Legacy CSS Still Active:
- `.card` - 27 usages
- `.card-elevated` - 3 usages
- `.mb-6` - 9 usages
- `.mb-8` - 4 usages
- `.mt-4` - 6 usages
- `.mt-6` - 3 usages
- `.text-center` - 27 usages
- `.flex` - 46 usages
- `.gap-4` - 14 usages

#### ✅ Safe to Remove:
- `.card-neon` - Unused
- `.mb-12` - Unused
- `.mt-8` - Unused

### Components Status:
- **Total Checked:** 11 legacy components
- **Legacy In Use:** 11 components (all need migration)
- **Unused:** 0 components

#### ⚠️ All Legacy Components Active:
All 11 legacy components are currently in use and require spatial replacements before removal.

## Current Status

**Legacy System:** 11 components marked for deprecation ✅
**Spatial System:** 3 components ready for production ✅
**Dead Code Detection:** Automated system implemented ✅
**Migration Progress:** ~25% complete

**Next Steps:**
1. ✅ Mark legacy components as deprecated
2. ✅ Implement dead code detection system
3. 🔄 Remove 3 unused CSS classes immediately
4. 📋 Create spatial versions of core components
5. 📋 Test integration with main app
6. 📋 Begin systematic replacement

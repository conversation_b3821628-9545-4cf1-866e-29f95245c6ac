/**
 * Typography Components - Bold & Confident (v2.0 Spatial Design)
 *
 * 🎯 OVERCONFIDENT TYPOGRAPHY SYSTEM
 *
 * Bold, spacious, and commanding typography that demands attention.
 * Every text element is designed to be confident and highly visible.
 * Inspired by the commanding presence of "TRANSFORMER" - no timid fonts!
 *
 * Design Principles:
 * - Bold weights as default (minimum 500)
 * - Generous sizing for maximum impact
 * - Spacious line heights for breathing room
 * - High contrast for accessibility
 * - Confident letter spacing
 *
 * @version 2.0 - Spatial Design System
 * @see docs/LAYOUT_SYSTEM_MIGRATION.md
 */

import React from 'react';

// Base text component props
interface BaseTextProps {
  children: React.ReactNode;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
  style?: React.CSSProperties;
  [key: string]: any; // Allow any additional props
}

// Hero Text - COMMANDING PRESENCE like "TRANSFORMER" (64px+)
export const HeroText: React.FC<BaseTextProps> = ({
  children,
  className = '',
  as: Component = 'h1',
  style = {},
  ...props
}) => (
  <Component
    className={`hero-text ${className}`}
    style={style}
    {...props}
  >
    {children}
    <style jsx>{`
      .hero-text {
        font-size: var(--text-5xl);
        font-weight: var(--font-extrabold);
        line-height: var(--leading-tight);
        color: var(--text-primary);
        text-shadow: var(--text-shadow-commanding);
        letter-spacing: -0.025em;
        margin: 0;
        text-transform: uppercase;
        font-stretch: expanded;
      }

      /* Responsive scaling maintains impact */
      @media (max-width: 768px) {
        .hero-text {
          font-size: var(--text-4xl);
          letter-spacing: -0.02em;
        }
      }

      @media (max-width: 480px) {
        .hero-text {
          font-size: var(--text-3xl);
          letter-spacing: -0.015em;
        }
      }
    `}</style>
  </Component>
);

// Primary Text - BOLD MAIN ELEMENTS (32px)
export const PrimaryText: React.FC<BaseTextProps> = ({
  children,
  className = '',
  as: Component = 'p',
  style = {},
  ...props
}) => (
  <Component
    className={`primary-text ${className}`}
    style={style}
    {...props}
  >
    {children}
    <style jsx>{`
      .primary-text {
        font-size: var(--text-2xl);
        font-weight: var(--font-bold);
        line-height: var(--leading-snug);
        color: var(--text-primary);
        text-shadow: var(--text-shadow-strong);
        letter-spacing: -0.01em;
        margin: 0;
      }

      @media (max-width: 768px) {
        .primary-text {
          font-size: var(--text-xl);
        }
      }
    `}</style>
  </Component>
);

// Secondary Text - CONFIDENT SUPPORTING INFO (22px)
export const SecondaryText: React.FC<BaseTextProps> = ({
  children,
  className = '',
  as: Component = 'p',
  style = {},
  ...props
}) => (
  <Component
    className={`secondary-text ${className}`}
    style={style}
    {...props}
  >
    {children}
    <style jsx>{`
      .secondary-text {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        line-height: var(--leading-normal);
        color: var(--text-secondary);
        text-shadow: var(--text-shadow-medium);
        letter-spacing: -0.005em;
        margin: 0;
      }
    `}</style>
  </Component>
);

// Body Text - READABLE & CONFIDENT (18px)
export const BodyText: React.FC<BaseTextProps> = ({
  children,
  className = '',
  as: Component = 'p',
  style = {},
  ...props
}) => (
  <Component
    className={`body-text ${className}`}
    style={style}
    {...props}
  >
    {children}
    <style jsx>{`
      .body-text {
        font-size: var(--text-base);
        font-weight: var(--font-medium);
        line-height: var(--leading-relaxed);
        color: var(--text-secondary);
        margin: 0;
      }
    `}</style>
  </Component>
);

// Small Text - STILL VISIBLE & CONFIDENT (16px)
export const SmallText: React.FC<BaseTextProps> = ({
  children,
  className = '',
  as: Component = 'span',
  style = {},
  ...props
}) => (
  <Component
    className={`small-text ${className}`}
    style={style}
    {...props}
  >
    {children}
    <style jsx>{`
      .small-text {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        line-height: var(--leading-normal);
        color: var(--text-muted);
        margin: 0;
      }
    `}</style>
  </Component>
);

// Muted Text - Less important information
export const MutedText: React.FC<BaseTextProps> = ({
  children,
  className = '',
  as: Component = 'span',
  style = {},
  ...props
}) => (
  <Component
    className={`muted-text ${className}`}
    style={style}
    {...props}
  >
    {children}
    <style jsx>{`
      .muted-text {
        color: var(--text-muted);
        margin: 0;
      }
    `}</style>
  </Component>
);

// Accent Text - Highlighted information
export const AccentText: React.FC<BaseTextProps> = ({
  children,
  className = '',
  as: Component = 'span',
  style = {},
  ...props
}) => (
  <Component
    className={`accent-text ${className}`}
    style={style}
    {...props}
  >
    {children}
    <style jsx>{`
      .accent-text {
        color: var(--accent-cyan);
        font-weight: var(--font-medium);
        margin: 0;
      }
    `}</style>
  </Component>
);

// Imposing Text - COMMANDING EMPHASIS with shadows
export const ImposingText: React.FC<BaseTextProps> = ({
  children,
  className = '',
  as: Component = 'span',
  style = {},
  ...props
}) => (
  <Component
    className={`imposing-text ${className}`}
    style={style}
    {...props}
  >
    {children}
    <style jsx>{`
      .imposing-text {
        color: var(--accent-cyan);
        font-weight: var(--font-bold);
        text-shadow: var(--text-shadow-hero);
        letter-spacing: -0.01em;
        margin: 0;
      }
    `}</style>
  </Component>
);

// Status Text - For game states
interface StatusTextProps extends BaseTextProps {
  status: 'success' | 'error' | 'warning' | 'info' | 'processing';
}

export const StatusText: React.FC<StatusTextProps> = ({
  children,
  status,
  className = '',
  as: Component = 'span',
  style = {},
  ...props
}) => {
  const statusClasses = [
    'status-text',
    `status-text--${status}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <Component
      className={statusClasses}
      style={style}
      {...props}
    >
      {children}
    </Component>
  );
};

// Monospace Text - For code or patterns
export const MonoText: React.FC<BaseTextProps> = ({
  children,
  className = '',
  as: Component = 'code',
  style = {},
  ...props
}) => {
  const monoClasses = [
    'mono-text',
    className
  ].filter(Boolean).join(' ');

  return (
    <Component
      className={monoClasses}
      style={style}
      {...props}
    >
      {children}
    </Component>
  );
};

// Export all components
export {
  HeroText as Hero,
  PrimaryText as Primary,
  SecondaryText as Secondary,
  BodyText as Body,
  SmallText as Small,
  MutedText as Muted,
  AccentText as Accent,
  ImposingText as Imposing,
  StatusText as Status,
  MonoText as Mono
};

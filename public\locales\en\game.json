{"title": "DEFEATER.AI", "subtitle": "The Dark Souls of puzzle games", "difficulty": {"title": "Select Difficulty", "easy": "Easy", "medium": "Medium", "hard": "Hard", "expert": "Expert", "description": {"easy": "30 steps, forgiving word limits", "medium": "25 steps, balanced challenge", "hard": "20 steps, strict word limits", "expert": "15 steps, brutal precision required"}}, "gameState": {"waiting": "Waiting for your move", "thinking": "AI is thinking...", "won": "Victory!", "lost": "Defeated", "paused": "Game Paused", "loading": "Loading game...", "error": "Game Error"}, "targets": {"title": "Target Words", "remaining": "Remaining targets", "completed": "Completed targets", "burned": "Burned targets", "revealed": "Letters revealed", "hidden": "Letters hidden", "progress": "Target progress", "hint": "New letters reveal every 3 steps"}, "input": {"placeholder": "Define the word...", "label": "Your definition", "submit": "Submit Definition", "clear": "Clear", "wordCount": "Words used", "wordsLeft": "Words left", "maxWords": "Maximum words", "tooLong": "Definition too long", "tooShort": "Definition too short", "invalid": "Invalid definition", "required": "Definition required", "hint": "Each definition must be shorter than the previous one"}, "progress": {"step": "Step", "of": "of", "steps": "steps", "remaining": "remaining", "completed": "completed", "percentage": "progress"}, "stats": {"title": "Game Statistics", "currentStep": "Current Step", "maxSteps": "Maximum Steps", "stepsRemaining": "Steps Remaining", "targetsTotal": "Total Targets", "targetsCompleted": "Completed", "targetsBurned": "Burned", "targetsRemaining": "Remaining", "wordsUsed": "Words Used", "wordsLeft": "Words Left", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "score": "Score", "time": "Time", "accuracy": "Accuracy", "efficiency": "Efficiency"}, "history": {"title": "Definition History", "step": "Step {{number}}", "word": "Word", "definition": "Definition", "wordCount": "Word Count", "status": "Status", "accepted": "Accepted", "rejected": "Rejected", "timestamp": "Time", "empty": "No definitions yet", "clear": "Clear History"}, "rules": {"title": "How to Play", "objective": "Objective", "objectiveText": "Define words to reach the hidden target words before the AI burns them all.", "gameplay": "Gameplay", "gameplayRules": ["You'll see a word that needs to be defined", "Write a definition using fewer words than your previous definition", "The AI will choose the next word from your definition", "Target words are revealed progressively (first/last letters + 1 letter every 3 turns)", "Win by including any target word in your definition", "Lose if you exceed the step limit, break rules 3 times, or resign"], "strategy": "Strategy Tips", "strategyTips": ["Plan your word count carefully - each definition must be shorter", "The AI will try to predict and burn your target words", "Use misdirection to confuse the AI", "Watch for patterns in the AI's word choices", "Save shorter definitions for when you're close to targets"], "scoring": "Scoring", "scoringRules": ["Faster completion = higher score", "Fewer steps used = bonus points", "Multiple targets reached = multiplier", "Difficulty level affects final score"]}, "victory": {"title": "VICTORY!", "message": "You defeated the DEFEATER!", "details": "You reached \"{{word}}\" in {{steps}} steps.", "score": "Final Score: {{score}}", "time": "Time: {{time}}", "efficiency": "Efficiency: {{efficiency}}%", "newRecord": "New Personal Record!", "shareText": "I just defeated DEFEATER.AI in {{steps}} steps! Can you do better?", "playAgain": "Challenge Again", "viewAnalysis": "View Analysis"}, "defeat": {"title": "DEFEATED", "message": "The AI has bested you this time.", "details": "You made it {{steps}} steps.", "reason": {"steps": "Exceeded maximum steps", "rejections": "Too many rule violations", "resignation": "<PERSON> resigned", "timeout": "Time limit exceeded", "error": "Game error occurred"}, "encouragement": ["Every defeat teaches valuable lessons", "The AI is learning your patterns", "Try a different strategy next time", "Practice makes perfect", "Even masters lose sometimes"], "tryAgain": "Try Again", "viewAnalysis": "View Analysis"}, "analysis": {"title": "Post-Game Analysis", "overview": "Game Overview", "performance": "Performance Metrics", "timeline": "Game Timeline", "aiStrategy": "AI Strategy Analysis", "playerStrategy": "Your Strategy Analysis", "improvements": "Suggested Improvements", "patterns": "Pattern Recognition", "efficiency": "Efficiency Analysis", "close": "Close Analysis"}, "errors": {"networkError": "Network connection lost", "serverError": "Server error occurred", "gameError": "Game error occurred", "invalidMove": "Invalid move", "timeout": "Request timed out", "unknown": "Unknown error occurred", "retry": "Retry", "reportBug": "Report Bug"}, "ai": {"thinking": "AI is analyzing your move...", "response": "AI Response", "strategy": "AI Strategy", "confidence": "AI Confidence", "prediction": "AI Prediction", "challenge": "AI Challenge", "burn": "Target Burned!", "burnMessage": "The AI burned target: {{word}}", "taunt": {"confident": "Predictable move...", "surprised": "Interesting choice!", "impressed": "Well played!", "dismissive": "Is that all you've got?", "warning": "I'm one step ahead...", "victory": "Victory is mine!"}}, "chat": {"title": "AI Trash Talk", "placeholder": "Type your response...", "send": "Send", "aiTyping": "AI is typing...", "expand": "Expand Chat", "collapse": "Collapse Chat", "clear": "Clear Chat", "mute": "Mute AI", "unmute": "Unmute AI"}, "settings": {"title": "Game Settings", "difficulty": "Difficulty Level", "language": "Language", "theme": "Theme", "sound": "Sound Effects", "music": "Background Music", "animations": "Animations", "autoSave": "Auto Save", "hints": "Show Hints", "timer": "Show Timer", "aiPersonality": "AI Personality", "accessibility": "Accessibility Options"}, "tutorial": {"title": "Tutorial", "welcome": "Welcome to DEFEATER.AI!", "step1": "This is your first word to define", "step2": "Write a definition using the input below", "step3": "The AI will choose the next word from your definition", "step4": "Try to reach the target words shown above", "step5": "Each definition must be shorter than the previous one", "complete": "Tutorial Complete!", "skip": "<PERSON><PERSON>", "next": "Next", "previous": "Previous", "finish": "Finish Tutorial"}}
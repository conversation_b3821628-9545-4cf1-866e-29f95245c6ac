# Week 2: Component Dependency Analysis

> **Status**: IN PROGRESS  
> **Date**: 2025-06-18  
> **Migration Phase**: Week 2 Day 1-2 of 7-week Technical Debt Migration  
> **Objective**: Map legacy component dependencies and determine safe migration order  

## 🎯 **Analysis Objectives**

### **Primary Goals**
- Map all dependencies between legacy components
- Identify safe migration order (leaf components first)
- Document prop flows and state dependencies
- Plan coordinated migration strategy

---

## 📊 **Legacy Component Dependency Map**

### **Component Hierarchy Analysis**

```
GameBoard.tsx (ROOT - 844 lines)
├── PostGameAnalysis.tsx (DEPENDS ON GameStats)
│   └── GameStats.tsx (LEAF COMPONENT)
├── DevPanel.tsx (LEAF COMPONENT)
├── GameRules.tsx (LEAF COMPONENT)
├── DifficultySelector.tsx (LEAF COMPONENT)
└── LoadingSpinner.tsx (LEAF COMPONENT)

NEW SPATIAL COMPONENTS (Already migrated)
├── GameFocus ✅
├── CurrentChallenge ✅
├── DefinitionInput ✅
├── TargetRevelationStrip ✅
├── CollapsibleSidePanel ✅
├── GameStatsPanel ✅ (replaces GameStats)
├── DefinitionHistoryPanel ✅
└── FloatingChatDialog ✅
```

---

## 🔗 **Detailed Dependency Analysis**

### **1. GameStats.tsx (LEAF - Priority 1)**

#### **Dependencies**:
- **Imports**: `GameState` type, `getRevealedTargets` utility
- **Props**: `gameState: GameState`, `className?: string`, `showDetailed?: boolean`
- **External Dependencies**: None (pure component)

#### **Used By**:
- **PostGameAnalysis.tsx**: Line 78-82
  ```typescript
  <GameStats 
    gameState={gameState} 
    showDetailed={true}
    className="post-game-stats"
  />
  ```

#### **Migration Status**:
- ✅ **Replacement Available**: `GameStatsPanel` already exists
- ✅ **No Circular Dependencies**: Safe to migrate first
- ✅ **Pure Component**: No side effects or complex state

#### **Migration Strategy**:
```typescript
// BEFORE (GameStats.tsx)
<GameStats 
  gameState={gameState} 
  showDetailed={true}
  className="post-game-stats"
/>

// AFTER (GameStatsPanel)
<GameStatsPanel 
  gameState={gameState} 
  variant="detailed"
  className="post-game-stats"
/>
```

---

### **2. PostGameAnalysis.tsx (DEPENDS ON GameStats - Priority 2)**

#### **Dependencies**:
- **Imports**: `GameState` type, `GameStats` component
- **Props**: `gameState: GameState`, `onNewGame: () => void`, `onClose: () => void`
- **Internal Dependencies**: `GameStats.tsx` (line 3, used line 78-82)

#### **Used By**:
- **GameBoard.tsx**: Lines 37, conditionally rendered

#### **State Management**:
- **Local State**: `activeTab` for tab navigation
- **No Global State**: Self-contained component

#### **Migration Blockers**:
- ❌ **Depends on GameStats**: Must migrate GameStats first
- ✅ **No Other Dependencies**: Ready after GameStats migration

#### **Migration Strategy**:
1. **Phase 1**: Replace GameStats with GameStatsPanel
2. **Phase 2**: Migrate to spatial design modal system
3. **Phase 3**: Update styling to design system

---

### **3. DevPanel.tsx (LEAF - Priority 1)**

#### **Dependencies**:
- **Imports**: `GameState`, `AIResponse` types, `analyzeWordSimilarity` utility
- **Props**: `gameState: GameState`, `lastAIResponse?: AIResponse`, `isVisible: boolean`, `onToggle: () => void`
- **External Dependencies**: None (pure component)

#### **Used By**:
- **GameBoard.tsx**: Lines 34, conditionally rendered

#### **State Management**:
- **Local State**: `activeTab` for tab navigation
- **No Global State**: Self-contained component

#### **Migration Status**:
- ✅ **No Dependencies**: Safe to migrate independently
- ✅ **Development Tool**: Low risk migration
- ✅ **Self-Contained**: No impact on game logic

#### **Migration Strategy**:
- Integrate with spatial design system
- Update styling to design system classes
- Maintain debugging functionality

---

### **4. GameBoard.tsx (ROOT - Priority 4)**

#### **Dependencies**:
- **Legacy Components**: GameRules, DevPanel, LoadingSpinner, DifficultySelector, PostGameAnalysis
- **New Components**: 9 spatial design components already integrated
- **State Management**: 15+ useState hooks with complex interdependencies

#### **Migration Blockers**:
- ❌ **Complex State**: 15+ state variables need extraction
- ❌ **Mixed Architecture**: Hybrid legacy/spatial system
- ❌ **844 Lines**: Requires systematic decomposition

#### **Migration Strategy**:
1. **Week 3**: Migrate child components (GameStats, PostGameAnalysis, DevPanel)
2. **Week 4**: Extract state to custom hooks
3. **Week 4**: Decompose into smaller components
4. **Week 5-6**: Complete migration to spatial system

---

## 📋 **Migration Order & Timeline**

### **Phase 1: Leaf Components (Week 3)**

#### **Priority 1 - Independent Components**:
1. **GameStats.tsx** → GameStatsPanel (Day 1-2)
   - ✅ No dependencies
   - ✅ Replacement exists
   - ✅ Used by PostGameAnalysis only

2. **DevPanel.tsx** → Spatial Integration (Day 5)
   - ✅ No dependencies
   - ✅ Development tool (low risk)
   - ✅ Self-contained

#### **Priority 2 - Dependent Components**:
3. **PostGameAnalysis.tsx** → Spatial Modal (Day 3-4)
   - ⚠️ Depends on GameStats (migrate after Priority 1)
   - ✅ No other dependencies
   - ✅ Modal system ready

### **Phase 2: Root Component (Week 4-6)**

#### **Priority 3 - State Extraction**:
4. **GameBoard.tsx State** → Custom Hooks (Week 4)
   - Extract game state management
   - Extract UI state management
   - Extract performance monitoring

#### **Priority 4 - Component Decomposition**:
5. **GameBoard.tsx Components** → Spatial System (Week 4-6)
   - Decompose into smaller components
   - Migrate to spatial design patterns
   - Optimize performance

---

## 🛡️ **Risk Assessment**

### **Low Risk Migrations**:
- ✅ **GameStats.tsx**: Pure component, replacement exists
- ✅ **DevPanel.tsx**: Development tool, self-contained

### **Medium Risk Migrations**:
- ⚠️ **PostGameAnalysis.tsx**: Modal system integration needed

### **High Risk Migrations**:
- ❌ **GameBoard.tsx**: Complex state, 844 lines, hybrid architecture

---

## 📈 **Success Metrics**

### **Week 3 Targets**:
- ✅ GameStats.tsx → GameStatsPanel migration complete
- ✅ PostGameAnalysis.tsx spatial design migration
- ✅ DevPanel.tsx integration updated
- ✅ All tests passing with improved coverage

### **Validation Criteria**:
- **Functionality**: No regression in game features
- **Performance**: No degradation in render times
- **Accessibility**: Maintain WCAG AA compliance
- **Testing**: All tests passing after each migration

---

## 🔄 **Next Steps**

### **Day 3-4: State Management Analysis**
1. **Extract GameBoard State**: Analyze 15+ state variables
2. **Design Custom Hooks**: Plan useGameState, useUIState
3. **Plan Context Providers**: Consolidate state management
4. **Design State Batching**: Optimize performance

### **Day 5: Performance Baseline**
1. **Measure Current Performance**: Establish baselines
2. **Create Migration Timeline**: Detailed week-by-week plan
3. **Validate Migration Order**: Confirm dependency analysis
4. **Prepare Week 3**: Ready for component migration

---

## 🧠 **State Management Analysis (Day 3-4)**

### **GameBoard.tsx State Complexity**

#### **Current State Variables (15+ useState hooks)**:
```typescript
// Core Game State
const [gameState, setGameState] = useState<GameState | null>(initialGameState || null);
const [uiState, setUIState] = useState<UIState>({...});
const [error, setError] = useState<string>('');
const [lastAIResponse, setLastAIResponse] = useState<AIResponse | undefined>();

// UI Control State
const [showDevPanel, setShowDevPanel] = useState<boolean>(process.env.NODE_ENV === 'development');
const [selectedDifficulty, setSelectedDifficulty] = useState<DifficultyLevel>('medium');
const [showDifficultySelector, setShowDifficultySelector] = useState<boolean>(!initialGameState);
const [showValidationFeedback, setShowValidationFeedback] = useState<boolean>(true);
const [showPostGameAnalysis, setShowPostGameAnalysis] = useState<boolean>(false);
const [showGameStats, setShowGameStats] = useState<boolean>(true);

// Spatial Design State
const [showSidePanel, setShowSidePanel] = useState(false);
const [sidePanelTab, setSidePanelTab] = useState<'stats' | 'history' | 'rules'>('stats');

// Development State
const [showPerformanceDashboard, setShowPerformanceDashboard] = useState(
  process.env.NODE_ENV === 'development'
);

// Refs for Performance
const submitTimeoutRef = useRef<NodeJS.Timeout>();
const isSubmittingRef = useRef<boolean>(false);
const animationTimeoutRef = useRef<NodeJS.Timeout>();
```

#### **State Interdependencies**:
1. **gameState** → affects: UI rendering, animations, AI responses
2. **uiState** → affects: loading states, animations, input handling
3. **error** → affects: user feedback, validation display
4. **showSidePanel + sidePanelTab** → affects: panel content and visibility
5. **showDifficultySelector** → affects: game initialization flow

### **Target State Architecture**

#### **Custom Hooks Design**:
```typescript
// hooks/useGameState.ts
export function useGameState(initialState?: GameState) {
  const [gameState, setGameState] = useState<GameState | null>(initialState || null);
  const [lastAIResponse, setLastAIResponse] = useState<AIResponse | undefined>();
  const [error, setError] = useState<string>('');

  const updateGameState = useCallback((updates: Partial<GameState>) => {
    setGameState(prev => prev ? { ...prev, ...updates } : null);
  }, []);

  const resetGameState = useCallback(() => {
    setGameState(null);
    setLastAIResponse(undefined);
    setError('');
  }, []);

  return {
    gameState,
    lastAIResponse,
    error,
    updateGameState,
    setLastAIResponse,
    setError,
    resetGameState
  };
}

// hooks/useUIState.ts
export function useUIState() {
  const [uiState, setUIState] = useState<UIState>({
    isLoading: false,
    showRules: false,
    showHistory: true,
    animationState: 'idle',
    inputValue: '',
    wordCount: 0
  });

  const updateUIState = useCallback((updates: Partial<UIState>) => {
    setUIState(prev => ({ ...prev, ...updates }));
  }, []);

  const setLoading = useCallback((isLoading: boolean) => {
    updateUIState({ isLoading });
  }, [updateUIState]);

  const setInputValue = useCallback((inputValue: string) => {
    const wordCount = inputValue.trim().split(/\s+/).filter(word => word.length > 0).length;
    updateUIState({ inputValue, wordCount: wordCount || 0 });
  }, [updateUIState]);

  return {
    uiState,
    updateUIState,
    setLoading,
    setInputValue
  };
}

// hooks/useGameControls.ts
export function useGameControls() {
  const [selectedDifficulty, setSelectedDifficulty] = useState<DifficultyLevel>('medium');
  const [showDifficultySelector, setShowDifficultySelector] = useState<boolean>(false);
  const [showValidationFeedback, setShowValidationFeedback] = useState<boolean>(true);
  const [showPostGameAnalysis, setShowPostGameAnalysis] = useState<boolean>(false);

  const resetControls = useCallback(() => {
    setShowDifficultySelector(false);
    setShowPostGameAnalysis(false);
  }, []);

  return {
    selectedDifficulty,
    showDifficultySelector,
    showValidationFeedback,
    showPostGameAnalysis,
    setSelectedDifficulty,
    setShowDifficultySelector,
    setShowValidationFeedback,
    setShowPostGameAnalysis,
    resetControls
  };
}

// hooks/useSpatialLayout.ts
export function useSpatialLayout() {
  const [showSidePanel, setShowSidePanel] = useState(false);
  const [sidePanelTab, setSidePanelTab] = useState<'stats' | 'history' | 'rules'>('stats');

  const toggleSidePanel = useCallback(() => {
    setShowSidePanel(prev => !prev);
  }, []);

  const changeSidePanelTab = useCallback((tab: 'stats' | 'history' | 'rules') => {
    setSidePanelTab(tab);
    if (!showSidePanel) {
      setShowSidePanel(true);
    }
  }, [showSidePanel]);

  return {
    showSidePanel,
    sidePanelTab,
    setShowSidePanel,
    setSidePanelTab,
    toggleSidePanel,
    changeSidePanelTab
  };
}

// hooks/useDevTools.ts
export function useDevTools() {
  const [showDevPanel, setShowDevPanel] = useState<boolean>(
    process.env.NODE_ENV === 'development'
  );
  const [showPerformanceDashboard, setShowPerformanceDashboard] = useState(
    process.env.NODE_ENV === 'development'
  );

  const toggleDevPanel = useCallback(() => {
    setShowDevPanel(prev => !prev);
  }, []);

  return {
    showDevPanel,
    showPerformanceDashboard,
    setShowDevPanel,
    setShowPerformanceDashboard,
    toggleDevPanel
  };
}
```

#### **Refactored GameBoard State Usage**:
```typescript
// GameBoard.tsx (after state extraction)
const GameBoard: React.FC<GameBoardProps> = ({ initialGameState }) => {
  // Custom hooks for state management
  const { gameState, lastAIResponse, error, updateGameState, setLastAIResponse, setError } = useGameState(initialGameState);
  const { uiState, updateUIState, setLoading, setInputValue } = useUIState();
  const { selectedDifficulty, showDifficultySelector, showPostGameAnalysis, setSelectedDifficulty, setShowDifficultySelector, setShowPostGameAnalysis } = useGameControls();
  const { showSidePanel, sidePanelTab, toggleSidePanel, changeSidePanelTab } = useSpatialLayout();
  const { showDevPanel, toggleDevPanel } = useDevTools();

  // Performance monitoring
  const { trackRender } = usePerformanceMonitoring('GameBoard');

  // Refs for performance optimization
  const submitTimeoutRef = useRef<NodeJS.Timeout>();
  const isSubmittingRef = useRef<boolean>(false);
  const animationTimeoutRef = useRef<NodeJS.Timeout>();

  // ... rest of component logic
};
```

### **State Migration Benefits**:
- ✅ **Reduced Complexity**: From 15+ useState to 5 custom hooks
- ✅ **Better Organization**: Logical grouping of related state
- ✅ **Improved Testability**: Each hook can be tested independently
- ✅ **Enhanced Reusability**: Hooks can be used in other components
- ✅ **Performance Optimization**: Reduced re-renders through targeted updates

---

*This dependency analysis provides the foundation for safe, systematic migration of legacy components while maintaining system stability and functionality.*
